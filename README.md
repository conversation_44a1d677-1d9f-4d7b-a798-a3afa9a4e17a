# RUH AI Developer Platform Frontend

The RUH AI Developer Platform is a comprehensive web application that allows developers to create, manage, and deploy AI agents, workflows, and tools. This platform provides a unified interface for monitoring and managing your AI infrastructure.

## Project Overview

The Developer Platform enables users to:

- Create and manage AI agents with customizable capabilities
- Design and deploy workflows for automation
- Monitor agent and workflow performance
- Manage API keys and authentication
- Configure webhooks for event notifications
- Access comprehensive documentation

## Technology Stack

- **Framework**: [Next.js 15](https://nextjs.org/) with App Router
- **UI Components**: [shadcn/ui](https://ui.shadcn.com/) with [TailwindCSS](https://tailwindcss.com/)
- **State Management**: [Zustand](https://github.com/pmndrs/zustand)
- **API Communication**: [Axios](https://axios-http.com/) and [React Query](https://tanstack.com/query/latest)
- **Form Handling**: [React Hook Form](https://react-hook-form.com/) with [Zod](https://zod.dev/) validation
- **Notifications**: [Sonner](https://sonner.emilkowal.ski/) for toast notifications
- **Styling**: [TailwindCSS](https://tailwindcss.com/) with dark/light theme support via [next-themes](https://github.com/pacocoursey/next-themes)
- **Icons**: [Lucide React](https://lucide.dev/guide/packages/lucide-react)

## Application Routes

### Main Routes

- `/dashboard/overview` - Platform overview and analytics
- `/dashboard/agents` - AI agent management
- `/dashboard/workflows` - Workflow management
- `/dashboard/tools` - Tools and MCP servers configuration
- `/dashboard/api-keys` - API key management
- `/dashboard/activity` - Activity monitoring
- `/dashboard/webhooks` - Webhook configuration
- `/dashboard/apps` - Application management
- `/dashboard/documentation` - API documentation
- `/dashboard/settings` - Platform settings
- `/dashboard/profile` - User profile management

### Authentication Routes

- `/auth/login` - User login
- `/auth/register` - User registration
- `/auth/forgot-password` - Password recovery

## Getting Started

First, install the dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

Then, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
NEXT_PUBLIC_API_URL=your_api_url
```

## Project Structure

```
/src
  /app                   # Next.js App Router pages
  /components            # Reusable UI components
    /common              # Common components used across the app
    /ui                  # UI components (from shadcn/ui)
    /providers           # Context providers
  /features              # Feature-specific code
    /agents              # Agent-related code
    /workflows           # Workflow-related code
  /lib                   # Shared utilities and helpers
    /api                 # API client functions
    /hooks               # Custom hooks
    /utils               # Utility functions
    /types               # TypeScript types
  /store                 # Global state management
```

## Deployment

The application can be deployed using Docker:

```bash
# Build the Docker image
docker build -t developer-platform-frontend .

# Run the container
docker run -p 3000:3000 developer-platform-frontend
```

## Learn More

For more information about the technologies used in this project:

- [Next.js Documentation](https://nextjs.org/docs)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Documentation](https://ui.shadcn.com/docs)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [React Query Documentation](https://tanstack.com/query/latest/docs)
