# ruh.ai Developer Platform
# Product Requirements Document (PRD)

## 1. Introduction

### 1.1 Purpose
This document outlines the product requirements for the ruh.ai Developer Platform. The platform aims to provide a comprehensive, intuitive, and powerful environment for developers and administrators to build, manage, monitor, and scale AI-driven applications and services, leveraging ruh.ai's core technologies. It will serve as the central hub for interacting with AI agents, workflows, and Model Context Protocol (MCP) compliant tools. A key principle is that assets (agents, workflows, MCPs) built or acquired within this platform can be seamlessly utilised within the main ruh app and the Ruh. AI Workflow Builder.

### 1.2 Goals
- **Empower Developers:** Enable developers to easily create, configure, and deploy sophisticated AI agents and automated workflows through various means, including building from scratch, importing, or leveraging a marketplace.
- **Simplify Integration:** Provide seamless ways to integrate custom data sources and tools using the open Model Context Protocol (MCP).
- **Enhance Visibility & Control:** Offer robust analytics, logging, and management tools including powerful search and filtering capabilities across agents, workflows, and MCPs, for monitoring performance, usage, and costs.
- **Foster a Community & Ecosystem:** Facilitate discovery, customisation and sharing of agents, workflows, and MCP Tools through a marketplace.
- **Drive Adoption:** Make ruh.ai's advanced AI capabilities accessible and easy to use, thereby increasing platform adoption and user engagement.
- **Ensure Security & Scalability:** Provide a secure and reliable platform that can scale with user needs, including secure management of credentials required for platform components.
- **Cross-Platform Utility:** Ensure that agents and workflows created or configured in the Developer Platform are directly usable in the main Ruh app and the ruh.ai Workflow Builder, fostering a cohesive ecosystem.

### 1.3 Target Audience & User Personas
- **Persona 1: Alex - The AI Application Developer**
  - Role: Software Engineer
  - Needs: Quickly prototype and build AI-powered features. Easily integrate diverse AI models, custom tools, and data sources. Acquire, customise, or build agents and workflows. Monitor application performance and troubleshoot issues. Securely manage API access.
  - Pain Points: Complexity in wiring up different AI services, lack of standardised ways to connect tools, difficulty in monitoring AI component behaviour.
- **Persona 2: Sarah - The Operations Administrator**
  - Role: System Administrator, DevOps Engineer, Platform Manager
  - Needs: Monitor platform usage, credit consumption, and system health. Manage user access (future) and ensure security compliance. Understand resource utilisation. Locate specific agents, workflows, or MCPs for oversight.
  - Pain Points: Lack of centralised visibility into AI resource usage, difficulty in tracking costs, inefficient search for deployed assets.
- **Persona 3: Ben - The Solutions Architect / Consultant**
  - Role: Designs and proposes AI solutions for clients or internal teams
  - Needs: Explore available AI capabilities. Demonstrate proof-of-concepts by leveraging and customising marketplace assets. Understand how components can be combined to solve business problems.
  - Pain Points: Difficulty in quickly assembling and showcasing AI solutions without extensive custom coding.

### 1.4 Success Metrics
- **Activation & Engagement:**
  - Number of agents created/configured per user (built, marketplace, A2A)
  - Number of workflows created/utilised per user (built, marketplace)
  - Number of custom MCP Tools registered
- **Adoption & Usage:**
  - Number of API calls made through the platform
  - Growth in marketplace item usage
  - Number of applications created
  - Usage of search and filter functionalities
- **Satisfaction & Retention:**
  - User satisfaction scores (CSAT, NPS)
  - User retention rate
  - Feature adoption rates
- **Platform Health:**
  - System uptime and reliability
  - Average API response times

## 2. Product Overview

### 2.1 Vision
To be the leading developer-first platform for building, deploying, and managing next-generation AI applications, by providing an intuitive, flexible, and powerful suite of tools that abstract complexity and accelerate innovation, ensuring seamless integration of developed assets across the broader ruh.ai ecosystem.

### 2.2 Key Features (High-Level)
- Centralised Dashboard: Real-time analytics and insights into platform usage and AI component performance.
- AI Agent Creation & Management: Build, acquire, import, and customise agents; powerful search/filter; marketplace integration.
- Workflow Orchestration: Create, acquire, and customise workflows; integrated Workflow Builder; search/filter.
- Model Context Protocol (MCP) Integration: Standardised way to connect AI models to any data source or tool.
- Marketplace: Discover, share, and use pre-built agents, workflows, and MCP Tools.
- Credential Management: Securely store and manage credentials for agents, workflows, and MCP tools.
- Application & API Key Management: Securely manage access to ruh.ai services for various applications.
- Event-Driven Notifications: Webhooks for real-time updates on platform events.
- Comprehensive Logging: Detailed activity logs for auditing, debugging, and monitoring.
- Cross-Platform Utility: Agents and workflows are usable in the main Ruh app and Workflow Builder.

### 2.3 Strategic Fit
The ruh.ai Developer Platform is critical to democratising access to ruh.ai's core AI technologies, supporting the company's strategy of fostering a vibrant ecosystem, driving innovation, and enabling assets to be used across all ruh.ai products.

## 3. User Stories / Use Cases

### Agent Management
- **US-AG-001:** As Alex, I want to create a new AI agent by specifying its name, purpose, and visual avatar, so I can easily identify and organise my agents.
- **US-AG-002:** As Alex, I want to define the core behaviour of my agent by writing a system prompt and selecting a specific AI model, so I can tailor its responses and capabilities.
- **US-AG-003:** As Alex, I want to enhance my agent's abilities by adding pre-built or custom MCP Tools, so it can perform specific actions or access relevant data.
- **US-AG-004:** As Alex, I want to provide my agent with specific knowledge by uploading documents or linking websites, so it can answer questions based on that information.
- **US-AG-005:** As Alex, I want to make my agent available for others to use via a public marketplace, or keep it private for my own applications.
- **US-AG-006:** As Ben, I want to quickly find and add an existing agent from the marketplace to demonstrate its capabilities to a client.
- **US-AG-007:** As Alex, I want to import an existing A2A-compatible agent by providing its URL, so I can integrate it into my ruh.ai environment.
- **US-AG-008:** As Alex, I want to search my list of agents by name or tag and filter by status to quickly find the agent I need.
- **US-AG-009:** As Alex, after adding an agent from the marketplace, I want to customise its system prompt and available tools, so it better fits my use case.
- **US-AG-010:** As Alex, I want to use an agent I built in the Developer Platform directly within the main ruh app.

### Workflow Management
- **US-WF-001:** As Alex, I want to create a new workflow from scratch using the ruh.ai Workflow Builder, connecting different agents and tools, so I can automate a complex business process.
- **US-WF-002:** As Alex, I want to test my workflow within the builder and also open it in a dedicated Workflow Builder instance for debugging.
- **US-WF-003:** As Ben, I want to browse the marketplace for pre-built workflows and add one to my account to accelerate my solution design.
- **US-WF-004:** As Alex, after adding a workflow from the marketplace, I want to modify its steps or change the agents it uses, so I can adapt it to my organization's needs.
- **US-WF-005:** As Alex, I want to search my list of workflows by name or the agents they include and filter by last modified date to efficiently manage my automations.
- **US-WF-006:** As Alex, I want to orchestrate agents I built in the Developer Platform using the ruh.ai Workflow Builder.

### MCP Tool Management
- **US-MCP-001:** As Alex, I want to register my company's internal customer database as an MCP Tool by providing its API endpoint and MCP protocol details, so my AI agents can securely query customer information.
- **US-MCP-002:** As Alex, I want to browse a marketplace of third-party MCP Tools and add them to my available resources.
- **US-MCP-003:** As Sarah, I want to search for a specific MCP Tool by name or type within our registered tools to check its configuration.

### Credential Management
- **US-CM-001:** As Alex, I want to securely store my API key for a third-party service in the platform, so that agents or workflows I use from the marketplace can utilize this service without me re-entering the key.
- **US-CM-002:** As Alex, when configuring a marketplace agent that requires an external API key, I want to be able to select from my securely stored credentials.

### Overview Dashboard
- **US-OV-001:** As Sarah, I want to see a dashboard with key metrics like total API calls, active agents, and credit usage, so I can monitor platform health and costs.
- **US-OV-002:** As Alex, I want to quickly see my most active agents and recent API request logs on my overview page, so I can get a snapshot of my current activity.

### API Keys & Apps
- **US-AK-001:** As Alex, I want to create separate API keys for my "Staging" and "Production" applications, so I can manage access and track usage independently.

## 4. Detailed Feature Requirements

### 4.1 Overview Dashboard
- Customizable widgets for key metrics (credit usage, active agents, agent/workflow requests, MCP Tools, app-specific usage)
- Feeds for latest API requests and recent platform events
- Filter dashboard data by time range
- Fast loading and clear presentation

### 4.2 Agent Management
- Intuitive creation wizard (foundation, core logic, capabilities, knowledge base, configuration, publishing controls)
- Acquire from marketplace, import via A2A, and customisation/forking
- In-platform testing, API access, and code snippets
- Robust search and filtering
- Management view (list, details, edit, delete)

### 4.3 Workflow Management
- Create/build via Workflow Builder, acquire from marketplace, and customisation/forking
- Testing and dedicated builder access
- Management view (list, details, edit, duplicate, delete)
- API execution snippets
- Robust search and filtering

### 4.4 MCP Tool Management
- Register custom MCP Tools (name, description, endpoint, schema, credentials)
- Marketplace for MCP Tools
- View, edit, and remove MCP Tools

### 4.5 Application & API Key Management
- Create logical applications as containers for API keys
- Generate/revoke API key pairs
- View API request logs and stats by application

### 4.6 Webhook Management
- Create webhooks (target URL, event types, secret)
- Manage (view, edit, enable/disable, delete) webhooks
- View webhook delivery logs

### 4.7 Activity Logging
- Log key events (API calls, executions, resource changes, webhook deliveries)
- Display logs with filtering

### 4.8 User Profile
- View profile details
- Summary of active applications and API keys

### 4.9 Credential Management (Secure Vault)
- Secure credential storage (add, encrypt, mask)
- Manage credentials (list, update, delete)
- Credential utilization (selection during configuration, audit logs)

### 4.10 Platform Settings
- Manage notification preferences
- Theme selection
- Data export options

## 5. Non-Functional Requirements
- **Performance:** Fast load times, responsive UI, low-latency API calls
- **Scalability:** Support for growing numbers of users, agents, workflows, and MCPs
- **Security:** Authentication, authorization, encrypted credential storage, compliance
- **Reliability:** High uptime, robust error handling, graceful degradation
- **Usability:** Intuitive, accessible, and consistent user experience
- **Compatibility:** Support for major browsers and devices, integration with ruh.ai ecosystem
- **Maintainability:** Clean codebase, documentation, modular architecture

## 6. Design & UX Considerations
- Intuitive navigation and user-friendly interfaces
- Efficient search and filtering
- Clear indication of customisation/forking
- Seamless credential selection
- Responsive and performant UI
- Clear feedback and error handling
- Accessibility (WCAG compliance)
- Consistent design patterns and terminology
- Onboarding aids (tours, tooltips)

## 7. Release Criteria (MVP)
- User authentication and basic profile management
- Agent Management MVP (create, build, add from marketplace, basic search/filter)
- Workflow Management MVP (add from marketplace, redirect to builder, basic search/filter)
- Application and API key management
- Basic overview dashboard
- Activity logging for critical events
- Credential Management MVP (secure storage and selection for at least one credential type)
- Cross-platform link documentation
- Stability, performance, and security for core flows
- Basic documentation for core features 