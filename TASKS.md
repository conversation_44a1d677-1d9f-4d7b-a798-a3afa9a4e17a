# Project Tasks

## ✅ Done Tasks
- [x] Agent: Implement Agent Creation
    - [x] Implement Foundation step
    - [x] Implement Core Logic step
    - [x] Implement Capabilities step
    - [x] Implement Automation step
    - [x] Implement Knowledge step
    - [x] Implement Configuration step
    - [x] Implement Preview step
- [x] Agent: Implement Agent Listing
- [x] Agent: Implement Agent Updation (Wizard Steps Completed)
    - [x] Implement Foundation step
    - [x] Implement Core Logic step
    - [x] Implement Capabilities step
    - [x] Implement Automation step
    - [x] Implement Knowledge step
    - [x] Implement Preview step
- [x] Agent: Implement Public/private visibility
- [x] Agent: Implement Agent deletion
- [x] Agent: Implement Agent filters and pagination
- [x] Workflow: Implement Workflow listing, redirection to workflow builder url upon clicking creation and updation
- [x] Workflow: Implement Workflow deletion and public/private
- [x] MCP servers and tools: Implement MCP cards listing
- [x] MCP servers and tools: Implement MCP details page
- [x] MCP servers and tools: Implement MCP creation using GitHub repository and deployed URL
- [x] MCP servers and tools: Implement MCP updation
- [x] MCP servers and tools: Implement MCP public/private and deletion
- [x] MCP servers and tools: Implement MCP filters and pagination

## ⏳ Pending Tasks
- [ ] Agent: Implement Configuration API for Agent Updation
- [ ] Apps
    - [ ] Implement Apps creation
    - [ ] Implement Apps listing
    - [ ] Implement App details page
    - [ ] Implement App settings management and deletion
- [ ] Activity
- [ ] Webhooks 