import axios, { AxiosError, AxiosRequestConfig } from "axios";
import { cookieUtils, COOKIE_NAMES } from "./cookie";

// Define base API URL from environment variable or use a default
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "https://api.example.com";

// Create an Axios instance with default config
const axiosClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 30000, // 30 seconds
});

// Helper functions for token refresh logic
const getAccessTokenRoute = "/auth/access-token";

const getRefreshToken = (): string | null => {
  // Only run on client side
  if (typeof window === "undefined") {
    console.log("getRefreshToken called on server side, returning null");
    return null;
  }

  const refreshToken = cookieUtils.getRefreshToken();
  console.log("Getting refresh token from cookies:", refreshToken ? "Found" : "Not found");

  // Additional debugging - check all cookies
  if (!refreshToken) {
    console.log("All available cookies:", document.cookie);
    console.log("Checking specific cookie name with COOKIE_NAMES.REFRESH_TOKEN:", cookieUtils.get(COOKIE_NAMES.REFRESH_TOKEN));

    // Try to get all cookies and see what's available
    const allCookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [name, value] = cookie.trim().split('=');
      acc[name] = value;
      return acc;
    }, {} as Record<string, string>);
    console.log("Parsed cookies:", allCookies);
  }

  return refreshToken || null;
};

const getAuthApi = async () => {
  // Import authService dynamically to avoid circular dependency
  const { authService } = await import("@/services/auth-service");
  return authService;
};

const clearAuthCookies = async (): Promise<void> => {
  cookieUtils.clearAllOnLogout();
};

const clearUserStore = (): void => {
  // Clear any user-related state if needed
  // This can be expanded if you have a user store
  console.log("User store cleared");
};

const clearUserStoreAndLogout = async (): Promise<void> => {
  await clearAuthCookies();
  clearUserStore();
  // Redirect to home page
  if (typeof window !== "undefined") {
    window.location.href = "/";
  }
};

// Helper function to redirect to GitHub OAuth
const redirectToGitHubAuth = async (): Promise<void> => {
  if (typeof window === "undefined") {
    return;
  }

  try {
    // Try to get user details to construct the proper GitHub OAuth URL
    const authApi = await getAuthApi();
    const userDetails = await authApi.getUserDetails();

    if (userDetails?.id) {
      const githubOAuthUrl = `${API_BASE_URL}/auth/github/?user_id=${userDetails.id}`;
      console.log("Redirecting to GitHub OAuth with user ID:", githubOAuthUrl);
      window.location.href = githubOAuthUrl;
    } else {
      console.error("Unable to get user ID for GitHub OAuth redirect");
    }
  } catch (error) {
    console.error("Error getting user details for GitHub OAuth redirect:", error);
    // Fallback: redirect to GitHub OAuth without user_id (if the backend supports it)
    const githubOAuthUrl = `${API_BASE_URL}/auth/github/`;
    console.log("Redirecting to GitHub OAuth without user ID:", githubOAuthUrl);
    window.location.href = githubOAuthUrl;
  }
};

// Request interceptor for adding auth token
axiosClient.interceptors.request.use(
  (config) => {
    // Only run on client side
    if (typeof window !== "undefined") {
      const accessToken = cookieUtils.getAccessToken();
      const tokenType = "Bearer";

      if (accessToken && config.headers) {
        config.headers.Authorization = `${tokenType} ${accessToken}`;
      }
    }

    // Log the full request URL for debugging
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`, config.params);

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors and token refresh
axiosClient.interceptors.response.use(
  (response) => {
    const accessToken = cookieUtils.getAccessToken();
    const refreshToken = cookieUtils.getRefreshToken()
    console.log("Access token:", accessToken);
    console.log("Refresh token:", refreshToken);
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {
      _retry?: boolean;
    };

    if (originalRequest.url?.includes(getAccessTokenRoute)) {
      await clearUserStoreAndLogout();
      return Promise.reject(new Error("Invalid or expired refresh token."));
    }

    // Check if the error is 400 and the endpoint is GitHub-related
    if (error.response?.status === 400 && originalRequest.url?.includes('/auth/github/')) {
      console.log("GitHub 400 error detected, redirecting to GitHub OAuth");
      await redirectToGitHubAuth();
      return Promise.reject(new Error("GitHub authentication required"));
    }

    // Check if the error is due to an expired token (401 Unauthorized or 403 Forbidden)
    if (
      (error.response?.status === 401 || error.response?.status === 403) &&
      !originalRequest._retry
    ) {
      // Mark this request as retried to prevent infinite loops
      originalRequest._retry = true;

      try {
        // Get the refresh token
        const refreshToken = getRefreshToken();

        if (!refreshToken) {
          console.log("No refresh token available, redirecting to login");
          await clearAuthCookies();
          clearUserStore();

          return Promise.reject(new Error("No refresh token available"));
        }

        console.log("Attempting to refresh token with refresh token:", refreshToken.substring(0, 10) + "...");

        // Get the auth API and generate a new access token
        const authApi = await getAuthApi();
        try {
          const tokenResponse = await authApi.generateAccessToken(refreshToken);
          console.log("Token refresh response:", { success: tokenResponse.success, hasAccessToken: !!tokenResponse.access_token });

          if (tokenResponse.success && tokenResponse.access_token) {
            console.log("Token refresh successful, updating cookies and retrying request");

            // Update the authorization header with the new token
            originalRequest.headers = {
              ...originalRequest.headers,
              Authorization: `Bearer ${tokenResponse.access_token}`,
            };

            // Update the access token in cookies
            cookieUtils.setAccessToken(tokenResponse.access_token);

            // Retry the original request with the new token
            return axiosClient(originalRequest);
          } else {
            console.log("Token refresh failed: Invalid response", tokenResponse);
            await clearAuthCookies();
            clearUserStore();
            return Promise.reject(new Error("Token refresh failed: Invalid response"));
          }
        } catch (tokenError: any) {
          console.log("Token refresh failed with error:", tokenError);
          await clearAuthCookies();
          clearUserStore();
          return Promise.reject(tokenError);
        }
      } catch (refreshError) {
        // Clear cookies and redirect to login on refresh error
        console.error("Error during refresh token process:", refreshError);
        await clearAuthCookies();
        clearUserStore();
        return Promise.reject(refreshError);
      }
    }

    // For other errors, just reject the promise
    return Promise.reject(error);
  }
);

export default axiosClient;
