// GCS Upload Interfaces

/**
 * Request payload for getting a presigned URL
 */
export interface PreSignedUrlRequest {
  fileName: string;
  fileType: string;
  filePath: string;
}

/**
 * Response from the presigned URL API
 */
export interface PreSignedUrlResponse {
  url: string;
  filePath: string;
  publicUrl?: string;
}

/**
 * Options for GCS upload
 */
export interface GCSUploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
}

// Activity Log Interfaces

export interface LogDetails {
  id: string;
  activity_id: string;
  http_method: string;
  endpoint: string;
  status_code: number;
  request_payload: Record<string, any>;
  response_payload: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface EventDetails {
  [key: string]: any;
}

export interface Activity {
  id: string;
  created_at: string;
  event_source_type: string;
  resource_id: string;
  user_id: string;
  timestamp: string;
  type: string;
  source_identifier: string;
  status: string;
  user_metadata: Record<string, any>;
  updated_at: string;
  logs: ActivityLog[];
  events: ActivityEvent[];
}

export interface ActivitiesMetadata {
  total: number;
  total_pages: number;
  current_page: number;
  page_size: number;
  has_next_page: boolean;
  has_previous_page: boolean;
}

export interface ActivitiesResponse {
  activities: Activity[];
  metadata: ActivitiesMetadata;
}

// Activity Logs Interfaces
export interface ActivityLog {
  id: string;
  activity_id: string;
  log_type: string;
  log_status: string;
  log_details: Record<string, any>;
  created_at: string;
}

export interface ActivityLogsResponse {
  success: boolean;
  message: string;
  data: ActivityLog[];
  metadata: ActivitiesMetadata;
}

// Activity Events Interfaces
export interface ActivityEvent {
  id: string;
  activity_id: string;
  event_name: string;
  event_details: Record<string, any>;
  created_at: string;
}

export interface ActivityEventsResponse {
  success: boolean;
  message: string;
  data: ActivityEvent[];
  metadata: ActivitiesMetadata;
}
