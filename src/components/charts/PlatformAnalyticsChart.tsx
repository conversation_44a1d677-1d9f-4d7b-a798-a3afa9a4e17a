import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

const data = [
  { name: '05/01', Requests: 0, 'Completion Rate (%)': 0 },
  { name: '05/02', Requests: 0, 'Completion Rate (%)': 0 },
  { name: '05/03', Requests: 0, 'Completion Rate (%)': 0 },
  { name: '05/04', Requests: 0, 'Completion Rate (%)': 0 },
  { name: '05/05', Requests: 0, 'Completion Rate (%)': 0 },
  { name: '05/06', Requests: 0,'Completion Rate (%)': 0 },
  { name: '05/07', Requests: 0, 'Completion Rate (%)': 0 },
];

export function PlatformAnalyticsChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Analytics</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ width: '100%', height: 400 }}>
          <ResponsiveContainer>
            <AreaChart
              data={data}
              margin={{
                top: 10,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" stroke="#8884d8" />
              <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
              <Tooltip />
               <Legend />
              <Area yAxisId="left" type="monotone" dataKey="Requests" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
              <Area yAxisId="right" type="monotone" dataKey="Completion Rate (%)" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.3} />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
} 