import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const data = [
  { name: 'Agents', 'Tokens Used': 0, 'Cost ($)': 0 },
  { name: 'Workflows', 'Tokens Used': 0, 'Cost ($)': 0 },
  { name: 'Custom MCPs', 'Tokens Used': 0, 'Cost ($)': 0 },
  { name: 'App Credits', 'Tokens Used': 0, 'Cost ($)': 0 },
  { name: 'Other', 'Tokens Used': 0, 'Cost ($)': 0 },
];

export function CreditUsageBreakdownChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Credit Usage Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ width: '100%', height: 400 }}>
          <ResponsiveContainer>
            <BarChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
              <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
              <Tooltip />
              <Legend />
              <Bar yAxisId="left" dataKey="Tokens Used" fill="#8884d8" />
              <Bar yAxisId="right" dataKey="Cost ($)" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
} 