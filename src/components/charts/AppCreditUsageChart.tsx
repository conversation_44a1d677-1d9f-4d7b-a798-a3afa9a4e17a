import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

const data = [
  { name: '05/01', 'App Credits': 0, 'Cost ($)': 0 },
  { name: '05/02', 'App Credits': 0, 'Cost ($)': 0 },
  { name: '05/03', 'App Credits': 0, 'Cost ($)': 0 },
  { name: '05/04', 'App Credits': 0, 'Cost ($)': 0 },
  { name: '05/05', 'App Credits': 0, 'Cost ($)': 0 },
  { name: '05/06', 'App Credits': 0, 'Cost ($)': 0 },
  { name: '05/07', 'App Credits': 0, 'Cost ($)': 0 },
];

export function AppCreditUsageChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>App Credit Usage</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ width: '100%', height: 400 }}>
          <ResponsiveContainer>
            <LineChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
              <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
              <Tooltip />
              <Legend />
              <Line yAxisId="left" type="monotone" dataKey="App Credits" stroke="#8884d8" activeDot={{ r: 8 }} />
              <Line yAxisId="right" type="monotone" dataKey="Cost ($)" stroke="#82ca9d" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
} 