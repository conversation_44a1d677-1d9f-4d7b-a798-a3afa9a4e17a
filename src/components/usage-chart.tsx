"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChevronDown, Check } from "lucide-react"
import { useState } from "react"

const data = [
  { name: "Apr 25", apiCalls: 180000, errorRate: 60000 },
  { name: "Apr 26", apiCalls: 190000, errorRate: 50000 },
  { name: "Apr 27", apiCalls: 210000, errorRate: 40000 },
  { name: "Apr 28", apiCalls: 240000, errorRate: 70000 },
  { name: "Apr 29", apiCalls: 190000, errorRate: 50000 },
  { name: "Apr 30", apiCalls: 180000, errorRate: 40000 },
  { name: "May 1", apiCalls: 190000, errorRate: 45000 },
]

export function UsageChart() {
  const [metricType, setMetricType] = useState("API Calls");
  const [timeRange, setTimeRange] = useState("Last 7 Days");
  const [showErrorRate, setShowErrorRate] = useState(true);

  const metricOptions = ["API Calls", "Workflow Runs", "Agent Interactions", "Event Volume"];
  const timeRangeOptions = ["Last 24 Hours", "Last 7 Days", "Last 30 Days", "Custom Range"];

  return (
    <Card className="col-span-3">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>Usage Over Time</CardTitle>
          <p className="text-sm text-muted-foreground">
            Platform activity for the selected period
          </p>
        </div>
        <div className="flex items-center gap-4">
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center gap-1 rounded-md border px-3 py-2 text-sm">
              {metricType}
              <ChevronDown className="h-4 w-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {metricOptions.map((option) => (
                <DropdownMenuItem
                  key={option}
                  onClick={() => setMetricType(option)}
                  className="flex items-center gap-2"
                >
                  {option === metricType && <Check className="h-4 w-4" />}
                  <span className={option === metricType ? "ml-0" : "ml-6"}>{option}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center gap-1 rounded-md border px-3 py-2 text-sm">
              {timeRange}
              <ChevronDown className="h-4 w-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {timeRangeOptions.map((option) => (
                <DropdownMenuItem
                  key={option}
                  onClick={() => setTimeRange(option)}
                  className="flex items-center gap-2"
                >
                  {option === timeRange && <Check className="h-4 w-4" />}
                  <span className={option === timeRange ? "ml-0" : "ml-6"}>{option}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={data}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient id="colorApiCalls" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
                </linearGradient>
                <linearGradient id="colorErrorRate" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
                domain={[0, 240000]}
                tickFormatter={(value) => `${value / 1000}k`}
              />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="apiCalls"
                stroke="#3b82f6"
                fillOpacity={1}
                fill="url(#colorApiCalls)"
                name="API Calls"
              />
              {showErrorRate && (
                <Area
                  type="monotone"
                  dataKey="errorRate"
                  stroke="#ef4444"
                  fillOpacity={1}
                  fill="url(#colorErrorRate)"
                  name="Error Rate (%)"
                />
              )}
            </AreaChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-4 flex items-center justify-center gap-8">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-blue-500"></div>
            <span className="text-xs text-muted-foreground">API Calls</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-red-500"></div>
            <span className="text-xs text-muted-foreground">Error Rate (%)</span>
          </div>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="showErrorRate"
              className="h-4 w-4 cursor-pointer"
              checked={showErrorRate}
              onChange={() => setShowErrorRate(!showErrorRate)}
            />
            <label
              htmlFor="showErrorRate"
              className="text-xs text-muted-foreground cursor-pointer"
            >
              Show Error Rate
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
