"use client"

import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card"
import { 
  AreaChart, 
  Area, 
  XAxis, 
  <PERSON>Axis, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer 
} from "recharts"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

// Sample data for agent performance
const agentPerformanceData = [
  { date: "05/01", requests: 854, completionRate: 97, responseTime: 3.2 },
  { date: "05/02", requests: 926, completionRate: 98, responseTime: 3.1 },
  { date: "05/03", requests: 1054, completionRate: 96, responseTime: 3.4 },
  { date: "05/04", requests: 987, completionRate: 97, responseTime: 3.3 },
  { date: "05/05", requests: 1123, completionRate: 99, responseTime: 3.0 },
  { date: "05/06", requests: 1087, completionRate: 98, responseTime: 3.1 },
  { date: "05/07", requests: 1182, completionRate: 97, responseTime: 3.2 },
]

// Sample data for workflow utilization
const workflowUtilizationData = [
  { date: "05/01", executions: 246, successRate: 95, avgDuration: 1.8 },
  { date: "05/02", executions: 275, successRate: 96, avgDuration: 1.7 },
  { date: "05/03", executions: 312, successRate: 94, avgDuration: 1.9 },
  { date: "05/04", executions: 298, successRate: 97, avgDuration: 1.6 },
  { date: "05/05", executions: 342, successRate: 98, avgDuration: 1.5 },
  { date: "05/06", executions: 328, successRate: 96, avgDuration: 1.7 },
  { date: "05/07", executions: 365, successRate: 97, avgDuration: 1.6 },
]

export function PlatformAnalytics() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Analytics</CardTitle>
        <CardDescription>
          Detailed insights into agent performance and workflow execution
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="agents">
          <TabsList className="mb-4">
            <TabsTrigger value="agents">Agent Performance</TabsTrigger>
            <TabsTrigger value="workflows">Workflow Utilization</TabsTrigger>
          </TabsList>
          
          <TabsContent value="agents">
            <ResponsiveContainer width="100%" height={350}>
              <AreaChart
                data={agentPerformanceData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" opacity={0.4} />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)',
                    borderRadius: '6px',
                  }} 
                />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="requests" 
                  name="Requests"
                  stroke="#8b5cf6" 
                  fill="#8b5cf6" 
                  fillOpacity={0.3} 
                />
                <Area 
                  type="monotone" 
                  dataKey="completionRate" 
                  name="Completion Rate (%)"
                  stroke="#f97316" 
                  fill="#f97316" 
                  fillOpacity={0.3} 
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
          
          <TabsContent value="workflows">
            <ResponsiveContainer width="100%" height={350}>
              <AreaChart
                data={workflowUtilizationData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" opacity={0.4} />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)',
                    borderRadius: '6px',
                  }} 
                />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="executions" 
                  name="Executions"
                  stroke="#06b6d4" 
                  fill="#06b6d4" 
                  fillOpacity={0.3} 
                />
                <Area 
                  type="monotone" 
                  dataKey="successRate" 
                  name="Success Rate (%)"
                  stroke="#ec4899" 
                  fill="#ec4899" 
                  fillOpacity={0.3} 
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
