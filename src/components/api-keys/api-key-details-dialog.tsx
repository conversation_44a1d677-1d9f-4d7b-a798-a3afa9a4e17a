"use client"

import * as React from "react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { CopyIcon, EyeIcon, EyeOffIcon, CheckIcon } from "lucide-react"
import { toast } from "sonner"

interface ApiKeyDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  keyDetails: { public_key: string; private_key: string } | null
}

export function ApiKeyDetailsDialog({
  open,
  onOpenChange,
  keyDetails
}: ApiKeyDetailsDialogProps) {
  const [showPrivateKey, setShowPrivateKey] = useState(false)

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setShowPrivateKey(false)
    }
  }, [open])

  // Toggle private key visibility
  const togglePrivateKeyVisibility = () => {
    setShowPrivateKey(!showPrivateKey)
  }

  // Handle copy to clipboard
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text)
    toast("Copied to clipboard", {
      description: "API key has been copied to clipboard",
    })
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>API Key Created Successfully</AlertDialogTitle>
          <AlertDialogDescription>
            Your API key has been created. Please copy your private key now as it won&apos;t be shown again.
          </AlertDialogDescription>
        </AlertDialogHeader>

        {keyDetails && (
          <div className="space-y-4 my-4">
            <div className="space-y-2">
              <Label>Public Key</Label>
              <div className="flex items-center gap-2">
                <code className="flex-1 rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm overflow-x-auto">
                  {keyDetails.public_key}
                </code>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handleCopy(keyDetails.public_key)}
                >
                  <CopyIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Private Key</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 text-xs"
                  onClick={togglePrivateKeyVisibility}
                >
                  {showPrivateKey ? (
                    <>
                      <EyeOffIcon className="h-3 w-3 mr-1" />
                      Hide
                    </>
                  ) : (
                    <>
                      <EyeIcon className="h-3 w-3 mr-1" />
                      Show
                    </>
                  )}
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <code className="flex-1 rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm overflow-x-auto">
                  {showPrivateKey
                    ? keyDetails.private_key
                    : "••••••••••••••••••••••••••••••••••••••••••••••••••"}
                </code>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handleCopy(keyDetails.private_key)}
                >
                  <CopyIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}

        <AlertDialogFooter>
          <AlertDialogAction>
            <CheckIcon className="h-4 w-4 mr-2" />
            Done
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}