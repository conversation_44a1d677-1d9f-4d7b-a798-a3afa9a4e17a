"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { PlusIcon } from "lucide-react"
import { useUserApplications } from "@/hooks/use-applications"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface CreateApiKeyDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateApiKey: (name: string, appId: string) => void
  isCreating: boolean
}

export function CreateApiKeyDialog({
  open,
  onOpenChange,
  onCreateApiKey,
  isCreating
}: CreateApiKeyDialogProps) {
  const [newKeyName, setNewKeyName] = useState("")
  const [selectedAppId, setSelectedAppId] = useState("")

  // Fetch applications for dropdown
  const { data, isLoading } = useUserApplications()
  const applications = data?.data || []

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      // Use setTimeout to ensure the dialog is fully closed before resetting state
      setTimeout(() => {
        setNewKeyName("")
        setSelectedAppId("")
      }, 100)
    }
  }, [open])

  // Handle dialog open/close
  const handleOpenChange = (open: boolean) => {
    onOpenChange(open)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New API Key</DialogTitle>
          <DialogDescription>
            Enter a name and select an app for your new API key.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={newKeyName}
              onChange={(e) => setNewKeyName(e.target.value)}
              placeholder="Production API Key"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="app">App</Label>
            {isLoading ? (
              <div className="h-9 animate-pulse bg-muted rounded-md"></div>
            ) : (
              <Select
                value={selectedAppId}
                onValueChange={setSelectedAppId}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select an app" />
                </SelectTrigger>
                <SelectContent>
                  {applications.length === 0 ? (
                    <SelectItem value="no-apps" disabled>
                      No apps available
                    </SelectItem>
                  ) : (
                    applications.map((app) => (
                      <SelectItem key={app.id} value={app.id}>
                        {app.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button
            onClick={() => onCreateApiKey(newKeyName, selectedAppId)}
            disabled={!newKeyName || !selectedAppId || isCreating}
          >
            {isCreating ? "Creating..." : "Create API Key"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function CreateApiKeyButton({ onClick }: { onClick: () => void }) {
  return (
    <Button className="gap-1" onClick={onClick}>
      <PlusIcon className="h-4 w-4" />
      Generate New API Key
    </Button>
  )
}