"use client";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  BarChart2,
  Workflow,
  Users,
  Server,
  Key,
  Activity,
  Webhook,
  AppWindow,
  User,
  ChevronLeft,
  ChevronRight,
  X,
} from "lucide-react";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTitle, SheetDescription } from "@/components/ui/sheet";
import React, { useCallback } from "react";
// Make sure to import the updated UserAvatar
import { UserAvatar } from "./user-avatar";
import { useUserDetails } from "@/hooks/use-auth";

const mainSidebarItems = [
  { name: "Overview", href: "/dashboard/overview", icon: BarChart2 },
  { name: "Agents", href: "/dashboard/agents", icon: Users },
  { name: "Workflows", href: "/dashboard/workflows", icon: Workflow },
  { name: "Tools & Servers", href: "/dashboard/tools", icon: Server },
  { name: "Apps", href: "/dashboard/apps", icon: AppWindow },
  { name: "API Keys", href: "/dashboard/api-keys", icon: Key },
  { name: "Activity", href: "/dashboard/activity", icon: Activity },
];

const bottomSidebarItems = [
  { name: "My Profile", href: "/dashboard/profile", icon: User },
];

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

export function Sidebar({ isOpen, setIsOpen }: SidebarProps) {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const toggleCollapse = () => setIsCollapsed(!isCollapsed);
  const { data: userDetails } = useUserDetails();
  const isMobileRef = React.useRef(false);

  React.useEffect(() => {
    if (typeof window !== "undefined") {
      isMobileRef.current = window.innerWidth < 1024;
      const handleResize = () => {
        isMobileRef.current = window.innerWidth < 1024;
      };
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  const handleNavigation = useCallback(() => {
    if (isMobileRef.current) {
      setIsOpen(false);
    }
  }, [setIsOpen]);

  // Desktop sidebar
  const DesktopSidebar = (
    <div
      className={cn(
        "hidden lg:flex h-screen flex-col bg-sidebar border-r border-sidebar-border transition-all duration-300 relative",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex h-14 items-center border-b border-sidebar-border px-4 justify-between">
        {!isCollapsed ? (
          <>
            <Link href="/dashboard" className="flex items-center gap-2">
              <div className="flex h-8 w-20 items-center justify-center">
                <Image
                  src="/logo.svg"
                  alt="RUH AI Logo"
                  width={50}
                  height={24}
                  className="h-8 w-full"
                />
              </div>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-sidebar-foreground/70 hover:text-sidebar-foreground"
              onClick={toggleCollapse}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </>
        ) : (
          <>
            <Link
              href="/dashboard"
              className="flex w-full items-center justify-center"
            >
              <div className="flex h-8 w-8 items-center justify-center">
                <Image
                  src="/logo.svg"
                  alt="RUH AI Logo"
                  width={20}
                  height={20}
                  className="h-8 w-8"
                />
              </div>
            </Link>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-background shadow-md border-sidebar-border"
              onClick={toggleCollapse}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </>
        )}
      </div>

      <div className="flex-1 overflow-auto py-6 flex flex-col justify-between">
        <nav
          className={cn(
            "flex flex-col items-center",
            isCollapsed ? "px-0 gap-4" : "px-2 gap-1"
          )}
        >
          <TooltipProvider delayDuration={0}>
            {mainSidebarItems.map((item) => (
              <React.Fragment key={item.href}>
                {isCollapsed ? (
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <Link
                        href={item.href}
                        className={cn(
                          "flex h-8 w-8 items-center justify-center rounded-md relative",
                          pathname === item.href
                            ? "text-primary bg-primary/10"
                            : "text-muted-foreground hover:text-foreground hover:bg-sidebar-accent/20"
                        )}
                        onClick={handleNavigation}
                      >
                        <item.icon className="h-4 w-4" />
                        <span className="sr-only">{item.name}</span>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="font-medium">
                      {item.name}
                    </TooltipContent>
                  </Tooltip>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                      "flex w-full items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                      pathname === item.href
                        ? "bg-sidebar-accent text-sidebar-accent-foreground"
                        : "text-sidebar-foreground/70 hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground"
                    )}
                    onClick={handleNavigation}
                  >
                    <item.icon className="h-4 w-4" />
                    {item.name}
                  </Link>
                )}
              </React.Fragment>
            ))}
          </TooltipProvider>
        </nav>

        <div className="mt-auto">
          {!isCollapsed ? (
            <div className="mx-2 my-4 h-px bg-sidebar-border/60" />
          ) : (
            <div className="my-4 w-8 h-px bg-sidebar-border/60 mx-auto" />
          )}
          <nav
            className={cn(
              "flex flex-col items-center rounded-t-lg",
              isCollapsed
                ? "px-0 gap-4 py-4 mx-1 bg-sidebar-accent/10"
                : "px-2 gap-1 py-3 mx-1 bg-sidebar-accent/5"
            )}
          >
            <TooltipProvider delayDuration={0}>
              {/* === CHANGE IS HERE === */}
              {isCollapsed ? (
                <Tooltip delayDuration={0}>
                  <TooltipTrigger asChild>
                    {/* Pass isCollapsed={true} and let the component handle the rest */}
                    <UserAvatar isCollapsed={true} />
                  </TooltipTrigger>
                  <TooltipContent side="right" className="font-medium">
                    {userDetails?.fullName || 'User Profile'}
                  </TooltipContent>
                </Tooltip>
              ) : (
                // Pass isCollapsed={false} (or omit it) and let the component handle the rest
                <UserAvatar isCollapsed={false} />
              )}
              {/* === END OF CHANGE === */}
            </TooltipProvider>
          </nav>
        </div>
      </div>
    </div>
  );

  // Mobile sidebar
  const MobileSidebar = React.useMemo(() => (
    <Sheet
      open={isOpen}
      onOpenChange={(open) => {
        if (open !== isOpen) setIsOpen(open);
      }}
    >
      <SheetContent side="left" className="p-0 w-[280px]" hideCloseButton={true}>
        <div className="sr-only">
          <SheetTitle>Navigation Menu</SheetTitle>
          <SheetDescription>Navigation menu for the RUH AI Developer Platform</SheetDescription>
        </div>
        <div className="flex h-14 items-center border-b border-sidebar-border px-4 justify-between">
          <Link href="/dashboard" className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center">
              <Image src="/logo.svg" alt="RUH AI Logo" width={24} height={24} className="h-8 w-8"/>
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-bold text-sidebar-foreground">RUH AI</span>
              <span className="text-xs text-sidebar-foreground/70">Developer</span>
            </div>
          </Link>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setIsOpen(false)}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex-1 overflow-auto py-6 flex flex-col justify-between h-[calc(100vh-3.5rem)]">
          <nav className="flex flex-col items-center px-2 gap-1">
            {mainSidebarItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex w-full items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                  pathname === item.href
                    ? "bg-sidebar-accent text-sidebar-accent-foreground"
                    : "text-sidebar-foreground/70 hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground"
                )}
                onClick={handleNavigation}
              >
                <item.icon className="h-4 w-4" />
                {item.name}
              </Link>
            ))}
          </nav>
          <div className="mt-auto">
            <div className="mx-2 my-4 h-px bg-sidebar-border/60" />
            <nav className="flex flex-col items-center px-2 gap-1 py-3 mx-1 bg-sidebar-accent/5 rounded-t-lg">
              <UserAvatar />
            </nav>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  ), [isOpen, setIsOpen, handleNavigation, pathname, userDetails]);

  return (
    <>
      {DesktopSidebar}
      {MobileSidebar}
    </>
  );
}

export const MemoizedSidebar = React.memo(Sidebar);