"use client";

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON>ton } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { PlusIcon } from "lucide-react";

interface CreateAppDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateApp: (name: string, description: string) => void;
}

export function CreateAppDialog({ isOpen, onOpenChange, onCreateApp }: CreateAppDialogProps) {
  const [appName, setAppName] = useState('');
  const [appDescription, setAppDescription] = useState('');

  const handleCreateClick = () => {
    onCreateApp(appName, appDescription);
    // Reset form fields after creation
    setAppName('');
    setAppDescription('');
  };

  const handleOpenChange = (open: boolean) => {
    onOpenChange(open);
    // Reset form fields when dialog is closed without creating
    if (!open) {
      setAppName('');
      setAppDescription('');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {/* The trigger button will be placed in the parent component */}
        {/* This empty trigger is necessary for the Dialog to work with onOpenChange prop */}
        <Button variant="ghost" className="h-0 w-0 p-0 invisible">Open Dialog</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New App</DialogTitle>
          <DialogDescription>
            Create a new app to organize your AI workflows and resources.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="appName">App Name</Label>
            <Input
              id="appName"
              value={appName}
              onChange={(e) => setAppName(e.target.value)}
              placeholder="e.g. Marketing Assistant"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="appDescription">Description</Label>
            <Textarea
              id="appDescription"
              value={appDescription}
              onChange={(e) => setAppDescription(e.target.value)}
              placeholder="e.g. An app to generate marketing content"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreateClick}>
            Create App
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 