"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON>ertCircle, AlertTriangle, CheckCircle, XCircle } from "lucide-react"

const activityItems = [
  {
    type: "API Error",
    description: "POST /v1/workflow/wf-456/run - 500 Internal Server Error",
    time: "4 minutes ago",
    status: "error",
  },
  {
    type: "Workflow Failure",
    description: 'Workflow "Lead Processing" failed at step "CRM Update"',
    time: "12 minutes ago",
    status: "warning",
  },
  {
    type: "Agent Status Change",
    description: 'Agent "Support Bot" went Offline',
    time: "47 minutes ago",
    status: "warning",
  },
  {
    type: "Webhook Delivery Failed",
    description: "Failed delivery to endpoint example.com/hook",
    time: "1 hour ago",
    status: "error",
  },
  {
    type: "Agent Deployed",
    description: 'Agent "Sales Assistant" deployed successfully',
    time: "2 hours ago",
    status: "success",
  },
]

export function RecentActivity() {
  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Recent Activity & Issues</CardTitle>
        <p className="text-sm text-muted-foreground">
          Latest system activity and potential issues
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activityItems.map((item, index) => (
            <div
              key={index}
              className="flex items-start gap-4 rounded-lg border p-3"
            >
              <div className="mt-0.5">
                {item.status === "error" && (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                {item.status === "warning" && (
                  <AlertTriangle className="h-5 w-5 text-amber-500" />
                )}
                {item.status === "success" && (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                )}
                {item.status === "info" && (
                  <AlertCircle className="h-5 w-5 text-blue-500" />
                )}
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium">{item.type}</h4>
                <p className="text-xs text-muted-foreground">{item.description}</p>
                <p className="mt-1 text-xs text-muted-foreground">{item.time}</p>
              </div>
            </div>
          ))}
          <div className="flex justify-center">
            <a
              href="/activity"
              className="text-xs text-primary hover:underline"
            >
              View Full Activity Log →
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
