"use client"

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
// import { ScrollArea } from "@/components/ui/scroll-area"; // For potentially large schemas - Removed due to import error

interface ViewSchemaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  schemaName: string; // e.g., "Input Schema" or "Output Schema"
  schema: object | null;
}

export const ViewSchemaDialog: React.FC<ViewSchemaDialogProps> = ({
  open,
  onOpenChange,
  schemaName,
  schema,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{schemaName}</DialogTitle>
          {schema ? (
            <DialogDescription>
              Viewing the {schemaName.toLowerCase()} for the tool.
            </DialogDescription>
          ) : (
            <DialogDescription>
              No {schemaName.toLowerCase()} is available for this tool.
            </DialogDescription>
          )}
        </DialogHeader>
        
        {schema && (
          <div className="max-h-[60vh] my-4 overflow-auto border rounded-md bg-muted/50">
            <pre className="text-sm p-4 rounded-md">
              {JSON.stringify(schema, null, 2)}
            </pre>
          </div>
        )}

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};