"use client"

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Plus, X, Edit, Eye } from "lucide-react";
import { toast } from "sonner";
import { JsonEditorWithPrettify } from "@/components/common/JsonEditorWithPrettify";
import { useUpdateMCPServerToolOutputSchema } from "@/hooks/use-mcp-servers";

// Form schema for validation
const propertySchema = z.object({
  type: z.enum(["string", "number", "boolean", "array"]),
  title: z.string().min(1, "Title is required").max(50, "Title must be at most 50 characters"),
  description: z.string().min(1, "Description is required").max(200, "Description must be at most 200 characters"),
});

const outputSchemaFormSchema = z.object({
  properties: z.array(propertySchema).min(1, "At least one property is required"),
});

type OutputSchemaFormData = z.infer<typeof outputSchemaFormSchema>;

interface ViewOutputSchemaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  toolName: string;
  mcpId: string;
  schema: object | null;
}

export const ViewOutputSchemaDialog: React.FC<ViewOutputSchemaDialogProps> = ({
  open,
  onOpenChange,
  toolName,
  mcpId,
  schema,
}) => {
  // State for edit mode
  const [isEditing, setIsEditing] = useState(false);
  
  // Tab state for editing
  const [activeTab, setActiveTab] = useState<"properties" | "json">("properties");

  // JSON tab state
  const [schemaString, setSchemaString] = useState("");
  const [isValidJson, setIsValidJson] = useState(true);

  // API mutation hook
  const updateToolOutputSchema = useUpdateMCPServerToolOutputSchema();

  // Property form
  const form = useForm<OutputSchemaFormData>({
    resolver: zodResolver(outputSchemaFormSchema),
    defaultValues: {
      properties: [{ type: "string", title: "", description: "" }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "properties",
  });

  // Initialize form data when schema changes or editing starts
  useEffect(() => {
    if (schema) {
        // Always set the JSON string for the JSON tab when schema is available
        setSchemaString(JSON.stringify(schema, null, 2));

        if (isEditing) {
            // Convert schema to form format only when entering edit mode
            const schemaObj = schema as any;
            if (schemaObj.properties) {
                const propertiesArray = Object.entries(schemaObj.properties).map(([key, value]: [string, any]) => ({
                    type: value.type || "string",
                    title: key,
                    description: value.description || "",
                }));
                
                form.reset({
                    properties: propertiesArray.length > 0 ? propertiesArray : [{ type: "string", title: "", description: "" }],
                });
            } else {
                // Reset to default if schema has no properties
                form.reset({
                    properties: [{ type: "string", title: "", description: "" }],
                });
            }
        }
    }
  }, [schema, isEditing, form]);


  // JSON tab handlers
  const handleSchemaChange = (jsonString: string) => {
    setSchemaString(jsonString);
    try {
      JSON.parse(jsonString);
      setIsValidJson(true);
    } catch (e) {
      setIsValidJson(false);
    }
  };

  // Property form submit handler
  const handleFormSubmit = (data: OutputSchemaFormData) => {
    // Transform the form data into the required JSON schema format
    const updatedSchema = {
      type: "object",
      properties: data.properties.reduce((acc, property) => {
        acc[property.title] = {
          type: property.type,
          description: property.description,
          title: property.title,
        };
        return acc;
      }, {} as Record<string, any>),
      required: data.properties.map(p => p.title),
    };

    updateSchema(updatedSchema);
  };

  // JSON submit handler
  const handleJsonSubmit = () => {
    try {
      const parsedSchema = JSON.parse(schemaString);
      updateSchema(parsedSchema);
    } catch (e) {
      toast.error("Invalid JSON. Please correct the schema before submitting.");
      setIsValidJson(false);
    }
  };

  // Update schema via API
  const updateSchema = (updatedSchema: object) => {
    const payload = {
      mcp_id: mcpId,
      tool_name: toolName,
      output_schema_json: updatedSchema,
    };

    updateToolOutputSchema.mutate(payload, {
      onSuccess: () => {
        toast.success(`Output schema for '${toolName}' updated successfully!`);
        setIsEditing(false);
        onOpenChange(false);
      },
      onError: (err: any) => {
        console.error("Error updating output schema:", err);
        const errorMessage = err.response?.data?.message || "Failed to update output schema.";
        toast.error(errorMessage);
      }
    });
  };

  // Combined submit handler
  const handleSubmit = () => {
    if (activeTab === "properties") {
      form.handleSubmit(handleFormSubmit)();
    } else {
      handleJsonSubmit();
    }
  };

  const addProperty = () => {
    append({ type: "string", title: "", description: "" });
  };

  const removeProperty = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const handleClose = () => {
    setIsEditing(false);
    setActiveTab("properties");
    form.reset();
    setSchemaString("");
    setIsValidJson(true);
    onOpenChange(false);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  // Check if current tab is valid for submission
  const isSubmitDisabled = () => {
    if (activeTab === "properties") {
      return !form.formState.isValid;
    } else {
      return !isValidJson || !schemaString.trim();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isEditing ? <Edit className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            {isEditing ? "Edit Output Schema" : "Output Schema"}
            : {toolName}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Edit the output schema using either property-based form or JSON input."
              : "Viewing the output schema for the tool."
            }
          </DialogDescription>
        </DialogHeader>

        {!schema ? (
          <div className="py-8 text-center">
            <p className="text-muted-foreground">
              No output schema is available for this tool.
            </p>
          </div>
        ) : isEditing ? (
          // Edit mode with tabs
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "properties" | "json")} className="space-y-4 pt-4">
            <TabsList className="grid w-auto max-w-[50%] grid-cols-2">
              <TabsTrigger value="properties">Properties</TabsTrigger>
              <TabsTrigger value="json">JSON</TabsTrigger>
            </TabsList>

            <TabsContent value="properties" className="space-y-4">
              <Form {...form}>
                <div className="py-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium">Properties</h3>
                    <Button type="button" variant="outline" size="sm" onClick={addProperty}>
                      <Plus className="h-4 w-4 mr-1" />
                      Add Property
                    </Button>
                  </div>

                  <div className="space-y-4">
                    {fields.map((field, index) => (
                      <div key={field.id} className="border rounded-lg p-4 space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">Property {index + 1}</h4>
                          {fields.length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeProperty(index)}
                              className="text-destructive hover:text-destructive"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name={`properties.${index}.type`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Type</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="string">String</SelectItem>
                                    <SelectItem value="number">Number</SelectItem>
                                    <SelectItem value="boolean">Boolean</SelectItem>
                                    <SelectItem value="array">Array</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`properties.${index}.title`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Title</FormLabel>
                                <FormControl>
                                  <Input placeholder="Property name" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`properties.${index}.description`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Description</FormLabel>
                                <FormControl>
                                  <Input placeholder="Property description" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Form>
            </TabsContent>

            <TabsContent value="json" className="space-y-4">
              <div className="py-4">
                <h3 className="text-lg font-medium mb-4">JSON Schema</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Edit your JSON schema directly. Use the editor below to modify your schema.
                </p>
                <JsonEditorWithPrettify
                  initialJsonString={schemaString}
                  onJsonChange={handleSchemaChange}
                  height="300px"
                />
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          // View mode - display schema as JSON with Edit button at top right
          <div className="my-4 grid gap-4">
            <div className="flex justify-end">
                <Button onClick={handleEditToggle} variant="outline">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Schema
                </Button>
            </div>
            <div className="max-h-[55vh] min-w-0 overflow-auto border rounded-md bg-muted/50">
              <pre className="text-sm p-4">
                {JSON.stringify(schema, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <DialogFooter className="pt-6">
          {isEditing ? (
            <div className="flex items-center justify-between w-full">
                <Button variant="outline" onClick={handleClose}>
                    Cancel
                </Button>
                <Button
                onClick={handleSubmit}
                disabled={isSubmitDisabled() || updateToolOutputSchema.isPending}
                >
                {updateToolOutputSchema.isPending ? "Updating..." : "Update Schema"}
                </Button>
            </div>
          ) : (
            <div className="flex justify-end w-full">
                <Button variant="outline" onClick={handleClose}>
                    Close
                </Button>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};