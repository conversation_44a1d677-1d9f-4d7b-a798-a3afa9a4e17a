"use client"

import { useState, useMemo } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Globe, Code, Settings, Plus, Github, Trash2, X, FolderGit2, GitBranch, User } from "lucide-react"
import { toast } from "sonner"
import { useCreateMCPServer } from "@/hooks/use-mcp-servers"
import { FileUpload } from "@/components/common/file-upload"
import { useUserDetails } from "@/hooks/use-auth"
import { useGitHubRepositoriesInfinite, useGitHubBranches, useGitHubUser } from "@/hooks/use-github"
import { getApiBaseUrl } from "@/lib/helpers"

// Base form schema - common fields
const baseSchema = z.object({
  name: z.string().min(1, "Server name is required").min(3, "Server name must be at least 3 characters").max(30, "Server name must be less than 30 characters"),
  category: z.enum(["general", "sales", "marketing", "engineering", "finance", "hr"], {
    required_error: "Category is required",
  }),
  description: z.string().optional().refine(val => !val || val.length >= 10 && val.length <= 200, "Description must be at least 10 characters and less than 200 characters if provided"),
  tags: z.string().optional(),
  logo: z.string().optional(),
})

// GitHub-specific schema
const githubSchema = baseSchema.extend({
  repository: z.string().min(1, "Repository is required"),
  branch: z.string().min(1, "Branch is required"),
  repositoryUrl: z.string().optional(),
  serverConfigs: z.array(z.object({
    url: z.string(),
    protocol: z.enum(["HTTP", "SSE", "STDIO"]),
  })).optional(),
  gitUrl: z.string().optional(),
  mcp_type: z.enum(["SSE", "HTTP", "STDIO"], { required_error: "MCP Type is required" }),
  env_keys: z.array(z.object({
    key: z.string().min(1, "Key is required"),
    description: z.string().optional(),
  })).optional(),
})

// Hosted URLs schema
const hostedSchema = baseSchema.extend({
  serverConfigs: z.array(z.object({
    url: z.string().url("Please enter a valid URL"),
    protocol: z.enum(["HTTP", "SSE", "STDIO"]),
  })).min(1, "At least one URL is required"),
  gitUrl: z.string().url("Please enter a valid Git URL").optional().or(z.literal("")),
  repository: z.string().optional(),
  branch: z.string().optional(),
  repositoryUrl: z.string().optional(),
  mcp_type: z.enum(["SSE", "HTTP", "STDIO"]).optional(),
  env_keys: z.array(z.object({
    key: z.string(),
    description: z.string().optional(),
  })).optional(),
})

// Combined schema for TypeScript types
const mcpServerSchema = z.object({
  name: z.string().min(1, "Server name is required").min(3, "Server name must be at least 3 characters").max(30, "Server name must be less than 30 characters"),
  category: z.enum(["general", "sales", "marketing", "engineering", "finance", "hr"], {
    required_error: "Category is required",
  }),
  description: z.string().optional().refine(val => !val || val.length >= 10 && val.length <= 200, "Description must be at least 10 characters and less than 200 characters if provided"),
  tags: z.string().optional(),
  logo: z.string().optional(),
  repositoryUrl: z.string().optional(),
  repository: z.string().optional(),
  branch: z.string().optional(),
  serverConfigs: z.array(z.object({
    url: z.string(),
    protocol: z.enum(["HTTP", "SSE", "STDIO"]),
  })).optional(),
  gitUrl: z.string().optional(),
  mcp_type: z.enum(["SSE", "HTTP", "STDIO"]).optional(),
  env_keys: z.array(z.object({
    key: z.string(),
    description: z.string().optional(),
  })).optional(),
  component_category: z.string().nullable().optional(),
})

type MCPServerFormData = z.infer<typeof mcpServerSchema>

interface AddMCPServerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const categories = [
  { value: "general", label: "General" },
  { value: "sales", label: "Sales" },
  { value: "marketing", label: "Marketing" },
  { value: "engineering", label: "Engineering" },
  { value: "finance", label: "Finance" },
  { value: "hr", label: "HR" },
]

// Add component category options
const componentCategories = [
  { value: "notifications alerts", label: "Notifications & Alerts" },
  { value: "communication", label: "Communication" },
  { value: "social media", label: "Social Media" },
  { value: "database", label: "Database" },
  { value: "cloud storage", label: "Cloud Storage" },
  { value: "devops system", label: "DevOps System" },
  { value: "file handling", label: "File Handling" },
]

export function AddMCPServerDialog({ open, onOpenChange }: AddMCPServerDialogProps) {
  const [activeTab, setActiveTab] = useState("github")
  const [logoUrl, setLogoUrl] = useState<string>("")
  const [isUploading, setIsUploading] = useState(false)
  const [selectedRepository, setSelectedRepository] = useState<string>("")

  const createMCPServer = useCreateMCPServer()
  const { data: userDetails } = useUserDetails()

  // Check if user has GitHub access token (not null and not empty string)
  const isGitHubConnected = userDetails?.github_access_token != null && userDetails?.github_access_token !== ""

  // Fetch GitHub user details when connected and dialog is open
  const { data: githubUser } = useGitHubUser(
    isGitHubConnected && open
  )

  // Fetch GitHub repositories with infinite scroll when user is connected and dialog is open
  const {
    data: repositoriesData,
    isLoading: isLoadingRepos,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGitHubRepositoriesInfinite(
    {
      sort: "updated",
      direction: "desc",
      page_size: 10
    },
    isGitHubConnected && open
  )

  // Fetch branches for selected repository when dialog is open
  // Extract repo name from full name (e.g., "username/repo" -> "repo")
  const repoName = selectedRepository ? selectedRepository.split('/')[1] : ''
  const { data: branchesData, isLoading: isLoadingBranches } = useGitHubBranches(
    repoName, !!repoName && open
  )

  // Flatten repositories from all pages
  const allRepositories = repositoriesData?.pages.flatMap(page => page.repositories) || []

  const form = useForm<MCPServerFormData>({
    resolver: zodResolver(mcpServerSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      category: "general",
      description: "",
      tags: "",
      logo: "",
      repositoryUrl: "",
      repository: "",
      branch: "",
      serverConfigs: [{ url: "", protocol: "SSE" }],
      gitUrl: "",
      mcp_type: "HTTP",
      env_keys: [],
      component_category: null,
    },
  })

  const formValues = form.watch()

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "serverConfigs",
  })

  const { fields: envKeyFields, append: appendEnvKey, remove: removeEnvKey } = useFieldArray({
    control: form.control,
    name: "env_keys",
  })

  // Determine if the form is valid based on the active tab schema
  const isFormValid = useMemo(() => {
    if (activeTab === "github") {
      return githubSchema.safeParse(formValues).success;
    } else if (activeTab === "hosted") {
      return hostedSchema.safeParse(formValues).success;
    }
    return false;
  }, [formValues, activeTab]); // Re-evaluate when form values or active tab changes

  const onSubmit = async (data: MCPServerFormData) => {
    try {
      // Validate based on active tab using appropriate schema
      let validationResult
      if (activeTab === "github") {
        validationResult = githubSchema.safeParse(data)
        if (!validationResult.success) {
          validationResult.error.errors.forEach(error => {
            toast.error(`${error.path.join('.')}: ${error.message}`)
          })
          return
        }
      } else if (activeTab === "hosted") {
        validationResult = hostedSchema.safeParse(data)
        if (!validationResult.success) {
          validationResult.error.errors.forEach(error => {
            toast.error(`${error.path.join('.')}: ${error.message}`)
          })
          return
        }
      }

      // Prepare the data for API based on deployment type
      const baseData = {
        name: data.name,
        description: data.description || undefined,
        category: data.category as "general" | "sales" | "marketing" | "engineering" | "finance" | "hr",
        tags: data.tags ? data.tags.split(",").map(tag => tag.trim()).filter(tag => tag.length > 0) : undefined,
        logo: logoUrl || undefined,
        visibility: "private" as const,
        status: "active" as const,
        component_category: data.component_category || null,
      }

      let mcpServerData
      if (activeTab === "github") {
        // Find the selected repository to get its html_url
        const selectedRepo = allRepositories.find(repo => repo.full_name === data.repository)
        mcpServerData = {
          ...baseData,
          git_url: selectedRepo?.html_url || `https://github.com/${data.repository}`,
          git_branch: data.branch!,
          mcp_type: data.mcp_type!.toLowerCase() === "http" ? "streamable-http" : data.mcp_type!.toLowerCase() as "sse" | "stdio" | "streamable-http",
          env_keys: data.env_keys && data.env_keys.length > 0 ? data.env_keys : undefined,
        }
      } else {
        // Hosted URLs case
        mcpServerData = {
          ...baseData,
          config: data.serverConfigs!.map(serverConfig => ({
            url: serverConfig.url,
            type: serverConfig.protocol.toLowerCase() === "http" ? "streamable-http" : serverConfig.protocol.toLowerCase() as "sse" | "stdio" | "streamable-http"
          })),
          git_url: data.gitUrl || undefined,
        }
      }

      await createMCPServer.mutateAsync(mcpServerData)

      toast.success("MCP Server created successfully!")
      onOpenChange(false)
      form.reset()
      setLogoUrl("")
      setSelectedRepository("")

      // The MCP list will be automatically refreshed due to query invalidation in the hook
    } catch (error: any) {
      console.error("Error creating MCP server:", error)

      // Show specific error message if available
      const errorMessage = error?.response?.data?.message ||
        error?.message ||
        "Failed to create MCP server. Please try again."
      toast.error(errorMessage)
    }
  }

  const addServerUrl = () => {
    append({ url: "", protocol: "SSE" })
  }

  const handleLogoUpload = (urls: string[]) => {
    if (urls.length > 0) {
      setLogoUrl(urls[0])
      form.setValue("logo", urls[0])
      toast.success("Logo uploaded successfully!")
    }
  }

  const handleLogoUploadingStateChange = (uploading: boolean) => {
    setIsUploading(uploading)
  }

  const handleRemoveLogo = () => {
    setLogoUrl("")
    form.setValue("logo", "")
    toast.success("Logo removed successfully!")
  }

  const handleConnectGitHub = () => {
    if (userDetails?.id) {
      // Construct the GitHub OAuth URL directly and open in browser
      const baseUrl = getApiBaseUrl();
      console.log('Base URL:', baseUrl)
      const githubOAuthUrl = `${baseUrl}/auth/github/?user_id=${userDetails.id}`
      window.location.href = githubOAuthUrl
      // window.open(githubOAuthUrl)
    } else {
      toast.error("Unable to connect to GitHub. Please try refreshing the page.")
    }
  }

  const handleRepositoryChange = (repoFullName: string) => {
    setSelectedRepository(repoFullName)
    form.setValue("repository", repoFullName)

    // Auto-populate server name and description with repository info if empty or previously auto-populated
    const selectedRepo = allRepositories.find(repo => repo.full_name === repoFullName)
    if (selectedRepo) {
      const currentName = form.getValues("name")
      const currentDescription = form.getValues("description")
      // Only auto-populate if name is empty or matches a previous repository name pattern
      if (!currentName || allRepositories.some(repo => repo.name === currentName)) {
        form.setValue("name", selectedRepo.name)
      }
      // Only auto-populate description if empty or matches a previous repo description
      if (
        (!currentDescription || allRepositories.some(repo => repo.description === currentDescription)) &&
        selectedRepo.description
      ) {
        form.setValue("description", selectedRepo.description || "")
  
      }
    }

    // Reset branch when repository changes
    form.setValue("branch", "")
  }

  const handleBranchChange = (branchName: string) => {
    form.setValue("branch", branchName)
  }

  const handleCancel = () => {
    form.reset()
    setLogoUrl("")
    setSelectedRepository("")
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>MCP Metadata</DialogTitle>
          <DialogDescription>
            Add a new MCP server to your platform integrations.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Choose Deployment Method */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Choose Deployment Method</h3>
              <div className="grid grid-cols-2 gap-4">
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${activeTab === "github"
                    ? "border-primary bg-primary/5"
                    : "border-muted hover:border-muted-foreground/50"
                    }`}
                  onClick={() => setActiveTab("github")}
                >
                  <div className="flex items-center gap-3">
                    <Github className="h-6 w-6" />
                    <div>
                      <div className="font-medium">GitHub Repository</div>
                      <div className="text-sm text-muted-foreground">Deploy from your GitHub repo</div>
                    </div>
                  </div>
                </div>

                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${activeTab === "hosted"
                    ? "border-primary bg-primary/5"
                    : "border-muted hover:border-muted-foreground/50"
                    }`}
                  onClick={() => setActiveTab("hosted")}
                >
                  <div className="flex items-center gap-3">
                    <Globe className="h-6 w-6" />
                    <div>
                      <div className="font-medium">Hosted URLs</div>
                      <div className="text-sm text-muted-foreground">Connect existing endpoints</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Conditional Content Based on Tab */}
            {activeTab === "github" && (
              <div className="space-y-4">
                {!isGitHubConnected ? (
                  <div className="flex flex-col items-center justify-center py-8 space-y-4">
                    <div className="text-center space-y-2">
                      <h4 className="text-lg font-medium">Connect to GitHub</h4>
                      <p className="text-sm text-muted-foreground">
                        You need to connect your GitHub account to deploy from repositories
                      </p>
                    </div>
                    <Button
                      type="button"
                      onClick={handleConnectGitHub}
                      className="bg-[#24292e] hover:bg-[#1a1e22] text-white"
                    >
                      <Github className="h-4 w-4 mr-2" />
                      Continue with GitHub
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* GitHub User Display */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium flex items-center gap-2">
                        <User className="h-4 w-4" />
                        GitHub Account
                      </label>
                      {githubUser ? (
                        <div className="flex items-center gap-3 p-3 border rounded-lg bg-muted/30">
                          <img
                            src={githubUser.avatar_url}
                            alt={githubUser.login}
                            className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                          />
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">{githubUser.login}</span>
                            <span className="text-xs text-muted-foreground">{githubUser.name || 'GitHub User'}</span>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center gap-3 p-3 border rounded-lg bg-muted/10">
                          <div className="w-8 h-8 rounded-full bg-muted animate-pulse"></div>
                          <div className="flex flex-col gap-1">
                            <div className="h-4 w-20 bg-muted rounded animate-pulse"></div>
                            <div className="h-3 w-16 bg-muted rounded animate-pulse"></div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Repository and Branch Selection */}
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="repository"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <FolderGit2 className="h-4 w-4" />
                              Repository
                            </FormLabel>
                            <Select
                              onValueChange={handleRepositoryChange}
                              value={field.value}
                              disabled={isLoadingRepos}
                            >
                              <FormControl>
                                <SelectTrigger className="w-full">
                                  <SelectValue
                                    placeholder={isLoadingRepos ? "Loading..." : "Select repository"}
                                    className="truncate"
                                  />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent className="max-h-[200px] w-[var(--radix-select-trigger-width)] max-w-[var(--radix-select-trigger-width)]">
                                {allRepositories.map((repo) => (
                                  <SelectItem key={repo.id} value={repo.full_name}>
                                    <div className="flex items-center gap-2 w-full min-w-0">
                                      <FolderGit2 className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                                      <span className="truncate flex-1">{repo.name}</span>
                                      {repo.private && (
                                        <span className="text-xs bg-muted px-1.5 py-0.5 rounded flex-shrink-0">Private</span>
                                      )}
                                    </div>
                                  </SelectItem>
                                ))}
                                {/* Load More Button */}
                                {hasNextPage && (
                                  <div className="border-t border-border/50 p-2">
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => fetchNextPage()}
                                      disabled={isFetchingNextPage}
                                      className="w-full h-8 text-xs"
                                    >
                                      {isFetchingNextPage ? (
                                        <div className="flex items-center gap-2">
                                          <div className="h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                                          Loading...
                                        </div>
                                      ) : (
                                        "Load More"
                                      )}
                                    </Button>
                                  </div>
                                )}
                                {!hasNextPage && allRepositories.length > 10 && (
                                  <div className="flex items-center justify-center py-2 border-t border-border/50">
                                    <span className="text-xs text-muted-foreground">All repositories loaded</span>
                                  </div>
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="branch"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <GitBranch className="h-4 w-4" />
                              Branch
                            </FormLabel>
                            <Select
                              onValueChange={handleBranchChange}
                              value={field.value}
                              disabled={!selectedRepository || isLoadingBranches}
                            >
                              <FormControl>
                                <SelectTrigger className="w-full">
                                  <SelectValue
                                    placeholder={
                                      !selectedRepository
                                        ? "Select repository first"
                                        : isLoadingBranches
                                          ? "Loading..."
                                          : "Select branch"
                                    }
                                    className="truncate"
                                  />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent className="w-[var(--radix-select-trigger-width)] max-w-[var(--radix-select-trigger-width)]">
                                {branchesData?.branches.map((branch) => (
                                  <SelectItem key={branch.name} value={branch.name}>
                                    <div className="flex items-center gap-2 w-full min-w-0">
                                      <GitBranch className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                                      <span className="truncate flex-1">{branch.name}</span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* MCP Server Information for GitHub tab */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">MCP Server Information</h3>

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Server Name *</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter server name" {...field} maxLength={30} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="category"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Category</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select category" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {categories.map((cat) => (
                                    <SelectItem key={cat.value} value={cat.value}>
                                      {cat.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe your MCP server's functionality"
                                className="min-h-[80px]"
                                {...field}
                                maxLength={200}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* MCP Type and Component Category Row */}
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="mcp_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>MCP Type *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select MCP type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="SSE" disabled>
                                  SSE (Deprecated)
                                </SelectItem>
                                <SelectItem value="HTTP">Streamable HTTP</SelectItem>
                                <SelectItem value="STDIO">STDIO</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="component_category"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Component Category</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value || undefined}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select component category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {componentCategories.map((cat) => (
                                  <SelectItem key={cat.value} value={cat.value}>
                                    {cat.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Environment Variables Section */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="text-base font-medium">Environment Variables (Optional)</h4>
                        <Button type="button" variant="outline" size="sm" onClick={() => appendEnvKey({ key: "", description: "" })}>
                          <Plus className="h-4 w-4 mr-1" />
                          Add Variable
                        </Button>
                      </div>

                      {envKeyFields.map((field, index) => (
                        <div key={field.id} className="flex gap-2">
                          <FormField
                            control={form.control}
                            name={`env_keys.${index}.key`}
                            render={({ field }) => (
                              <FormItem className="flex-1">
                                <FormControl>
                                  <Input
                                    placeholder="ENV_KEY_NAME"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`env_keys.${index}.description`}
                            render={({ field }) => (
                              <FormItem className="flex-1">
                                <FormControl>
                                  <Input
                                    placeholder="Description (optional)"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeEnvKey(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>

                    {/* Tags and Logo for GitHub tab */}
                    <FormField
                      control={form.control}
                      name="tags"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tags</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="ai, automation, workflow (comma separated)"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="space-y-3">
                      <label className="text-sm font-medium">Logo</label>
                      {logoUrl ? (
                        <div className="flex items-center gap-4 p-4 border rounded-lg bg-muted/30">
                          <img src={logoUrl} alt="Logo" className="h-16 w-16 object-contain border rounded-md bg-white" />
                          <div className="flex-1">
                            <div className="text-sm font-medium text-green-600">Logo uploaded successfully</div>
                            <div className="text-xs text-muted-foreground">PNG, JPG formats supported</div>
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleRemoveLogo}
                            className="text-destructive hover:text-destructive"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <FileUpload
                            onUploadSuccess={handleLogoUpload}
                            gcsPathPrefix="mcp-logos/"
                            onUploadingStateChange={handleLogoUploadingStateChange}
                            acceptedFileTypes="image/png,image/jpeg,image/jpg,.png,.jpg,.jpeg"
                            className="min-h-[120px]"
                            customText={{
                              title: "Upload Logo",
                              subtitle: "Drag and drop or click to upload PNG, JPG files"
                            }}
                          />
                          <p className="text-xs text-muted-foreground text-center">
                            Upload a logo for your MCP server (PNG, JPG formats only)
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === "hosted" && (
              <div className="space-y-4">
                {/* Server URLs Section */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-base font-medium">Server URLs</h4>
                    <Button type="button" variant="outline" size="sm" onClick={addServerUrl}>
                      <Plus className="h-4 w-4 mr-1" />
                      Add URL
                    </Button>
                  </div>

                  {fields.map((field, index) => (
                    <div key={field.id} className="flex gap-2">
                      <FormField
                        control={form.control}
                        name={`serverConfigs.${index}.url`}
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <Input
                                placeholder="https://your-mcp-server.com"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`serverConfigs.${index}.protocol`}
                        render={({ field }) => (
                          <FormItem className="w-auto min-w-[150px]">
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="SSE">SSE</SelectItem>
                                <SelectItem value="HTTP">Streamable HTTP</SelectItem>
                                <SelectItem value="STDIO"> STDIO</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => remove(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>

                {/* MCP Server Information for Hosted URLs tab */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">MCP Server Information</h3>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Server Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter server name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {categories.map((cat) => (
                                <SelectItem key={cat.value} value={cat.value}>
                                  {cat.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe your MCP server's functionality"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* Component Category for Hosted tab (after description) */}
                  <FormField
                    control={form.control}
                    name="component_category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Component Category</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value || undefined}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select component category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {componentCategories.map((cat) => (
                              <SelectItem key={cat.value} value={cat.value}>
                                {cat.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Additional Information for Hosted URLs */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Additional Information</h3>

                  <FormField
                    control={form.control}
                    name="tags"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tags</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="ai, automation, workflow (comma separated)"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-3">
                    <label className="text-sm font-medium">Logo</label>
                    {logoUrl ? (
                      <div className="flex items-center gap-4 p-4 border rounded-lg bg-muted/30">
                        <img src={logoUrl} alt="Logo" className="h-16 w-16 object-contain border rounded-md bg-white" />
                        <div className="flex-1">
                          <div className="text-sm font-medium text-green-600">Logo uploaded successfully</div>
                          <div className="text-xs text-muted-foreground">PNG, JPG formats supported</div>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleRemoveLogo}
                          className="text-destructive hover:text-destructive"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <FileUpload
                          onUploadSuccess={handleLogoUpload}
                          gcsPathPrefix="mcp-logos/"
                          onUploadingStateChange={handleLogoUploadingStateChange}
                          acceptedFileTypes="image/png,image/jpeg,image/jpg,.png,.jpg,.jpeg"
                          className="min-h-[120px]"
                          customText={{
                            title: "Upload Logo",
                            subtitle: "Drag and drop or click to upload PNG, JPG files"
                          }}
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Upload a logo for your MCP server (PNG, JPG formats only)
                        </p>
                      </div>
                    )}
                  </div>

                  <FormField
                    control={form.control}
                    name="gitUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Git URL (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://github.com/user/repo (optional)"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            )}

            {/* Deployment Options - Static Cards */}
            <div className="grid grid-cols-3 gap-4">
              <div className="border rounded-lg p-4 transition-colors hover:border-muted-foreground/50">
                <div className="flex items-center gap-2 mb-2">
                  <Globe className="h-5 w-5" />
                  <span className="font-medium">Distribute</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Get your MCP server listed in front of thousands of users.
                </p>
              </div>

              <div className="border rounded-lg p-4 transition-colors hover:border-muted-foreground/50">
                <div className="flex items-center gap-2 mb-2">
                  <Settings className="h-5 w-5" />
                  <span className="font-medium">Host</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Give users install-free access to your MCP server.
                </p>
              </div>

              <div className="border rounded-lg p-4 transition-colors hover:border-muted-foreground/50">
                <div className="flex items-center gap-2 mb-2">
                  <Code className="h-5 w-5" />
                  <span className="font-medium">Build</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Deploy from your repository or connect existing endpoints.
                </p>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createMCPServer.isPending || isUploading || !isFormValid}
              >
                {createMCPServer.isPending
                  ? (activeTab === "github" ? "Deploying..." : "Adding Server...")
                  : (activeTab === "github" ? "Deploy Server" : "Add Server")}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
