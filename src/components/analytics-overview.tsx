"use client"

import { Card, CardContent } from "@/components/ui/card"
import { 
  Activity, 
  Bot, 
  Workflow, 
  CreditCard,
  Package
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useOverview } from "@/hooks/use-overview";
import { useUserDetails } from "@/hooks/use-auth";
import { Skeleton } from "@/components/ui/skeleton";

export function AnalyticsOverview() {
  const { data: userDetails } = useUserDetails();
  const userId = userDetails?.id?.toString();

  const { data: overviewData, isLoading, isError } = useOverview({
    userId: userId || "",
    enabled: !!userId,
  });

  const metrics = [
    {
      title: "Active Agents",
      value: isLoading ? <Skeleton className="h-7 w-16" /> : (isError ? "0" : (overviewData?.active_agents ?? "0").toString()),
      icon: <Bot className="h-4 w-4 text-emerald-500" />,
      description: "Currently deployed"
    },
    {
      title: "Credit Usage",
      value: isLoading ? <Skeleton className="h-7 w-16" /> : (isError ? "0" : `$${(overviewData?.credit_usage ?? 0).toFixed(2)}`),
      icon: <CreditCard className="h-4 w-4 text-amber-500" />,
      description: "This month"
    },
    {
      title: "Agent Requests",
      value: isLoading ? <Skeleton className="h-7 w-16" /> : (isError ? "0" : (overviewData?.agent_requests ?? "0").toString()),
      icon: <Activity className="h-4 w-4 text-violet-500" />,
      description: "Last 30 days"
    },
    {
      title: "Workflow Requests",
      value: isLoading ? <Skeleton className="h-7 w-16" /> : (isError ? "0" : (overviewData?.workflow_requests ?? "0").toString()),
      icon: <Workflow className="h-4 w-4 text-cyan-500" />,
      description: "Last 30 days"
    },
    {
      title: "Custom MCPs",
      value: isLoading ? <Skeleton className="h-7 w-16" /> : (isError ? "0" : (overviewData?.custom_mcps ?? "0").toString()),
      icon: <Package className="h-4 w-4 text-rose-500" />,
      description: "Last 30 days"
    }
  ];

  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
      {metrics.map((metric) => (
        <Card key={metric.title} className="bg-card hover:bg-accent/5 transition-colors">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between space-y-0 pb-2">
              <p className="text-sm font-medium text-muted-foreground">{metric.title}</p>
              {metric.icon}
            </div>
            <div className="space-y-1">
              <h2 className="text-xl sm:text-2xl font-bold">{metric.value}</h2>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">
                  {metric.description}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
