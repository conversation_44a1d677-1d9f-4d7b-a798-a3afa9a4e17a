"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import { CreditCard } from "lucide-react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";


// Sample data for credit usage
const creditUsageData = [
  { date: "Jan 1", amount: 0, limit: 0 },
  { date: "Jan 5", amount: 0, limit: 0 },
  { date: "Jan 10", amount: 0, limit: 0 },
  { date: "Jan 15", amount: 0, limit: 0 },
  { date: "Jan 20", amount: 0, limit: 0 },
  { date: "Jan 25", amount: 0, limit: 0 },
  { date: "Jan 30", amount: 0, limit: 0 },
  { date: "Feb 5", amount: 0, limit: 0 },
  { date: "Feb 10", amount: 0, limit: 0 },
  { date: "Feb 15", amount: 0, limit: 0 },
  { date: "Feb 20", amount: 0, limit: 0 },
  { date: "Feb 25", amount: 0, limit: 0 },
  { date: "Mar 1", amount: 0.00, limit: 0 },
];

// Sample data for credit usage by service
const creditByServiceData = [
  { date: "Jan 1", GPT4: 0, DALL_E: 0, Embeddings: 0 },
  { date: "Jan 10", GPT4: 0, DALL_E: 0, Embeddings: 0 },
  { date: "Jan 20", GPT4: 0, DALL_E: 0, Embeddings: 0 },
  { date: "Jan 30", GPT4: 0, DALL_E: 0, Embeddings: 0 },
  { date: "Feb 10", GPT4: 0, DALL_E: 0, Embeddings: 0 },
  { date: "Feb 20", GPT4: 0, DALL_E: 0, Embeddings: 0 },
  { date: "Mar 1", GPT4: 0, DALL_E: 0, Embeddings: 0 },
];

// Calculate total cost
const totalCost = creditByServiceData.reduce(
  (acc, item) => acc + item.GPT4 + item.DALL_E + item.Embeddings,
  0
);

export function CreditUsageChart() {
  // const [activeTab, setActiveTab] = useState("total");

  return (
    <Card className="col-span-full">
      <CardHeader className="flex flex-row items-start justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Credit Usage Breakdown
          </CardTitle>
          <CardDescription>
            Token consumption and associated costs
          </CardDescription>
        </div>
        <div className="text-right">
          <p className="text-sm text-muted-foreground">Total Cost</p>
          <p className="text-2xl font-bold">${totalCost.toFixed(2)}</p>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="total">
          <TabsList className="mb-4">
            <TabsTrigger value="total">Total Usage</TabsTrigger>
            <TabsTrigger value="byService">By Service</TabsTrigger>
          </TabsList>

          <TabsContent value="total" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={creditUsageData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <defs>
                  <linearGradient id="colorAmount" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#8884d8" stopOpacity={0.1} />
                  </linearGradient>
                  <linearGradient id="colorLimit" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#ff4d4f" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#ff4d4f" stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `$${value}`}
                />
                <Tooltip
                  formatter={(value) => [`$${value}`, undefined]}
                  contentStyle={{
                    backgroundColor: "var(--background)",
                    borderColor: "var(--border)",
                    borderRadius: "6px",
                  }}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="amount"
                  stroke="#8884d8"
                  fillOpacity={1}
                  fill="url(#colorAmount)"
                  name="Usage"
                />
                <Area
                  type="monotone"
                  dataKey="limit"
                  stroke="#ff4d4f"
                  fillOpacity={0}
                  strokeDasharray="5 5"
                  name="Limit"
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="byService" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={creditByServiceData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <defs>
                  <linearGradient id="colorGPT4" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#8884d8" stopOpacity={0.1} />
                  </linearGradient>
                  <linearGradient id="colorDALL_E" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#82ca9d" stopOpacity={0.1} />
                  </linearGradient>
                  <linearGradient
                    id="colorEmbeddings"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop offset="5%" stopColor="#ffc658" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#ffc658" stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `$${value}`}
                />
                <Tooltip
                  formatter={(value) => [`$${value}`, undefined]}
                  contentStyle={{
                    backgroundColor: "var(--background)",
                    borderColor: "var(--border)",
                    borderRadius: "6px",
                  }}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="GPT4"
                  stroke="#8884d8"
                  fillOpacity={1}
                  fill="url(#colorGPT4)"
                  name="GPT-4"
                  stackId="1"
                />
                <Area
                  type="monotone"
                  dataKey="DALL_E"
                  stroke="#82ca9d"
                  fillOpacity={1}
                  fill="url(#colorDALL_E)"
                  name="DALL-E"
                  stackId="1"
                />
                <Area
                  type="monotone"
                  dataKey="Embeddings"
                  stroke="#ffc658"
                  fillOpacity={1}
                  fill="url(#colorEmbeddings)"
                  name="Embeddings"
                  stackId="1"
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
