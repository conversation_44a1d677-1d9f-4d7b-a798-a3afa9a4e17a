"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bo<PERSON>, FileText, Server, Variable, Workflow } from "lucide-react";
import { AgentData } from "../agent-creation-wizard";

interface AgentPreviewProps {
  data: AgentData;
}

export function AgentPreview({ data }: AgentPreviewProps) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">Agent Preview</h2>
        <p className="text-muted-foreground">
          Review your agent configuration before publishing
        </p>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-start gap-4">
          <Avatar className="h-16 w-16 rounded-md">
            {data.image ? (
              <AvatarImage src={data.image} alt={data.name} />
            ) : (
              <AvatarFallback className="rounded-md bg-primary/10 text-primary">
                <Bot className="h-8 w-8" />
              </AvatarFallback>
            )}
          </Avatar>
          <div className="flex-1">
            <CardTitle className="text-xl">{data.name || "Unnamed Agent"}</CardTitle>
            <CardDescription className="mt-1">
              {data.description || "No description provided"}
            </CardDescription>
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge variant="outline">{data.category}</Badge>
              <Badge variant="outline">{data.aiProvider}</Badge>
              <Badge variant="outline">{data.aiModel}</Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Server className="h-4 w-4" />
              MCP Servers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Selected MCP Servers</h4>
                <div className="flex flex-wrap gap-2">
                  {data.mcp_server_ids.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No MCP servers selected</p>
                  ) : (
                    data.mcp_server_ids.map(serverId => (
                      <Badge key={serverId} variant="outline" className="flex items-center gap-1 border-primary/20 bg-primary/10">
                        <Server className="h-3 w-3" />
                        {serverId}
                      </Badge>
                    ))
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Workflow className="h-4 w-4" />
              Workflows & Knowledge
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Connected Workflows</h4>
                <div className="flex flex-wrap gap-2">
                  {data.workflows.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No workflows connected</p>
                  ) : (
                    data.workflows.map(workflow => (
                      <Badge key={workflow} variant="secondary">{workflow}</Badge>
                    ))
                  )}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Knowledge Sources</h4>
                <div className="space-y-2">
                  {data.knowledgeSources.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No knowledge sources added</p>
                  ) : (
                    data.knowledgeSources.map((source, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{source.name}</span>
                      </div>
                    ))
                  )}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Variables</h4>
                <div className="space-y-2">
                  {data.variables.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No variables configured</p>
                  ) : (
                    data.variables.map((variable, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Variable className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{variable.name}: <span className="text-muted-foreground">{variable.type}</span></span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
