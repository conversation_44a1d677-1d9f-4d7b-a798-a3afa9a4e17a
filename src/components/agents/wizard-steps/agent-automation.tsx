"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AgentData } from "../agent-creation-wizard";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Workflow, Search, Plus, X, Server, RefreshCw, ExternalLink } from "lucide-react";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useUserWorkflows } from "@/hooks/use-workflows";
import { useDebounce } from "@/hooks/use-debounce";
import { useMCPServers } from "@/hooks/use-mcp-servers";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetFooter } from "@/components/ui/sheet";
import { Skeleton } from "@/components/ui/skeleton";
import { getWorkflowBuilderUrl } from "@/lib/helpers";

type AgentAutomationProps = {
  data: AgentData;
  updateData: (data: Partial<AgentData>) => void;
};

// No sample workflows - using real API data

export function AgentAutomation({ data, updateData }: AgentAutomationProps) {
  const [workflowSearchQuery, setWorkflowSearchQuery] = useState("");
  const [mcpSearchQuery, setMcpSearchQuery] = useState("");
  const [showMCPServers, setShowMCPServers] = useState(false);
  const [showWorkflows, setShowWorkflows] = useState(false);

  // Debounce search queries
  const debouncedWorkflowSearchQuery = useDebounce(workflowSearchQuery, 2000);
  const debouncedMcpSearchQuery = useDebounce(mcpSearchQuery, 500);

  // Fetch workflows from API with debounced search filter
  const {
    data: workflowsData,
    isLoading: isLoadingWorkflows,
    isError: isWorkflowsError,
    refetch: refetchWorkflows
  } = useUserWorkflows({
    search: debouncedWorkflowSearchQuery || undefined,
    page: 1,
    page_size: 100 // Get more workflows for the agent selection
  });

  // Fetch MCP servers from API
  const {
    data: mcpServersData,
    isLoading: isLoadingMCPServers,
    isError: isMCPServersError,
    refetch: refetchMCPServers
  } = useMCPServers();

  // Map API workflows to UI format
  const apiWorkflows = workflowsData?.data?.map(workflow => ({
    id: workflow.id,
    name: workflow.name,
    description: workflow.description || "No description provided",
  })) || [];

  // No need to filter workflows as the API does it for us
  const filteredWorkflows = apiWorkflows;

  const handleWorkflowToggle = (workflowId: string) => {
    const isSelected = data.workflows.includes(workflowId);
    let updatedWorkflows;

    if (isSelected) {
      updatedWorkflows = data.workflows.filter((id) => id !== workflowId);
    } else {
      updatedWorkflows = [...data.workflows, workflowId];
    }

    updateData({ workflows: updatedWorkflows });
  };

  const handleMCPServerToggle = (serverId: string) => {
    const isSelected = data.mcp_server_ids.includes(serverId);
    let updatedServers;

    if (isSelected) {
      updatedServers = data.mcp_server_ids.filter((id) => id !== serverId);
    } else {
      updatedServers = [...data.mcp_server_ids, serverId];
    }

    updateData({ mcp_server_ids: updatedServers });
  };

  const handleOpenWorkflowBuilder = () => {
    const workflowUrl = getWorkflowBuilderUrl();
    window.open(workflowUrl, "_blank");
    toast.info("Opening workflow builder in a new tab");
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">
          Agent Automation
        </h2>
        <p className="text-muted-foreground">
          Configure automation and integrations for your agent
        </p>
      </div>

      {/* MCP Servers Section */}
      <div className="space-y-6 border-b pb-6">
        <div>
          <h3 className="text-lg font-medium">MCP Servers</h3>
          <p className="text-sm text-muted-foreground">
            Select MCP servers to connect with your agent
          </p>
        </div>

        <div className="flex items-center justify-between mb-4">
          <Button
            variant="outline"
            onClick={() => setShowMCPServers(true)}
            className="flex items-center gap-1"
          >
            <Server className="h-4 w-4" />
            Select Servers
          </Button>
        </div>

        <div className="flex flex-wrap gap-2 mt-2">
          {data.mcp_server_ids.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No MCP servers selected
            </p>
          ) : (
            <>
              {data.mcp_server_ids.map((serverId) => {
                const server = mcpServersData?.data?.find((s) => s.id === serverId);
                return server ? (
                  <Badge
                    key={server.id}
                    variant="outline"
                    className="flex items-center gap-1 border-primary/20 bg-primary/10 max-w-full"
                  >
                    <Server className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">{server.name}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1 flex-shrink-0 hover:bg-destructive/20 hover:text-destructive"
                      onClick={() => handleMCPServerToggle(server.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ) : null;
              })}
            </>
          )}
        </div>

        {/* MCP Servers Sheet */}
        <Sheet open={showMCPServers} onOpenChange={setShowMCPServers}>
          <SheetContent className="w-full sm:max-w-md">
            <SheetHeader className="pb-4">
              <SheetTitle>MCP Servers</SheetTitle>
              <SheetDescription>
                Select MCP servers to connect with your agent
              </SheetDescription>
            </SheetHeader>

            <div className="py-4 px-1 space-y-6">
              <div className="flex items-center gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search MCP servers..."
                    className="pl-8"
                    value={mcpSearchQuery}
                    onChange={(e) => setMcpSearchQuery(e.target.value)}
                  />
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => refetchMCPServers()}
                  title="Refresh MCP servers"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Available MCP Servers</h3>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  onClick={() => window.open("/dashboard/tools", "_blank")}
                >
                  <Plus className="h-3.5 w-3.5" />
                  <span>Create MCP</span>
                  <ExternalLink className="h-3.5 w-3.5 ml-1" />
                </Button>
              </div>

              {isLoadingMCPServers && (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Card key={index} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex items-start gap-2">
                          <Skeleton className="h-4 w-4 mt-1" />
                          <div className="space-y-2 flex-1">
                            <Skeleton className="h-5 w-3/4" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-1/3" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {isMCPServersError && !isLoadingMCPServers && (
                <div className="text-center p-6 border rounded-lg">
                  <p className="text-muted-foreground">
                    Failed to load MCP servers. Please try again.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => refetchMCPServers()}
                  >
                    Retry
                  </Button>
                </div>
              )}

              {!isLoadingMCPServers && !isMCPServersError && mcpServersData && (
                <div className="space-y-4 max-h-[50vh] overflow-y-auto pr-1">
                  {mcpServersData.data
                    .filter(server =>
                      server.name.toLowerCase().includes(debouncedMcpSearchQuery.toLowerCase()) ||
                      (server.description && server.description.toLowerCase().includes(debouncedMcpSearchQuery.toLowerCase()))
                    ).length === 0 ? (
                    <div className="text-center p-6 border rounded-lg">
                      <p className="text-muted-foreground">
                        {mcpServersData.data.length === 0
                          ? "No MCP servers available. Create your first MCP server to get started."
                          : "No MCP servers match your search criteria."}
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {mcpServersData.data
                        .filter(server =>
                          server.name.toLowerCase().includes(debouncedMcpSearchQuery.toLowerCase()) ||
                          (server.description && server.description.toLowerCase().includes(debouncedMcpSearchQuery.toLowerCase()))
                        )
                        .map((server) => (
                          <Card key={server.id} className="overflow-hidden">
                            <CardContent className="p-4">
                              <div className="flex items-start gap-2">
                                <Checkbox
                                  id={`mcp-server-${server.id}`}
                                  checked={data.mcp_server_ids.includes(server.id)}
                                  onCheckedChange={() => handleMCPServerToggle(server.id)}
                                  className="mt-1 flex-shrink-0"
                                />
                                <div className="min-w-0 flex-1">
                                  <Label
                                    htmlFor={`mcp-server-${server.id}`}
                                    className="font-medium cursor-pointer line-clamp-1"
                                  >
                                    {server.name}
                                  </Label>
                                  {server.description && (
                                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                      {server.description}
                                    </p>
                                  )}
                                  <Badge variant="outline" className="mt-2 text-xs">
                                    {server.visibility}
                                  </Badge>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                    </div>
                  )}
                </div>
              )}

              <SheetFooter className="pt-2">
                <Button onClick={() => setShowMCPServers(false)}>Done</Button>
              </SheetFooter>
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {/* Workflows Section */}
      <div className="space-y-6 pt-2">
        <div>
          <h3 className="text-lg font-medium">Workflows</h3>
          <p className="text-sm text-muted-foreground">
            Connect workflows to automate tasks with this agent
          </p>
        </div>

        <div className="flex items-center justify-between mb-4">
          <Button
            variant="outline"
            onClick={() => setShowWorkflows(true)}
            className="flex items-center gap-1"
          >
            <Workflow className="h-4 w-4" />
            Select Workflows
          </Button>
        </div>

        <div className="flex flex-wrap gap-2 mt-2">
          {data.workflows.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No workflows selected
            </p>
          ) : (
            <>
              {data.workflows.map((workflowId) => {
                const workflow = apiWorkflows.find((w) => w.id === workflowId);
                return workflow ? (
                  <Badge
                    key={workflow.id}
                    variant="secondary"
                    className="flex items-center gap-1 max-w-full"
                  >
                    <Workflow className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">{workflow.name}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1 flex-shrink-0 hover:bg-destructive/20 hover:text-destructive"
                      onClick={() => handleWorkflowToggle(workflow.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ) : null;
              })}
            </>
          )}
        </div>

        {/* Workflows Sheet */}
        <Sheet open={showWorkflows} onOpenChange={setShowWorkflows}>
          <SheetContent className="w-full sm:max-w-md">
            <SheetHeader className="pb-4">
              <SheetTitle>Workflows</SheetTitle>
              <SheetDescription>
                Select workflows to connect with your agent
              </SheetDescription>
            </SheetHeader>

            <div className="py-4 px-1 space-y-6">
              <div className="flex items-center gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search workflows..."
                    className="pl-8"
                    value={workflowSearchQuery}
                    onChange={(e) => setWorkflowSearchQuery(e.target.value)}
                  />
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => refetchWorkflows()}
                  title="Refresh workflows"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Available Workflows</h3>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  onClick={handleOpenWorkflowBuilder}
                >
                  <Plus className="h-3.5 w-3.5" />
                  <span>Create Workflow</span>
                  <ExternalLink className="h-3.5 w-3.5 ml-1" />
                </Button>
              </div>

              {isLoadingWorkflows && (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Card key={index} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex items-start gap-2">
                          <Skeleton className="h-4 w-4 mt-1" />
                          <div className="space-y-2 flex-1">
                            <Skeleton className="h-5 w-3/4" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-1/3" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {isWorkflowsError && !isLoadingWorkflows && (
                <div className="text-center p-6 border rounded-lg">
                  <p className="text-muted-foreground">
                    Failed to load workflows. Please try again.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => refetchWorkflows()}
                  >
                    Retry
                  </Button>
                </div>
              )}

              {!isLoadingWorkflows && !isWorkflowsError && (
                <div className="space-y-4 max-h-[50vh] overflow-y-auto pr-1">
                  {filteredWorkflows.length === 0 ? (
                    <div className="text-center p-6 border rounded-lg">
                      <p className="text-muted-foreground">
                        {apiWorkflows.length === 0
                          ? "No workflows available. Create your first workflow to get started."
                          : "No workflows match your search criteria."}
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {filteredWorkflows.map((workflow) => (
                        <Card key={workflow.id} className="overflow-hidden">
                          <CardContent className="p-4">
                            <div className="flex items-start gap-2">
                              <Checkbox
                                id={`workflow-${workflow.id}`}
                                checked={data.workflows.includes(workflow.id)}
                                onCheckedChange={() => handleWorkflowToggle(workflow.id)}
                                className="mt-1 flex-shrink-0"
                              />
                              <div className="min-w-0 flex-1">
                                <Label
                                  htmlFor={`workflow-${workflow.id}`}
                                  className="font-medium cursor-pointer line-clamp-1"
                                >
                                  {workflow.name}
                                </Label>
                                <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                  {workflow.description}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              )}

              <SheetFooter className="pt-2">
                <Button onClick={() => setShowWorkflows(false)}>Done</Button>
              </SheetFooter>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
}
