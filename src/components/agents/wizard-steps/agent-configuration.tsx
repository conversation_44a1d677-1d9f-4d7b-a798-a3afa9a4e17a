"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Edit, Plus, Trash, Variable } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { AgentData } from "../agent-creation-wizard";

interface AgentConfigurationProps {
  data: AgentData;
  updateData: (data: Partial<AgentData>) => void;
}

interface VariableFormData {
  name: string;
  description: string;
  type: "text" | "number" | "json";
  default_value: string | null;
}

export function AgentConfiguration({ data, updateData }: AgentConfigurationProps) {
  const [variableModalOpen, setVariableModalOpen] = useState(false);
  const [editingVariableIndex, setEditingVariableIndex] = useState<number | null>(null);

  // Form for variable modal
  const variableForm = useForm<VariableFormData>({
    defaultValues: {
      name: "",
      description: "",
      type: "text",
      default_value: ""
    }
  });

  // Form for configuration tab
  // const configForm = useForm({
  //   defaultValues: {
  //     apiKey: "••••••••••••••••••••••••••••••",
  //     apiEndpoint: "https://api.ruh.ai/v1/agents/{agent_id}/invoke"
  //   }
  // });

  const handleOpenVariableModal = (index?: number) => {
    if (index !== undefined) {
      // Editing existing variable
      setEditingVariableIndex(index);
      const variable = data.variables[index];
      variableForm.reset({
        name: variable.name,
        description: variable.description,
        type: variable.type,
        default_value: variable.default_value
      });
    } else {
      // Adding new variable
      setEditingVariableIndex(null);
      variableForm.reset({
        name: "",
        description: "",
        type: "text",
        default_value: ""
      });
    }

    setVariableModalOpen(true);
  };

  const handleDeleteVariable = (index: number) => {
    const updatedVariables = [...data.variables];
    updatedVariables.splice(index, 1);
    updateData({ variables: updatedVariables });
    toast.success("Variable deleted");
  };

  const onSubmitVariable = (formData: VariableFormData) => {
    // Process the form data to handle empty default_value
    const processedFormData = {
      ...formData,
      default_value:
        formData.default_value == null || formData.default_value.trim() === ""
          ? null
          : formData.default_value
    };

    if (editingVariableIndex !== null) {
      // Update existing variable
      const updatedVariables = [...data.variables];
      updatedVariables[editingVariableIndex] = processedFormData;
      updateData({ variables: updatedVariables });
      toast.success("Variable updated");
    } else {
      // Add new variable
      updateData({ variables: [...data.variables, processedFormData] });
      toast.success("Variable added");
    }

    setVariableModalOpen(false);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">Agent Configuration</h2>
        <p className="text-muted-foreground">
          Configure variables and settings for your agent
        </p>
      </div>

      <div className="space-y-6">
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={() => handleOpenVariableModal()}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" /> Add Variable
          </Button>
        </div>

        {data.variables.length === 0 ? (
          <div className="text-center p-6 border rounded-lg">
            <Variable className="h-8 w-8 mx-auto text-muted-foreground" />
            <p className="mt-2 text-muted-foreground">No variables configured yet</p>
            <p className="text-sm text-muted-foreground">
              Variables allow your agent to be customized at runtime
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => handleOpenVariableModal()}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Variable
            </Button>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2">
            {data.variables.map((variable, index) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Variable className="h-4 w-4" />
                      {variable.name}
                    </span>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => handleOpenVariableModal(index)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                        onClick={() => handleDeleteVariable(index)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">
                    {variable.description}
                  </p>
                  <div className="flex items-center gap-2 text-xs">
                    <span className="bg-primary/10 text-primary px-2 py-1 rounded-md">
                      {variable.type}
                    </span>
                    <span className="text-muted-foreground">
                      Default: {variable.default_value || "(empty)"}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Variable Modal */}
      <Dialog open={variableModalOpen} onOpenChange={setVariableModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingVariableIndex !== null ? "Edit Variable" : "Add Variable"}
            </DialogTitle>
            <DialogDescription>
              {editingVariableIndex !== null
                ? "Update the details of this variable"
                : "Add a new variable to customize your agent's behavior"}
            </DialogDescription>
          </DialogHeader>

          <Form {...variableForm}>
            <form onSubmit={variableForm.handleSubmit(onSubmitVariable)} className="space-y-4">
              <FormField
                control={variableForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Variable Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., companyName" {...field} />
                    </FormControl>
                    <FormDescription>
                      A unique identifier for this variable
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={variableForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., The name of the company" {...field} />
                    </FormControl>
                    <FormDescription>
                      Explain what this variable is used for
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={variableForm.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Variable Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="text">Text</SelectItem>
                        <SelectItem value="number">Number</SelectItem>
                        <SelectItem value="json">JSON</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The data type of this variable
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={variableForm.control}
                name="default_value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Default Value (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Default value..." {...field} value={field.value ?? ""} />
                    </FormControl>
                    <FormDescription>
                      The default value if none is provided
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setVariableModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {editingVariableIndex !== null ? "Update" : "Add"} Variable
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
