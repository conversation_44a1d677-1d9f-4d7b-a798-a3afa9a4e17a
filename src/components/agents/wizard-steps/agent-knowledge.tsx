"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AgentData } from "../agent-creation-wizard";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  FileText,
  Plus,
  Globe,
  Trash,
} from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import { gcsApi, uploadToGCS } from "@/app/api/gcs";

// Add the uploadAgentFiles property to the Window interface
declare global {
  interface Window {
    uploadAgentFiles?: () => Promise<boolean>;
  }
}

interface AgentKnowledgeProps {
  data: AgentData;
  updateData: (data: Partial<AgentData>) => void;
  onUploadStatusChange?: (isUploading: boolean) => void;
}

export function AgentKnowledge({ data, updateData, onUploadStatusChange }: AgentKnowledgeProps) {
  const [activeTab, setActiveTab] = useState<"document" | "url" | "text">("document");
  const [urlInput, setUrlInput] = useState("");
  const [textName, setTextName] = useState("");
  const [textContent, setTextContent] = useState("");
  const [isUploading, setIsUploading] = useState(false);

  // Notify parent of upload status
  const setUploading = (uploading: boolean) => {
    setIsUploading(uploading);
    if (onUploadStatusChange) onUploadStatusChange(uploading);
  };

  // Handle file selection (not uploading yet)
  const handleFileSelection = (files: File[]) => {
    // Only allow document types (no images)
    const validFiles = Array.from(files).filter(file => {
      const fileType = file.type.toLowerCase();
      return (
        fileType === 'application/pdf' ||
        fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        fileType === 'text/plain'
      );
    });

    if (validFiles.length !== files.length) {
      toast.error("Only PDF, DOCX, and TXT files are supported");
    }
    if (validFiles.length === 0) return;

    // Add the files to the knowledge sources with a temporary name
    const newSources = validFiles.map(file => ({
      type: "document" as const,
      name: file.name,
      content: '', // Will be set after upload
      file: file, // Store the file object for later upload
      status: "pending" as const
    }));

    updateData({
      knowledgeSources: [...data.knowledgeSources, ...newSources],
    });

    // Immediately upload after selection
    uploadPendingFiles([...data.knowledgeSources, ...newSources]);
  };

  // Upload pending files
  const uploadPendingFiles = async (sources: any[]) => {
    const pendingFiles = sources
      .filter(source => source.type === "document" && source.status === "pending" && source.file)
      .map(source => source.file as File);
    if (pendingFiles.length === 0) return;
    try {
      setUploading(true);
      const loadingToast = toast.loading(`Uploading ${pendingFiles.length} file(s)...`);
      const uploadPromises = pendingFiles.map(async (file) => {
        try {
          const presignedUrlResponse = await gcsApi.getPresignedUrl(
            file.name,
            file.type,
            `agent-knowledge/${file.name}`
          );
          const publicUrl = await uploadToGCS(presignedUrlResponse.url, file);
          return { file, url: publicUrl };
        } catch (error) {
          console.error(`Error uploading file ${file.name}:`, error);
          return { file, error: true };
        }
      });
      const results = await Promise.all(uploadPromises);
      const updatedSources = [...sources];
      const newFileUrls: string[] = [];
      results.forEach(({ file, url, error }) => {
        if (!file) return;
        const fileIndex = updatedSources.findIndex(
          (source) => source.type === "document" && source.name === file.name && source.status === "pending"
        );
        if (fileIndex !== -1) {
          if (error) {
            updatedSources[fileIndex] = {
              ...updatedSources[fileIndex],
              status: "error" as const
            };
          } else if (url) {
            updatedSources[fileIndex] = {
              ...updatedSources[fileIndex],
              content: url,
              status: "uploaded" as const
            };
            newFileUrls.push(url);
          }
        }
      });
      updateData({
        knowledgeSources: updatedSources,
        files: [...(data.files || []), ...newFileUrls]
      });
      toast.dismiss(loadingToast);
      const successCount = results.filter(r => r.url).length;
      const errorCount = results.filter(r => r.error).length;
      if (errorCount === 0) {
        toast.success(`Successfully uploaded ${successCount} file(s)`);
      } else if (successCount === 0) {
        toast.error(`Failed to upload ${errorCount} file(s)`);
      } else {
        toast.warning(`Uploaded ${successCount} file(s), but ${errorCount} file(s) failed`);
      }
    } catch (error) {
      console.error("Error during file upload:", error);
      toast.error("An error occurred during file upload");
    } finally {
      setUploading(false);
    }
  };

  // Handle URL addition
  const handleAddUrl = () => {
    if (!urlInput) {
      toast.error("Please enter a URL");
      return;
    }

    // Basic URL validation
    try {
      new URL(urlInput);
    } catch {
      toast.error("Please enter a valid URL");
      return;
    }

    // Add the URL to the knowledge sources
    const newSource = {
      type: "url" as const,
      name: urlInput,
      content: urlInput,
    };

    // Update the data with the new URL
    const newUrls = [...(data.urls || []), urlInput];

    updateData({
      knowledgeSources: [...data.knowledgeSources, newSource],
      urls: newUrls
    });

    setUrlInput("");
    toast.success("URL added to knowledge base");
  };

  // Handle text addition
  const handleAddText = () => {
    if (!textName) {
      toast.error("Please enter a name for this text");
      return;
    }

    if (!textContent) {
      toast.error("Please enter some content");
      return;
    }

    const newSource = {
      type: "text" as const,
      name: textName,
      content: textContent,
    };

    updateData({
      knowledgeSources: [...data.knowledgeSources, newSource],
    });

    setTextName("");
    setTextContent("");
    toast.success("Text added to knowledge base");
  };

  // Handle removing a knowledge source
  const handleRemoveSource = (index: number) => {
    const updatedSources = [...data.knowledgeSources];
    const removedSource = updatedSources[index];

    updatedSources.splice(index, 1);

    // If it's a URL, also remove from the URLs array
    if (removedSource.type === "url") {
      const updatedUrls = (data.urls || []).filter(url => url !== removedSource.content);
      updateData({
        knowledgeSources: updatedSources,
        urls: updatedUrls
      });
    } else {
      updateData({ knowledgeSources: updatedSources });
    }

    toast.success("Knowledge source removed");
  };

  // Remove duplicate window.uploadAgentFiles and redefine as a check only
  window.uploadAgentFiles = async () => {
    // If any file is uploading, block navigation
    if (isUploading) return false;
    // If any file failed, block navigation
    if (data.knowledgeSources.some(s => s.type === 'document' && s.status === 'error')) return false;
    return true;
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">
          Agent Knowledge
        </h2>
        <p className="text-muted-foreground">
          Add knowledge sources to enhance your agent&apos;s capabilities
        </p>
      </div>

      <Tabs
        defaultValue={activeTab}
        onValueChange={(value) => setActiveTab(value as "document" | "url" | "text")}
      >
        <TabsList className="mb-6">
          <TabsTrigger value="document">Document</TabsTrigger>
          <TabsTrigger value="url">URL</TabsTrigger>
          <TabsTrigger value="text">Text</TabsTrigger>
        </TabsList>

        <TabsContent value="document" className="space-y-4">
          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium mb-2">Upload Documents</div>
              <div
                className="border border-dashed rounded-md p-6 text-center cursor-pointer hover:bg-gray-50"
                onClick={() => document.getElementById("file-input")?.click()}
                onDragOver={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                    handleFileSelection(Array.from(e.dataTransfer.files));
                  }
                }}
              >
                <input
                  id="file-input"
                  type="file"
                  multiple
                  className="hidden"
                  accept="application/pdf,.pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.docx,text/plain,.txt"
                  onChange={(e) => {
                    if (e.target.files && e.target.files.length > 0) {
                      handleFileSelection(Array.from(e.target.files));
                      e.target.value = '';
                    }
                  }}
                />
                <FileText className="h-8 w-8 mx-auto text-muted-foreground" />
                <p className="mt-2 text-sm text-muted-foreground">
                  Drag and drop your documents here, or click to browse
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  You can select multiple files
                </p>
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                Supported formats: PDF, DOCX, TXT (max 10MB)
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                Files will be uploaded as soon as you select them
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="url" className="space-y-4">
          <div>
            <div className="text-sm font-medium mb-2">Website URL</div>
            <Input
              id="url-input"
              placeholder="https://example.com/knowledge-page"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
            />
            <p className="text-sm text-muted-foreground mt-1">
              Enter the URL of a webpage containing relevant knowledge
            </p>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleAddUrl}>
              <Plus className="h-4 w-4 mr-2" />
              Add URL
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="text" className="space-y-4">
          <div>
            <div className="text-sm font-medium mb-2">Text Name</div>
            <Input
              id="text-name"
              placeholder="Enter a name for this text"
              value={textName}
              onChange={(e) => setTextName(e.target.value)}
            />
            <p className="text-sm text-muted-foreground mt-1">
              Give your text a descriptive name
            </p>
          </div>

          <div>
            <div className="text-sm font-medium mb-2">Text Content</div>
            <Textarea
              id="text-content"
              placeholder="Enter the text content..."
              className="min-h-[200px]"
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
            />
            <p className="text-sm text-muted-foreground mt-1">
              Enter the text content that you want to add to the agent&apos;s
              knowledge
            </p>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleAddText}>
              <Plus className="h-4 w-4 mr-2" />
              Add Text
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      <div className="pt-6 border-t">
        <h3 className="text-lg font-medium mb-4">Knowledge Sources</h3>

        {data.knowledgeSources.length === 0 ? (
          <div className="text-center p-6 border rounded-lg">
            <p className="text-muted-foreground">
              No knowledge sources added yet
            </p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-4">
            {data.knowledgeSources.map((source, index) => (
              <Card key={index}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    {source.type === "document" ? (
                      <FileText className="h-4 w-4" />
                    ) : source.type === "url" ? (
                      <Globe className="h-4 w-4" />
                    ) : (
                      <FileText className="h-4 w-4" />
                    )}
                    {source.type === 'document' ? (
                      <span
                        style={{
                          color:
                            source.status === 'uploaded'
                              ? 'green'
                              : source.status === 'pending'
                              ? 'gray'
                              : source.status === 'error'
                              ? 'red'
                              : undefined,
                          cursor: source.status === 'uploaded' ? 'pointer' : 'default',
                          textDecoration: source.status === 'uploaded' ? 'underline' : 'none',
                        }}
                        onClick={() => {
                          if (source.status === 'uploaded' && source.content) {
                            window.open(source.content, '_blank');
                          }
                        }}
                      >
                        {source.name}
                      </span>
                    ) : (
                      source.name
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground truncate">
                    {source.type === "text"
                      ? source.content.length > 100
                        ? source.content.substring(0, 100) + "..."
                        : source.content
                      : source.type === 'document'
                        ? source.status === 'error'
                          ? 'Failed to upload'
                          : source.status === 'pending'
                            ? 'Uploading...'
                            : ''
                        : source.content}
                  </p>
                </CardContent>
                <CardFooter className="flex justify-end pt-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    onClick={() => handleRemoveSource(index)}
                  >
                    <Trash className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
