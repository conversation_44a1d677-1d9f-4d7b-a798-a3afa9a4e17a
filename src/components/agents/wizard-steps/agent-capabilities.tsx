"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AgentData } from "../agent-creation-wizard";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Plus,
  X,
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";

interface AgentCapabilitiesProps {
  data: AgentData;
  updateData: (data: Partial<AgentData>) => void;
}

export function AgentCapabilities({
  data,
  updateData,
}: AgentCapabilitiesProps) {
  const [activeTab, setActiveTab] = useState<"capabilities" | "advanced">("capabilities");
  const [capabilityTitle, setCapabilityTitle] = useState("");
  const [capabilityDescription, setCapabilityDescription] = useState("");

  // Handle adding a new capability
  const handleAddCapability = () => {
    if (!capabilityTitle.trim()) {
      toast.error("Please enter a capability title");
      return;
    }

    if (!capabilityDescription.trim()) {
      toast.error("Please enter a capability description");
      return;
    }

    const newCapability = {
      title: capabilityTitle,
      description: capabilityDescription,
    };

    updateData({
      capabilities: [...data.capabilities, newCapability],
    });

    // Reset form
    setCapabilityTitle("");
    setCapabilityDescription("");
    toast.success("Capability added successfully");
  };

  // Handle removing a capability
  const handleRemoveCapability = (index: number) => {
    const updatedCapabilities = [...data.capabilities];
    updatedCapabilities.splice(index, 1);
    updateData({ capabilities: updatedCapabilities });
    toast.success("Capability removed");
  };

  // Handle toggling advanced settings
  const handleToggleAdvancedSetting = (setting: "streaming" | "pushNotifications" | "stateTransitionHistory") => {
    updateData({ [setting]: !data[setting] });
  };

  // Handle adding an example prompt
  const [newExample, setNewExample] = useState("");

  const handleAddExample = () => {
    if (!newExample.trim()) {
      toast.error("Please enter an example prompt");
      return;
    }

    updateData({
      examples: [...data.examples, newExample],
    });

    setNewExample("");
    toast.success("Example prompt added");
  };

  // Handle removing an example prompt
  const handleRemoveExample = (index: number) => {
    const updatedExamples = [...data.examples];
    updatedExamples.splice(index, 1);
    updateData({ examples: updatedExamples });
  };

  // Handle toggling input/output modes
  const handleToggleMode = (mode: string, type: "inputModes" | "outputModes") => {
    const currentModes = data[type];
    let updatedModes;

    if (currentModes.includes(mode)) {
      updatedModes = currentModes.filter((m) => m !== mode);
    } else {
      updatedModes = [...currentModes, mode];
    }

    updateData({ [type]: updatedModes });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">
          Agent Capabilities
        </h2>
        <p className="text-muted-foreground">
          Configure what your agent can do and how it interacts
        </p>
      </div>

      <Tabs
        defaultValue="capabilities"
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as "capabilities" | "advanced")}
      >
        <TabsList className="mb-6">
          <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="capabilities" className="space-y-6">
          {/* Add Capability Form */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Add Capability</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="capability-title">Title</Label>
                  <Input
                    id="capability-title"
                    placeholder="Enter capability title"
                    value={capabilityTitle}
                    onChange={(e) => setCapabilityTitle(e.target.value)}
                    maxLength={50}
                    className="w-full"
                  />
                </div>
                <div>
                  <Label htmlFor="capability-description">Description</Label>
                  <Textarea
                    id="capability-description"
                    placeholder="Describe what this capability does"
                    value={capabilityDescription}
                    onChange={(e) => setCapabilityDescription(e.target.value)}
                    className="resize-none w-full"
                    rows={3}
                    maxLength={200}
                  />
                </div>
                <div className="flex justify-end">
                  <Button
                    onClick={handleAddCapability}
                    className="w-full sm:w-auto"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Capability
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Capabilities List */}
          <div>
            <h3 className="text-sm font-medium mb-4">Agent Capabilities</h3>
            {data.capabilities.length === 0 ? (
              <div className="text-center p-6 border rounded-lg">
                <p className="text-muted-foreground">
                  No capabilities added yet
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Add capabilities to describe what your agent can do
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {data.capabilities.map((capability, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="min-w-0 flex-1 mr-2">
                          <h4 className="font-medium truncate">{capability.title}</h4>
                          <p className="text-sm text-muted-foreground mt-1 line-clamp-3">
                            {capability.description}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 flex-shrink-0 text-destructive hover:bg-destructive/10"
                          onClick={() => handleRemoveCapability(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          {/* Streaming Responses */}
          <div className="flex items-center justify-between py-4 border-b">
            <div>
              <h3 className="font-medium">Streaming Responses</h3>
              <p className="text-sm text-muted-foreground">
                Enable token-by-token streaming for more responsive interactions
              </p>
            </div>
            <Switch
              checked={data.streaming}
              onCheckedChange={() => handleToggleAdvancedSetting("streaming")}
            />
          </div>

          {/* Push Notifications */}
          <div className="flex items-center justify-between py-4 border-b">
            <div>
              <h3 className="font-medium">Push Notifications</h3>
              <p className="text-sm text-muted-foreground">
                Send notifications when the agent completes tasks
              </p>
            </div>
            <Switch
              checked={data.pushNotifications}
              onCheckedChange={() => handleToggleAdvancedSetting("pushNotifications")}
            />
          </div>

          {/* State Transition History */}
          <div className="flex items-center justify-between py-4 border-b">
            <div>
              <h3 className="font-medium">State Transition History</h3>
              <p className="text-sm text-muted-foreground">
                Track and store the agent state transitions for debugging
              </p>
            </div>
            <Switch
              checked={data.stateTransitionHistory}
              onCheckedChange={() => handleToggleAdvancedSetting("stateTransitionHistory")}
            />
          </div>

          {/* Example Prompts */}
          <div className="py-4 border-b">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-4">
              <h3 className="font-medium">Example Prompts</h3>
              <Button variant="outline" size="sm" onClick={handleAddExample}>
                <Plus className="h-4 w-4 mr-1" /> Add Example
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Provide example prompts to help users understand how to interact with your agent
            </p>

            {/* Example Input */}
            <div className="mb-4">
              <Input
                placeholder="How can I help you today?"
                value={newExample}
                onChange={(e) => setNewExample(e.target.value)}
                className="mb-2 w-full"
              />
            </div>

            {/* Example List */}
            <div className="space-y-2">
              {data.examples.map((example, index) => (
                <div key={index} className="flex items-center justify-between bg-muted/30 p-3 rounded-md">
                  <p className="text-sm break-words flex-1 mr-2">{example}</p>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 flex-shrink-0 text-muted-foreground hover:text-destructive"
                    onClick={() => handleRemoveExample(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Input/Output Modes */}
          <div className="py-4">
            <h3 className="font-medium mb-2">Input/Output Modes</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Configure how users can interact with your agent and how it responds
            </p>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8">
              {/* Input Modes */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium mb-3">Input Modes</h4>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Checkbox
                      id="input-text"
                      checked={data.inputModes.includes("text")}
                      onCheckedChange={() => handleToggleMode("text", "inputModes")}
                    />
                    <Label htmlFor="input-text" className="ml-2">Text</Label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="input-voice"
                      checked={data.inputModes.includes("voice")}
                      onCheckedChange={() => handleToggleMode("voice", "inputModes")}
                    />
                    <Label htmlFor="input-voice" className="ml-2">Voice</Label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="input-image"
                      checked={data.inputModes.includes("image")}
                      onCheckedChange={() => handleToggleMode("image", "inputModes")}
                    />
                    <Label htmlFor="input-image" className="ml-2">Image</Label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="input-file"
                      checked={data.inputModes.includes("file_upload")}
                      onCheckedChange={() => handleToggleMode("file_upload", "inputModes")}
                    />
                    <Label htmlFor="input-file" className="ml-2">File Upload</Label>
                  </div>
                </div>
              </div>

              {/* Output Modes */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium mb-3">Output Modes</h4>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Checkbox
                      id="output-text"
                      checked={data.outputModes.includes("text")}
                      onCheckedChange={() => handleToggleMode("text", "outputModes")}
                    />
                    <Label htmlFor="output-text" className="ml-2">Text</Label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="output-voice"
                      checked={data.outputModes.includes("voice")}
                      onCheckedChange={() => handleToggleMode("voice", "outputModes")}
                    />
                    <Label htmlFor="output-voice" className="ml-2">Voice</Label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="output-image"
                      checked={data.outputModes.includes("image")}
                      onCheckedChange={() => handleToggleMode("image", "outputModes")}
                    />
                    <Label htmlFor="output-image" className="ml-2">Image</Label>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id="output-file"
                      checked={data.outputModes.includes("file_generation")}
                      onCheckedChange={() => handleToggleMode("file_generation", "outputModes")}
                    />
                    <Label htmlFor="output-file" className="ml-2">File Generation</Label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
