"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  Bot,
  Check,
  ChevronDown,
  ChevronUp,
  FileText,
  Lightbulb,
  Server,
  Variable,
  Workflow,
  Wrench
} from "lucide-react";
import { useState } from "react";
import { AgentData } from "../agent-creation-wizard";

interface AgentUpdatePreviewProps {
  data: AgentData;
  agentId?: string;
}

export function AgentUpdatePreview({ data }: AgentUpdatePreviewProps) {
  const [systemPromptOpen, setSystemPromptOpen] = useState(false);
  const [selectedToolsOpen, setSelectedToolsOpen] = useState(false);
  const [workflowsOpen, setWorkflowsOpen] = useState(false);

  // Calculate completion status for each section
  const completionStatus = {
    foundation: !!(data.name && data.description && data.category && data.role && data.tone),
    coreLogic: !!(data.systemPrompt && data.aiProvider && data.aiModel),
    capabilities: data.capabilities.length > 0 || data.inputModes.length > 0 || data.outputModes.length > 0,
    automation: data.mcp_server_ids.length > 0 || data.workflows.length > 0,
    knowledge: data.knowledgeSources.length > 0,
    configuration: data.variables.length > 0
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">Agent Preview</h2>
        <p className="text-muted-foreground">
          Review your agent configuration before publishing
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Agent Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Agent Summary</h3>

          <div className="flex flex-col items-center text-center mb-6">
            <Avatar className="h-16 w-16 mb-3">
              {data.image ? (
                <AvatarImage src={data.image} alt={data.name} />
              ) : (
                <AvatarFallback className="bg-primary/10 text-primary text-lg font-semibold">
                  {data.name ? data.name.substring(0, 2).toUpperCase() : "SA"}
                </AvatarFallback>
              )}
            </Avatar>

            <h4 className="text-lg font-semibold">{data.name || "Sample Agent agentJ"}</h4>
            <Badge variant="secondary" className="mb-2">{data.category || "General"}</Badge>
            <p className="text-sm text-muted-foreground">
              {data.description || "This is a sample agent for demonstration purposes"}
            </p>
          </div>

          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">AI Provider</span>
              <span className="font-medium">{data.aiProvider || "OpenAI"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">AI Model</span>
              <span className="font-medium">{data.aiModel || "gpt-4o"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Tools</span>
              <span className="font-medium">{data.mcp_server_ids.length} selected</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Workflows</span>
              <span className="font-medium">{data.workflows.length} selected</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Knowledge Sources</span>
              <span className="font-medium">{data.knowledgeSources.length} added</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Variables</span>
              <span className="font-medium">{data.variables.length} defined</span>
            </div>
          </div>
        </Card>

        {/* Completion Checklist */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Completion Checklist</h3>
          </div>

          <div className="grid grid-cols-2 gap-3 mb-6">
            <div className="flex items-center gap-2">
              <Check className={`h-4 w-4 ${completionStatus.foundation ? 'text-green-600' : 'text-muted-foreground'}`} />
              <Bot className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Foundation</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className={`h-4 w-4 ${completionStatus.coreLogic ? 'text-green-600' : 'text-muted-foreground'}`} />
              <Lightbulb className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Core Logic</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className={`h-4 w-4 ${completionStatus.capabilities ? 'text-green-600' : 'text-muted-foreground'}`} />
              <Wrench className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Capabilities</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className={`h-4 w-4 ${completionStatus.automation ? 'text-green-600' : 'text-muted-foreground'}`} />
              <Workflow className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Automation</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className={`h-4 w-4 ${completionStatus.knowledge ? 'text-green-600' : 'text-muted-foreground'}`} />
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Knowledge</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className={`h-4 w-4 ${completionStatus.configuration ? 'text-green-600' : 'text-muted-foreground'}`} />
              <Variable className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Configuration</span>
            </div>
          </div>

          {/* Collapsible Sections */}
          <div className="space-y-3">
            {/* System Prompt */}
            <Collapsible open={systemPromptOpen} onOpenChange={setSystemPromptOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                  <span className="text-sm font-medium">System Prompt</span>
                  {systemPromptOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <div className="bg-muted p-3 rounded-md text-sm font-mono">
                  {data.systemPrompt || "You are a helpful assistant."}
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Selected Tools */}
            <Collapsible open={selectedToolsOpen} onOpenChange={setSelectedToolsOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                  <span className="text-sm font-medium">Selected Tools</span>
                  {selectedToolsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <div className="flex flex-wrap gap-2">
                  {data.mcp_server_ids.length === 0 ? (
                    <span className="text-sm text-muted-foreground">No tools selected</span>
                  ) : (
                    data.mcp_server_ids.map((serverId, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {serverId}
                      </Badge>
                    ))
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Workflows */}
            <Collapsible open={workflowsOpen} onOpenChange={setWorkflowsOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                  <span className="text-sm font-medium">Workflows</span>
                  {workflowsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <div className="flex flex-wrap gap-2">
                  {data.workflows.length === 0 ? (
                    <span className="text-sm text-muted-foreground">No workflows selected</span>
                  ) : (
                    data.workflows.map((workflow, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {workflow}
                      </Badge>
                    ))
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </Card>
      </div>
    </div>
  );
}
