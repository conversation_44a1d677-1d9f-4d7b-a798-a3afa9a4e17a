"use client";

import { useState, useEffect } from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  Form,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { AgentData } from "../agent-creation-wizard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Bot, ImageIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";
import { AvatarSelectionDialog } from "../avatar-selection-dialog";
import { AgentAvatar } from "@/services/agent-avatars";

interface AgentFoundationProps {
  data: AgentData;
  updateData: (data: Partial<AgentData>) => void;
}

// Define validation schema
const foundationSchema = z.object({
  name: z.string().min(1, "Agent name is required").max(20, "Name should be of max. 20 characters"),
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  role: z.string().min(1, "Role is required").max(20, "Role should be of max. 20 characters"),
  tone: z.string().min(1, "Tone is required"),
});

type FoundationFormValues = z.infer<typeof foundationSchema>;

export function AgentFoundation({ data, updateData }: AgentFoundationProps) {
  const [isAvatarDialogOpen, setIsAvatarDialogOpen] = useState(false);

  const form = useForm<FoundationFormValues>({
    resolver: zodResolver(foundationSchema),
    defaultValues: {
      name: data.name,
      description: data.description,
      category: data.category,
      role: data.role,
      tone: data.tone,
    },
  });

  const handleSelectAvatar = (avatar: AgentAvatar) => {
    updateData({ image: avatar.url });
    setIsAvatarDialogOpen(false);
    toast.success("Avatar selected successfully");
  };

  // Update parent component with current form values
  const updateParentData = () => {
    const values = form.getValues();
    updateData({
      name: values.name,
      description: values.description,
      category: values.category,
      role: values.role,
      tone: values.tone,
    });
  };

  // Update parent data when form values change
  useEffect(() => {
    // Set up a subscription to form changes
    const subscription = form.watch(() => {
      // Don't update on every keystroke, only when the form is valid
      if (form.formState.isValid) {
        // Use setTimeout to debounce updates
        const timeoutId = setTimeout(() => {
          updateParentData();
        }, 500); // 500ms debounce

        return () => clearTimeout(timeoutId);
      }
    });

    // Cleanup subscription
    return () => subscription.unsubscribe();
  }, [form]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">
          Agent Foundation
        </h2>
        <p className="text-muted-foreground">
          Set up the basic information for your AI agent
        </p>
      </div>

      <Form {...form}>
        <form
          onKeyDown={(e) => {
            // Prevent form submission on Enter key press in input fields
            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
              e.preventDefault();
            }
          }}
          className="space-y-6"
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="max-w-md">
                <FormLabel>Agent Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Customer Support Assistant"
                    maxLength={20}
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      updateParentData();
                    }}
                  />
                </FormControl>
                <FormDescription className="flex justify-between">
                  <span>A clear, descriptive name for your agent</span>
                  <span className="text-xs text-muted-foreground">
                    {field.value?.length || 0}/20
                  </span>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem className="max-w-2xl">
                <FormLabel>Agent Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="This agent helps customers with support requests and guides them through troubleshooting steps..."
                    className="min-h-[100px] resize-none"
                    maxLength={500}
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      updateParentData();
                    }}
                  />
                </FormControl>
                <FormDescription className="flex justify-between">
                  <span>Describe what the agent does and its purpose</span>
                  <span className="text-xs text-muted-foreground">
                    {field.value?.length || 0}/500
                  </span>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem className="max-w-md">
                <FormLabel>Agent Role</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Customer Support Specialist"
                    maxLength={20}
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      updateParentData();
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Define the specific role of your agent
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tone"
            render={({ field }) => (
              <FormItem className="max-w-md">
                <FormLabel>Agent Tone</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    updateParentData();
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a tone" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="friendly">Friendly</SelectItem>
                    <SelectItem value="casual">Casual</SelectItem>
                    <SelectItem value="formal">Formal</SelectItem>
                    <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Choose the tone of voice for your agent
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem className="max-w-md">
                <FormLabel>Agent Category</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    updateParentData();
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="engineering">Engineering</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="customer_support">Customer Support</SelectItem>
                    <SelectItem value="human_resources">Human Resources</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="operations">Operations</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Categorize your agent for better organization
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="max-w-2xl">
            <FormLabel>Agent Avatar</FormLabel>
            <div className="flex flex-col sm:flex-row items-start gap-6 mt-3 p-4 border rounded-lg bg-muted/20">
              <div className="flex-shrink-0">
                <Avatar
                  className="h-24 w-24 rounded-md border shadow-sm cursor-pointer hover:ring-2 hover:ring-primary/50 transition-all"
                  onClick={() => setIsAvatarDialogOpen(true)}
                >
                  {data.image ? (
                    <AvatarImage src={data.image} alt="Agent avatar" />
                  ) : (
                    <AvatarFallback className="rounded-md bg-primary/10 text-primary">
                      <Bot className="h-12 w-12" />
                    </AvatarFallback>
                  )}
                </Avatar>
              </div>

              <div className="space-y-4 flex-1 w-full">
                <div className="flex flex-col">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="w-fit"
                    onClick={() => setIsAvatarDialogOpen(true)}
                  >
                    <ImageIcon className="h-4 w-4 mr-2" /> Select an Avatar
                  </Button>
                  <p className="text-xs text-muted-foreground mt-2">
                    Choose from our collection of pre-designed agent avatars
                  </p>
                </div>
              </div>
            </div>
          </div>

          <AvatarSelectionDialog
            open={isAvatarDialogOpen}
            onOpenChange={setIsAvatarDialogOpen}
            onSelectAvatar={handleSelectAvatar}
            selectedAvatarUrl={data.image}
          />
        </form>
      </Form>
    </div>
  );
}
