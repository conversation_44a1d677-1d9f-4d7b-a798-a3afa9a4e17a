"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  CheckCircle2,
  Bot,
  Lightbulb,
  Wrench,
  Workflow,
  FileText,
  Variable,
  Info,
} from "lucide-react";

// Step components
import { AgentFoundation } from "./wizard-steps/agent-foundation";
import { AgentCoreLogic } from "./wizard-steps/agent-core-logic";
import { AgentCapabilities } from "./wizard-steps/agent-capabilities";
import { AgentAutomation } from "./wizard-steps/agent-automation";
import { AgentKnowledge } from "./wizard-steps/agent-knowledge";
import { AgentConfiguration } from "./wizard-steps/agent-configuration";
import { AgentUpdatePreview } from "./wizard-steps/agent-update-preview";
import { AgentData } from "./agent-creation-wizard";

// Add a simple loading spinner component or use a div
const LoadingSpinner = () => (
  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
);

const steps = [
  { id: "foundation", label: "Foundation", icon: <Bot className="h-5 w-5" /> },
  {
    id: "core-logic",
    label: "Core Logic",
    icon: <Lightbulb className="h-5 w-5" />,
  },
  {
    id: "capabilities",
    label: "Capabilities",
    icon: <Wrench className="h-5 w-5" />,
  },
  {
    id: "automation",
    label: "Automation",
    icon: <Workflow className="h-5 w-5" />,
  },
  {
    id: "knowledge",
    label: "Knowledge",
    icon: <FileText className="h-5 w-5" />,
  },
  {
    id: "configuration",
    label: "Configuration",
    icon: <Variable className="h-5 w-5" />,
  },
  { id: "preview", label: "Preview", icon: <Info className="h-5 w-5" /> },
];

interface AgentUpdateWizardProps {
  agentId: string;
  currentStep: string;
  setCurrentStep: (step: string) => void;
}

export function AgentUpdateWizard({
  agentId,
  currentStep,
  setCurrentStep,
}: AgentUpdateWizardProps) {
  const router = useRouter();
  // Initialize stepIndex from URL param on mount
  const getStepIndexFromUrl = () => {
    if (typeof window === 'undefined') return 0;
    const params = new URLSearchParams(window.location.search);
    const stepParam = params.get('step');
    const idx = steps.findIndex((step) => step.id === stepParam);
    return idx >= 0 ? idx : 0;
  };
  const [stepIndex, setStepIndex] = useState(getStepIndexFromUrl);
  const [isLoading, setIsLoading] = useState(false);
  const [isKnowledgeUploading, setIsKnowledgeUploading] = useState(false);

  // Keep stepIndex in sync with currentStep prop (from parent)
  useEffect(() => {
    const idx = steps.findIndex((step) => step.id === currentStep);
    if (idx >= 0 && idx !== stepIndex) {
      setStepIndex(idx);
    }
    // Only run when currentStep changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentStep]);

  // Update URL and parent state when stepIndex changes, but only if needed
  useEffect(() => {
    const stepId = steps[stepIndex].id;
    const params = new URLSearchParams(window.location.search);
    if (params.get('step') !== stepId) {
      params.set('step', stepId);
      window.history.replaceState(
        {},
        '',
        `${window.location.pathname}?${params.toString()}`
      );
    }
    if (currentStep !== stepId) {
      setCurrentStep(stepId);
    }
    // Only run when stepIndex changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stepIndex]);

  const [agentData, setAgentData] = useState<AgentData>({
    // Foundation
    name: "",
    description: "",
    image: null,
    category: "general",
    role: "",
    tone: "professional",

    // Core Logic
    systemPrompt: "",
    aiProvider: "OpenAI",
    aiModel: "gpt-4o",
    apiKeyId: null,
    ruhCredentials: false,

    // Capabilities
    selectedTools: [],
    mcp_server_ids: [],
    capabilities: [],

    // Advanced capabilities
    streaming: false,
    pushNotifications: false,
    stateTransitionHistory: false,
    examples: [],
    inputModes: ["text"],
    outputModes: ["text"],

    // Automation
    workflows: [],

    // Knowledge
    knowledgeSources: [],

    // Configuration
    variables: [],
  });

  // Load agent data when component mounts
  useEffect(() => {
    const loadAgentData = async () => {
      if (!agentId) return;

      try {
        setIsLoading(true);
        // Import the agent service
        const { agentService } = await import("@/services/agent-service");

        // Show loading toast
        toast.loading("Loading agent data...", {
          id: "agent-loading",
        });

        // Fetch agent data
        const response = await agentService.getAgentById(agentId);

        // Dismiss loading toast
        toast.dismiss("agent-loading");

        if (response.success) {
          const agent = response.agent;

          // Map API response to our agent data structure
          updateAgentData({
            name: agent.name,
            description: agent.description,
            image: agent.avatar,
            category: agent.category || "general",
            role: agent.agent_topic_type || "",
            tone: agent.tone || "professional",
            systemPrompt: agent.system_message,
            aiProvider: agent.model_provider || "OpenAI",
            aiModel: agent.model_name || "gpt-4o",
            apiKeyId: agent.model_api_key || null,
            ruhCredentials: agent.ruh_credentials || false,
            workflows: agent.workflow_ids || [],
            mcp_server_ids: agent.mcp_server_ids || [],
            // Map capabilities data
            capabilities: agent.agent_capabilities?.capabilities || [],
            inputModes: agent.agent_capabilities?.input_modes || ["text"],
            outputModes: agent.agent_capabilities?.output_modes || ["text"],
            streaming: agent.agent_capabilities?.response_model?.includes("streaming_response") || false,
            pushNotifications: agent.agent_capabilities?.response_model?.includes("push_notification") || false,
            stateTransitionHistory: agent.agent_capabilities?.response_model?.includes("state_transition_history") || false,
            examples: agent.example_prompts || [],
            // Map knowledge data
            knowledgeSources: [
              ...(agent.files || []).map((file: string) => ({
                type: 'document' as const,
                content: file,
                name: file.split('/').pop() || 'Unknown file',
                status: 'uploaded' as const
              })),
              ...(agent.urls || []).map((url: string) => ({
                type: 'url' as const,
                content: url,
                name: url,
                status: 'uploaded' as const
              }))
            ],
            // Map variables with proper type conversion
            variables: (agent.variables || []).map(variable => ({
              name: variable.name,
              description: variable.description,
              type: (variable.type === 'text' || variable.type === 'number' || variable.type === 'json')
                ? variable.type
                : 'text' as const,
              default_value: variable.default_value || null,
            })),
            // Add visibility and marketplace sync status
            visibility: agent.visibility,
            isChangesMarketplace: agent.is_changes_marketplace,
          });

          toast.success("Agent data loaded successfully");
        } else {
          toast.error("Failed to load agent data");
        }
      } catch (error) {
        toast.dismiss("agent-loading");
        console.error("Error loading agent data:", error);
        toast.error("Failed to load agent data: " +
          (error instanceof Error ? error.message : "Unknown error"));
      } finally {
        setIsLoading(false);
      }
    };

    loadAgentData();
  }, [agentId]);

  const updateAgentData = (data: Partial<AgentData>) => {
    setAgentData((prev) => ({ ...prev, ...data }));
  };

  // Function to refresh agent data from the server
  const refreshAgentData = async () => {
    if (!agentId) return;

    try {
      // Import the agent service
      const { agentService } = await import("@/services/agent-service");

      // Fetch latest agent data
      const response = await agentService.getAgentById(agentId);

      if (response.success) {
        const agent = response.agent;

        // Map API response to our agent data structure
        updateAgentData({
          name: agent.name,
          description: agent.description,
          image: agent.avatar,
          category: agent.category || "general",
          role: agent.agent_topic_type || "",
          tone: agent.tone || "professional",
          systemPrompt: agent.system_message,
          aiProvider: agent.model_provider || "OpenAI",
          aiModel: agent.model_name || "gpt-4o",
          apiKeyId: agent.model_api_key || null,
          ruhCredentials: agent.ruh_credentials || false,
          workflows: agent.workflow_ids || [],
          mcp_server_ids: agent.mcp_server_ids || [],
          // Map capabilities data
          capabilities: agent.agent_capabilities?.capabilities || [],
          inputModes: agent.agent_capabilities?.input_modes || ["text"],
          outputModes: agent.agent_capabilities?.output_modes || ["text"],
          streaming: agent.agent_capabilities?.response_model?.includes("streaming_response") || false,
          pushNotifications: agent.agent_capabilities?.response_model?.includes("push_notification") || false,
          stateTransitionHistory: agent.agent_capabilities?.response_model?.includes("state_transition_history") || false,
          examples: agent.example_prompts || [],
          // Map knowledge data
          knowledgeSources: [
            ...(agent.files || []).map((file: string) => ({
              type: 'document' as const,
              content: file,
              name: file.split('/').pop() || 'Unknown file',
              status: 'uploaded' as const
            })),
            ...(agent.urls || []).map((url: string) => ({
              type: 'url' as const,
              content: url,
              name: url,
              status: 'uploaded' as const
            }))
          ],
          // Map variables with proper type conversion
          variables: (agent.variables || []).map(variable => ({
            name: variable.name,
            description: variable.description,
            type: (variable.type === 'text' || variable.type === 'number' || variable.type === 'json')
              ? variable.type
              : 'text' as const,
            default_value: variable.default_value || null,
          })),
          // Add visibility and marketplace sync status
          visibility: agent.visibility,
          isChangesMarketplace: agent.is_changes_marketplace,
        });

        console.log("Agent data refreshed successfully");
      } else {
        console.error("Failed to refresh agent data:", response.message);
      }
    } catch (error) {
      console.error("Error refreshing agent data:", error);
    }
  };

  // Validate the current step
  const validateCurrentStep = (): boolean => {
    switch (steps[stepIndex].id) {
      case "foundation":
        if (!agentData.name || agentData.name.trim() === "") {
          toast.error("Please enter an agent name");
          return false;
        }
        if (!agentData.description || agentData.description.trim() === "") {
          toast.error("Please enter an agent description");
          return false;
        }
        if (!agentData.role || agentData.role.trim() === "") {
          toast.error("Please enter an agent role");
          return false;
        }
        if (!agentData.tone || agentData.tone.trim() === "") {
          toast.error("Please select an agent tone");
          return false;
        }
        if (!agentData.category || agentData.category.trim() === "") {
          toast.error("Please select an agent category");
          return false;
        }
        return true;

      case "core-logic":
        if (!agentData.systemPrompt || agentData.systemPrompt.trim() === "") {
          toast.error("Please enter a system prompt");
          return false;
        }
        return true;

      case "capabilities":
        // No validation required for MCP servers
        return true;

      case "automation":
        // No validation required for workflows
        return true;

      // Other steps are optional
      default:
        return true;
    }
  };

  // Update functions for each section
  const updateFoundation = async () => {
    console.log("updateFoundation called");
    if (!validateCurrentStep()) {
      console.log("Foundation validation failed");
      return;
    }

    try {
      setIsLoading(true);
      console.log("Loading agent update service...");
      const { agentUpdateService } = await import("@/services/agent-update-service");

      const payload = {
        name: agentData.name,
        description: agentData.description,
        department: "IT", // Default department
        tone: agentData.tone, // Use the selected tone
        agent_topic_type: agentData.role, // Include the role as agent_topic_type
        category: agentData.category, // Include the category
        ruh_credentials: false // Default value
      };

      console.log("Updating foundation with payload:", payload);
      console.log("Agent ID:", agentId);

      const response = await agentUpdateService.updateCoreDetails(agentId, payload);
      console.log("Foundation update response:", response);

      if (response.success) {
        toast.success("Foundation details updated successfully");
        // Refresh agent data to show latest changes
        await refreshAgentData();
      } else {
        toast.error(`Failed to update foundation details: ${response.message}`);
      }
    } catch (error) {
      console.error("Error updating foundation:", error);
      toast.error("Failed to update foundation details: " +
        (error instanceof Error ? error.message : "Unknown error"));
    } finally {
      setIsLoading(false);
    }
  };

  const updateCoreLogic = async () => {
    if (!validateCurrentStep()) return;

    try {
      setIsLoading(true);
      const { agentUpdateService } = await import("@/services/agent-update-service");

      const payload = {
        system_message: agentData.systemPrompt,
        model_provider: agentData.ruhCredentials ? null : (agentData.aiProvider || null),
        model_name: agentData.ruhCredentials ? null : (agentData.aiModel || null),
        model_api_key: agentData.ruhCredentials ? null : agentData.apiKeyId,
        ruh_credentials: agentData.ruhCredentials,
      };

      const response = await agentUpdateService.updateCoreDetails(agentId, payload);

      if (response.success) {
        toast.success("Core logic updated successfully");
        // Refresh agent data to show latest changes
        await refreshAgentData();
      } else {
        toast.error(`Failed to update core logic: ${response.message}`);
      }
    } catch (error) {
      console.error("Error updating core logic:", error);
      toast.error("Failed to update core logic: " +
        (error instanceof Error ? error.message : "Unknown error"));
    } finally {
      setIsLoading(false);
    }
  };

  const updateCapabilities = async () => {
    if (!validateCurrentStep()) return;

    try {
      setIsLoading(true);
      const { agentUpdateService } = await import("@/services/agent-update-service");

      // Prepare response model array based on toggles
      const responseModel: string[] = [];
      if (agentData.streaming) {
        responseModel.push("streaming_response");
      }
      if (agentData.pushNotifications) {
        responseModel.push("push_notification");
      }
      if (agentData.stateTransitionHistory) {
        responseModel.push("state_transition_history");
      }

      // Update capabilities using the new endpoint structure
      const payload = {
        capabilities: agentData.capabilities,
        input_modes: agentData.inputModes,
        output_modes: agentData.outputModes,
        response_model: responseModel
      };

      // Call the API to update capabilities
      const response = await agentUpdateService.updateCapabilities(agentId, payload);

      if (response.success) {
        toast.success("Capabilities updated successfully");
        // Refresh agent data to show latest changes
        await refreshAgentData();
      } else {
        toast.error(`Failed to update capabilities: ${response.message}`);
      }
    } catch (error) {
      console.error("Error updating capabilities:", error);
      toast.error("Failed to update capabilities: " +
        (error instanceof Error ? error.message : "Unknown error"));
    } finally {
      setIsLoading(false);
    }
  };

  const updateAutomation = async () => {
    if (!validateCurrentStep()) return;

    try {
      setIsLoading(true);
      const { agentUpdateService } = await import("@/services/agent-update-service");

      // First update workflows
      const workflowsPayload = {
        workflow_ids: agentData.workflows,
      };

      const workflowsResponse = await agentUpdateService.updateWorkflows(agentId, workflowsPayload);

      // Then update MCP servers
      const mcpPayload = {
        mcp_server_ids: agentData.mcp_server_ids,
      };

      const mcpResponse = await agentUpdateService.updateMCPServers(agentId, mcpPayload);

      if (workflowsResponse.success && mcpResponse.success) {
        toast.success("Automation updated successfully");
        // Refresh agent data to show latest changes
        await refreshAgentData();
      } else {
        if (!workflowsResponse.success) {
          toast.error(`Failed to update workflows: ${workflowsResponse.message}`);
        }
        if (!mcpResponse.success) {
          toast.error(`Failed to update MCP servers: ${mcpResponse.message}`);
        }
      }
    } catch (error) {
      console.error("Error updating automation:", error);
      toast.error("Failed to update automation: " +
        (error instanceof Error ? error.message : "Unknown error"));
    } finally {
      setIsLoading(false);
    }
  };

  const updateKnowledge = async () => {
    console.log("updateKnowledge called");
    if (!validateCurrentStep()) {
      console.log("Knowledge validation failed");
      return;
    }

    // Prevent updating if files are uploading or have errors
    if (isKnowledgeUploading || agentData.knowledgeSources.some(s => s.type === 'document' && s.status === 'error')) {
        toast.error("Please wait for files to finish uploading or resolve errors before updating.");
        return;
    }

    try {
      setIsLoading(true);
      console.log("Loading agent update service...");
      const { agentUpdateService } = await import("@/services/agent-update-service");

      // Separate files and URLs from knowledge sources
      const files: string[] = [];
      const urls: string[] = [];

      // Process knowledge sources to extract files and URLs
      for (const source of agentData.knowledgeSources) {
        if (source.type === 'document' && source.file) {
          // Handle file upload - generate presigned URL and upload
          try {
            // Import GCS upload functions
            const { gcsApi, uploadToGCS } = await import("@/app/api/gcs");

            // Generate presigned URL and upload file
            const presignedUrlResponse = await gcsApi.getPresignedUrl(
              source.file.name,
              source.file.type,
              `agent-knowledge/${source.file.name}`
            );
            const uploadedUrl = await uploadToGCS(presignedUrlResponse.url, source.file);
            files.push(uploadedUrl);
          } catch (uploadError) {
            console.error("Error uploading file:", uploadError);
            toast.error(`Failed to upload file: ${source.name}`);
            return;
          }
        } else if (source.type === 'document' && source.content) {
          // File already uploaded (existing file)
          files.push(source.content);
        } else if (source.type === 'url') {
          // Direct URL
          urls.push(source.content);
        }
      }

      // Also include existing files and URLs from agent data
      if (agentData.files) {
        files.push(...agentData.files);
      }
      if (agentData.urls) {
        urls.push(...agentData.urls);
      }

      const payload = {
        files: [...new Set(files)], // Remove duplicates
        urls: [...new Set(urls)], // Remove duplicates
      };

      const response = await agentUpdateService.updateKnowledge(agentId, payload);

      if (response.success) {
        toast.success("Knowledge sources updated successfully");
        // Refresh agent data to show latest changes
        await refreshAgentData();
      } else {
        toast.error(`Failed to update knowledge sources: ${response.message}`);
      }
    } catch (error) {
      console.error("Error updating knowledge sources:", error);
      toast.error("Failed to update knowledge sources: " +
        (error instanceof Error ? error.message : "Unknown error"));
    } finally {
      setIsLoading(false);
    }
  };

  const updateConfiguration = async () => {
    if (!validateCurrentStep()) return;

    try {
      setIsLoading(true);

      // Import the agent update service
      const { agentUpdateService } = await import("@/services/agent-update-service");

      const payload = {
        variables: agentData.variables.map(variable => ({
          name: variable.name,
          description: variable.description,
          type: variable.type,
          default_value: variable.default_value,
        })),
      };

      const response = await agentUpdateService.updateVariables(agentId, payload);

      if (response.success) {
        toast.success("Variables updated successfully");
        // Refresh agent data to show latest changes
        await refreshAgentData();
      } else {
        toast.error(`Failed to update variables: ${response.message}`);
      }

    } catch (error) {
      console.error("Error updating configuration:", error);
      toast.error("Failed to update variables: " +
        (error instanceof Error ? error.message : "Unknown error"));
    } finally {
      setIsLoading(false);
    }
  };

  // Function to get the appropriate update function for the current step
  const getCurrentUpdateFunction = () => {
    switch (steps[stepIndex].id) {
      case "foundation":
        return updateFoundation;
      case "core-logic":
        return updateCoreLogic;
      case "capabilities":
        return updateCapabilities;
      case "automation":
        return updateAutomation;
      case "knowledge":
        return updateKnowledge;
      case "configuration":
        return updateConfiguration;
      default:
        return () => {}; // No update function for preview
    }
  };

  // Function to render the current step component
  const renderStepContent = () => {
    try {
      switch (steps[stepIndex].id) {
        case "foundation":
          return (
            <AgentFoundation data={agentData} updateData={updateAgentData} />
          );
        case "core-logic":
          return (
            <AgentCoreLogic data={agentData} updateData={updateAgentData} />
          );
        case "capabilities":
          return (
            <AgentCapabilities data={agentData} updateData={updateAgentData} />
          );
        case "automation":
          return (
            <AgentAutomation data={agentData} updateData={updateAgentData} />
          );
        case "knowledge":
          return (
            <AgentKnowledge data={agentData} updateData={updateAgentData} onUploadStatusChange={setIsKnowledgeUploading} />
          );
        case "configuration":
          return (
            <AgentConfiguration data={agentData} updateData={updateAgentData} />
          );
        case "preview":
          return <AgentUpdatePreview data={agentData} agentId={agentId} />;
        default:
          return <div>Unknown step</div>;
      }
    } catch (error) {
      console.error("Error rendering step content:", error);
      return <div>Error loading step content</div>;
    }
  };

  return (
    <div className="space-y-6">
      {isLoading && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[animate-out]:fade-out-0 data-[animate-in]:fade-in-0 data-[animate-out]:zoom-out-95 data-[animate-in]:zoom-in-95">
          <div className="flex flex-col items-center gap-4">
            <LoadingSpinner />
            <p className="text-primary">Loading agent data...</p>
          </div>
        </div>
      )}

      {/* Progress steps */}
      <div className="hidden md:block">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className="flex flex-col items-center cursor-pointer"
              onClick={() => setStepIndex(index)}
            >
              <div
                className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                  index < stepIndex
                    ? "bg-primary border-primary text-primary-foreground"
                    : index === stepIndex
                    ? "border-primary text-primary"
                    : "border-muted-foreground/30 text-muted-foreground/50"
                }`}
              >
                {index < stepIndex ? (
                  <CheckCircle2 className="h-5 w-5" />
                ) : (
                  step.icon
                )}
              </div>
              <span
                className={`mt-2 text-xs font-medium ${
                  index <= stepIndex
                    ? "text-foreground"
                    : "text-muted-foreground/50"
                }`}
              >
                {step.label}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-4 h-0.5 w-full bg-muted-foreground/20">
          <div
            className="absolute h-0.5 bg-primary transition-all duration-300"
            style={{ width: `${(stepIndex / (steps.length - 1)) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Mobile steps */}
      <div className="md:hidden">
        <Tabs value={steps[stepIndex].id} className="w-full" onValueChange={(value) => {
          const index = steps.findIndex(step => step.id === value);
          if (index !== -1) {
            setStepIndex(index);
          }
        }}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="foundation">Foundation</TabsTrigger>
            <TabsTrigger value="core-logic">Core Logic</TabsTrigger>
            <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
          </TabsList>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="automation">Automation</TabsTrigger>
            <TabsTrigger value="knowledge">Knowledge</TabsTrigger>
            <TabsTrigger value="configuration">Config</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Step content */}
      <Card className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <>
            {renderStepContent()}

            <div className="flex justify-end mt-8">
              {steps[stepIndex].id !== "preview" ? (
                <Button
                  onClick={() => getCurrentUpdateFunction()()}
                  disabled={isLoading || isKnowledgeUploading}
                >
                  {isLoading ? "Updating..." : "Update"}
                </Button>
              ) : (
                <Button
                  onClick={() => router.push("/dashboard/agents")}
                >
                  Back to Agents
                </Button>
              )}
            </div>
          </>
        )}
      </Card>
    </div>
  );
}
