"use client";

import { useState } from "react";
import { useAgentAvatars } from "@/hooks/use-agent-avatars";
import { AgentAvatar } from "@/services/agent-avatars";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Bot, ChevronLeft, ChevronRight } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface AvatarSelectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectAvatar: (avatar: AgentAvatar) => void;
  selectedAvatarUrl?: string | null;
}

export function AvatarSelectionDialog({
  open,
  onOpenChange,
  onSelectAvatar,
  selectedAvatarUrl,
}: AvatarSelectionDialogProps) {
  const [page, setPage] = useState(1);
  const { data, isLoading, error } = useAgentAvatars(page);

  const handleNextPage = () => {
    if (data?.metadata.hasNextPage) {
      setPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (data?.metadata.hasPreviousPage) {
      setPage((prev) => prev - 1);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Select an Avatar</DialogTitle>
          <DialogDescription>
            Choose an avatar for your agent from the available options.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {isLoading ? (
            <div className="grid grid-cols-3 sm:grid-cols-4 gap-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <Skeleton key={i} className="h-20 w-20 rounded-md" />
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-4 text-destructive">
              Error loading avatars. Please try again.
            </div>
          ) : (
            <>
              <div className="grid grid-cols-3 sm:grid-cols-4 gap-4">
                {data?.avatars.map((avatar) => (
                  <div
                    key={avatar.id}
                    className={`cursor-pointer p-1 rounded-md transition-all ${
                      selectedAvatarUrl === avatar.url
                        ? "ring-2 ring-primary bg-primary/10"
                        : "hover:bg-muted"
                    }`}
                    onClick={() => onSelectAvatar(avatar)}
                  >
                    <Avatar className="h-20 w-20 rounded-md">
                      <AvatarImage src={avatar.url} alt="Agent avatar" />
                      <AvatarFallback className="rounded-md bg-primary/10 text-primary">
                        <Bot className="h-8 w-8" />
                      </AvatarFallback>
                    </Avatar>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {data && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Page {data.metadata.currentPage} of {data.metadata.totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePrevPage}
                      disabled={!data.metadata.hasPreviousPage}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleNextPage}
                      disabled={!data.metadata.hasNextPage}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
