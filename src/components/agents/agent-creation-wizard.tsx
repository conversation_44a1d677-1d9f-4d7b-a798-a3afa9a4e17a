"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  CheckCircle2,
  ChevronRight,
  Info,
  Bot,
  Lightbulb,
  Wrench,
  Workflow,
  FileText,
  Variable,
} from "lucide-react";
import { useCreateAgent } from "@/hooks/use-agents";

// Step components
import { AgentFoundation } from "./wizard-steps/agent-foundation";
import { AgentCoreLogic } from "./wizard-steps/agent-core-logic";
import { AgentCapabilities } from "./wizard-steps/agent-capabilities";
import { AgentAutomation } from "./wizard-steps/agent-automation";
import { AgentKnowledge } from "./wizard-steps/agent-knowledge";
import { AgentConfiguration } from "./wizard-steps/agent-configuration";
import { AgentPreview } from "./wizard-steps/agent-preview";

export type AgentData = {
  // Foundation
  name: string;
  description: string;
  image: string | null;
  category: string;
  role: string;
  tone: string;

  // Core Logic
  systemPrompt: string;
  aiProvider: string | undefined;
  aiModel: string | undefined;
  apiKeyId: string | null;
  ruhCredentials: boolean;

  // Capabilities
  selectedTools: string[];
  mcp_server_ids: string[];
  capabilities: Array<{
    title: string;
    description: string;
  }>;

  // Advanced capabilities
  streaming: boolean;
  pushNotifications: boolean;
  stateTransitionHistory: boolean;
  examples: string[];
  inputModes: string[];
  outputModes: string[];

  // Automation
  workflows: string[];

  // Knowledge
  knowledgeSources: Array<{
    type: "document" | "url" | "text";
    content: string;
    name: string;
    file?: File;
    status?: "pending" | "uploaded" | "error";
  }>;
  files?: string[]; // Array of file URLs
  urls?: string[]; // Array of website URLs

  // Configuration
  variables: Array<{
    name: string;
    description: string;
    type: "text" | "number" | "json";
    default_value: string | null;
  }>;

  // Settings
  visibility?: string;
  isChangesMarketplace?: boolean;
};

const steps = [
  { id: "foundation", label: "Foundation", icon: <Bot className="h-5 w-5" /> },
  {
    id: "core-logic",
    label: "Core Logic",
    icon: <Lightbulb className="h-5 w-5" />,
  },
  {
    id: "capabilities",
    label: "Capabilities",
    icon: <Wrench className="h-5 w-5" />,
  },
  {
    id: "automation",
    label: "Automation",
    icon: <Workflow className="h-5 w-5" />,
  },
  {
    id: "knowledge",
    label: "Knowledge",
    icon: <FileText className="h-5 w-5" />,
  },
  {
    id: "configuration",
    label: "Configuration",
    icon: <Variable className="h-5 w-5" />,
  },
  { id: "preview", label: "Preview", icon: <Info className="h-5 w-5" /> },
];

interface AgentCreationWizardProps {
  agentId?: string;
  currentStep: string;
  setCurrentStep: (step: string) => void;
  initialData?: Partial<AgentData> | null;
}

export function AgentCreationWizard({
  agentId,
  currentStep,
  setCurrentStep,
  initialData,
}: AgentCreationWizardProps) {
  const router = useRouter();
  const createAgent = useCreateAgent();
  const [stepIndex, setStepIndex] = useState(0);
  const [isKnowledgeUploading, setIsKnowledgeUploading] = useState(false);

  // Find the index of the current step
  useEffect(() => {
    const index = steps.findIndex((step) => step.id === currentStep);
    setStepIndex(index >= 0 ? index : 0);
  }, [currentStep]);

  // Load agent data if editing an existing agent or if initialData is provided
  useEffect(() => {
    const loadAgentData = async () => {
      // If initialData is provided, use it
      if (initialData) {
        updateAgentData(initialData);
        toast.success("Agent configuration loaded");
        return;
      }

      // Otherwise, if agentId is provided, fetch the agent data from the API
      if (agentId) {
        try {
          // TODO: Replace with actual API call when backend is ready
          // const response = await agentService.getAgentById(agentId);
          // const agent = response.agent;

          // For now, just show a loading message
          toast.loading("Loading agent data...", {
            id: "agent-loading",
            duration: 2000,
          });

          // In the future, map API response to our agent data structure
          // updateAgentData({
          //   name: agent.name,
          //   description: agent.description,
          //   image: agent.avatar,
          //   category: agent.agent_category,
          //   systemPrompt: agent.system_message,
          //   aiProvider: agent.model_provider,
          //   aiModel: agent.model_name,
          //   workflows: agent.workflow_ids,
          //   mcp_server_ids: agent.mcp_server_ids,
          // });

          // toast.success("Agent data loaded successfully");
        } catch (error) {
          console.error("Error loading agent data:", error);
          toast.error("Failed to load agent data");
        }
      }
    };

    loadAgentData();
  }, [agentId, initialData]);

  const [agentData, setAgentData] = useState<AgentData>({
    // Foundation
    name: "",
    description: "",
    image: null,
    category: "general",
    role: "",
    tone: "professional",

    // Core Logic
    systemPrompt: "",
    aiProvider: "OpenAI",
    aiModel: "gpt-4o",
    apiKeyId: null,
    ruhCredentials: false,

    // Capabilities
    selectedTools: [],
    mcp_server_ids: [],
    capabilities: [],

    // Advanced capabilities
    streaming: false,
    pushNotifications: false,
    stateTransitionHistory: false,
    examples: [],
    inputModes: ["text"],
    outputModes: ["text"],

    // Automation
    workflows: [],

    // Knowledge
    knowledgeSources: [],

    // Configuration
    variables: [],
  });

  // Update URL when step changes
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    params.set("step", steps[stepIndex].id);

    // Update URL without causing a navigation
    window.history.replaceState(
      {},
      "",
      `${window.location.pathname}?${params.toString()}`
    );

    // Update the parent component's state
    setCurrentStep(steps[stepIndex].id);
  }, [stepIndex, setCurrentStep]);

  const updateAgentData = (data: Partial<AgentData>) => {
    setAgentData((prev) => ({ ...prev, ...data }));
  };

  // Validate the current step
  const validateCurrentStep = (): boolean => {
    switch (steps[stepIndex].id) {
      case "foundation":
        if (!agentData.name || agentData.name.trim() === "") {
          toast.error("Please enter an agent name");
          return false;
        }
        if (!agentData.description || agentData.description.trim() === "") {
          toast.error("Please enter an agent description");
          return false;
        }
        if (!agentData.role || agentData.role.trim() === "") {
          toast.error("Please enter an agent role");
          return false;
        }
        if (!agentData.tone || agentData.tone.trim() === "") {
          toast.error("Please select an agent tone");
          return false;
        }
        if (!agentData.category || agentData.category.trim() === "") {
          toast.error("Please select an agent category");
          return false;
        }
        return true;

      case "core-logic":
        if (!agentData.systemPrompt || agentData.systemPrompt.trim() === "") {
          toast.error("Please enter a system prompt");
          return false;
        }
        return true;

      case "capabilities":
        // No validation required for MCP servers
        return true;

      case "automation":
        // No validation required for workflows
        return true;

      // Other steps are optional
      default:
        return true;
    }
  };

  const handleNext = async () => {
    if (stepIndex < steps.length - 1) {
      // Validate the current step before proceeding
      if (!validateCurrentStep()) {
        return;
      }

      // If we're on the knowledge step, check upload status
      if (steps[stepIndex].id === "knowledge") {
        // Check if any files are currently uploading or have failed
        if (isKnowledgeUploading || agentData.knowledgeSources.some(s => s.type === 'document' && s.status === 'error')) {
          toast.error("Please wait for files to finish uploading or resolve errors.");
          return;
        }
      }

      const nextStep = stepIndex + 1;
      setStepIndex(nextStep);
      window.scrollTo(0, 0);

      // Show success toast for completed steps
      if (steps[stepIndex].id === "foundation") {
        toast.success("Foundation details saved");
      } else if (steps[stepIndex].id === "core-logic") {
        toast.success("Core logic configured");
      } else if (steps[stepIndex].id === "capabilities") {
        toast.success("Capabilities configured");
      } else if (steps[stepIndex].id === "automation") {
        toast.success("Automation configured");
      } else if (steps[stepIndex].id === "knowledge") {
        toast.success("Knowledge sources added");
      }
    }
  };

  const handlePrevious = () => {
    if (stepIndex > 0) {
      const prevStep = stepIndex - 1;
      setStepIndex(prevStep);
      window.scrollTo(0, 0);
    }
  };

  const handleSaveDraft = async () => {
    // Validate the final step
    if (!validateCurrentStep()) {
      return;
    }

    // Prevent saving if files are uploading
    if (isKnowledgeUploading) {
      toast.error("Please wait for files to finish uploading before saving.");
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading(
      agentId ? "Updating agent..." : "Saving agent as draft..."
    );

    try {
      // Prepare response model array based on toggles
      const responseModel: string[] = [];
      if (agentData.streaming) {
        responseModel.push("streaming_response");
      }
      if (agentData.pushNotifications) {
        responseModel.push("push_notification");
      }
      if (agentData.stateTransitionHistory) {
        responseModel.push("state_transition_history");
      }

      // Process variables to handle null default_value
      const processedVariables = agentData.variables.map(variable => ({
        ...variable,
        default_value: variable.default_value === null ? "" : variable.default_value
      }));

      // Prepare the payload for the API
      const payload = {
        name: agentData.name,
        description: agentData.description,
        system_message: agentData.systemPrompt,
        model_provider: agentData.ruhCredentials ? null : (agentData.aiProvider || null),
        model_name: agentData.ruhCredentials ? null : (agentData.aiModel || null),
        model_api_key: agentData.ruhCredentials ? null : agentData.apiKeyId,
        workflow_ids: agentData.workflows,
        mcp_server_ids: agentData.mcp_server_ids,
        tone: agentData.tone,
        department: "IT",
        category: agentData.category,
        agent_topic_type: agentData.role,
        visibility: "public",
        status: "draft", // Save as draft instead of active
        ruh_credentials: agentData.ruhCredentials,
        avatar: agentData.image, // Include the avatar URL
        variables: processedVariables, // Include the processed variables
        files: agentData.files || [], // Include uploaded files
        urls: agentData.urls || [], // Include URLs
        capabilities_data: {
          capabilities: agentData.capabilities,
          input_modes: agentData.inputModes,
          output_modes: agentData.outputModes,
          response_model: responseModel
        },
        example_prompts: agentData.examples
      };

      let result;

      if (agentId) {
        // TODO: Implement update logic when backend is ready
        // result = await agentService.updateAgent(agentId, payload);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        result = { id: agentId };
      } else {
        // Create a new agent as draft
        result = await createAgent.mutateAsync(payload);
      }

      // Always dismiss the loading toast
      toast.dismiss(loadingToast);

      // Show success message
      toast.success(
        agentId ? "Agent updated successfully" : "Agent saved as draft"
      );

      // Add a small delay before redirecting to ensure toast is shown
      setTimeout(() => {
        // Navigate back to agents list
        router.push("/dashboard/agents");
      }, 500);

      return result;
    } catch (error) {
      // Always dismiss the loading toast in case of error
      toast.dismiss(loadingToast);

      console.error("Error saving agent:", error);
      toast.error(
        "Failed to save agent: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  };

  const handlePublish = async () => {
    // Validate the final step
    if (!validateCurrentStep()) {
      return;
    }

    // Prevent publishing if files are uploading
    if (isKnowledgeUploading) {
      toast.error("Please wait for files to finish uploading before publishing.");
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading(
      agentId ? "Updating agent..." : "Publishing agent..."
    );

    try {
      // Prepare response model array based on toggles
      const responseModel: string[] = [];
      if (agentData.streaming) {
        responseModel.push("streaming_response");
      }
      if (agentData.pushNotifications) {
        responseModel.push("push_notification");
      }
      if (agentData.stateTransitionHistory) {
        responseModel.push("state_transition_history");
      }

      // Process variables to handle null default_value
      const processedVariables = agentData.variables.map(variable => ({
        ...variable,
        default_value: variable.default_value === null ? "" : variable.default_value
      }));

      // Prepare the payload for the API
      const payload = {
        name: agentData.name,
        description: agentData.description,
        system_message: agentData.systemPrompt,
        model_provider: agentData.ruhCredentials ? null : (agentData.aiProvider || null),
        model_name: agentData.ruhCredentials ? null : (agentData.aiModel || null),
        model_api_key: agentData.ruhCredentials ? null : agentData.apiKeyId,
        workflow_ids: agentData.workflows,
        mcp_server_ids: agentData.mcp_server_ids,
        tone: agentData.tone,
        department: "IT",
        category: agentData.category,
        agent_topic_type: agentData.role,
        visibility: "private",
        status: "active",
        ruh_credentials: agentData.ruhCredentials,
        avatar: agentData.image, // Include the avatar URL
        variables: processedVariables, // Include the processed variables
        files: agentData.files || [], // Include uploaded files
        urls: agentData.urls || [], // Include URLs
        capabilities_data: {
          capabilities: agentData.capabilities,
          input_modes: agentData.inputModes,
          output_modes: agentData.outputModes,
          response_model: responseModel
        },
        example_prompts: agentData.examples
      };

      let result;

      if (agentId) {
        // TODO: Implement update logic when backend is ready
        // result = await agentService.updateAgent(agentId, payload);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        result = { id: agentId };
      } else {
        // Create a new agent
        result = await createAgent.mutateAsync(payload);
      }

      // Always dismiss the loading toast
      toast.dismiss(loadingToast);

      // Show success message
      toast.success(
        agentId
          ? "Agent updated and published successfully"
          : "Agent published successfully"
      );

      // Add a small delay before redirecting to ensure toast is shown
      setTimeout(() => {
        // Navigate back to agents list
        router.push("/dashboard/agents");
      }, 500);

      return result;
    } catch (error) {
      // Always dismiss the loading toast in case of error
      toast.dismiss(loadingToast);

      console.error("Error publishing agent:", error);
      toast.error(
        "Failed to publish agent: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  };

  // Function to render the current step component
  const renderStepContent = () => {
    try {
      switch (steps[stepIndex].id) {
        case "foundation":
          return (
            <AgentFoundation data={agentData} updateData={updateAgentData} />
          );
        case "core-logic":
          return (
            <AgentCoreLogic data={agentData} updateData={updateAgentData} />
          );
        case "capabilities":
          return (
            <AgentCapabilities data={agentData} updateData={updateAgentData} />
          );
        case "automation":
          return (
            <AgentAutomation data={agentData} updateData={updateAgentData} />
          );
        case "knowledge":
          return (
            <AgentKnowledge data={agentData} updateData={updateAgentData} onUploadStatusChange={setIsKnowledgeUploading} />
          );
        case "configuration":
          return (
            <AgentConfiguration data={agentData} updateData={updateAgentData} />
          );
        case "preview":
          return <AgentPreview data={agentData} />;
        default:
          return <div>Unknown step</div>;
      }
    } catch (error) {
      console.error("Error rendering step content:", error);
      return <div>Error loading step content</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress steps */}
      <div className="hidden md:block">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex flex-col items-center">
              <div
                className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                  index < stepIndex
                    ? "bg-primary border-primary text-primary-foreground"
                    : index === stepIndex
                    ? "border-primary text-primary"
                    : "border-muted-foreground/30 text-muted-foreground/50"
                }`}
              >
                {index < stepIndex ? (
                  <CheckCircle2 className="h-5 w-5" />
                ) : (
                  step.icon
                )}
              </div>
              <span
                className={`mt-2 text-xs font-medium ${
                  index <= stepIndex
                    ? "text-foreground"
                    : "text-muted-foreground/50"
                }`}
              >
                {step.label}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-4 h-0.5 w-full bg-muted-foreground/20">
          <div
            className="absolute h-0.5 bg-primary transition-all duration-300"
            style={{ width: `${(stepIndex / (steps.length - 1)) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Mobile steps */}
      <div className="md:hidden">
        <Tabs value={steps[stepIndex].id} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="foundation">Foundation</TabsTrigger>
            <TabsTrigger value="core-logic">Core Logic</TabsTrigger>
            <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
          </TabsList>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="automation">Automation</TabsTrigger>
            <TabsTrigger value="knowledge">Knowledge</TabsTrigger>
            <TabsTrigger value="configuration">Config</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Step content */}
      <Card className="p-6">
        {renderStepContent()}

        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={stepIndex === 0}
          >
            Previous
          </Button>

          <div className="space-x-2">
            {stepIndex === steps.length - 1 ? (
              <>
                <Button
                  variant="secondary"
                  onClick={handleSaveDraft}
                  disabled={isKnowledgeUploading}
                >
                  {agentId ? "Save Changes" : "Save as Draft"}
                </Button>
                <Button
                  variant="default"
                  onClick={handlePublish}
                  disabled={isKnowledgeUploading}
                >
                  {agentId ? "Update & Publish" : "Publish Agent"}
                </Button>
              </>
            ) : (
              <Button
                onClick={handleNext}
                disabled={stepIndex === steps.length - 1 || isKnowledgeUploading}
              >
                Next <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
}
