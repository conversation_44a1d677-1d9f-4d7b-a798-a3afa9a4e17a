"use client"

import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  CardDescription, 
  Card<PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { FileText, CheckCircle, AlertCircle, Clock } from "lucide-react"

// Sample API request/event data
const apiEvents = [
  { 
    id: "req_278943",
    type: "API Request", 
    endpoint: "/api/agents/invoke",
    status: "success", 
    timestamp: "Today, 10:23 AM", 
    duration: "320ms",
    user: "<EMAIL>"
  },
  { 
    id: "req_278942",
    type: "Workflow Exec", 
    endpoint: "/api/workflows/run/wf-28791",
    status: "success", 
    timestamp: "Today, 10:21 AM", 
    duration: "1.2s",
    user: "<EMAIL>"
  },
  { 
    id: "req_278941",
    type: "API Request", 
    endpoint: "/api/knowledge/search",
    status: "success", 
    timestamp: "Today, 10:15 AM", 
    duration: "450ms",
    user: "<EMAIL>"
  },
  { 
    id: "req_278940",
    type: "API Request", 
    endpoint: "/api/agents/invoke",
    status: "error", 
    timestamp: "Today, 10:10 AM", 
    duration: "384ms",
    user: "<EMAIL>"
  },
  { 
    id: "req_278939",
    type: "Auth Event", 
    endpoint: "/api/auth/login",
    status: "success", 
    timestamp: "Today, 10:02 AM", 
    duration: "120ms",
    user: "<EMAIL>"
  },
  { 
    id: "req_278938",
    type: "Workflow Exec", 
    endpoint: "/api/workflows/run/wf-28523",
    status: "pending", 
    timestamp: "Today, 9:58 AM", 
    duration: "5.3s",
    user: "<EMAIL>"
  },
]

export function ApiEventsTable() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Latest API Requests & Events
        </CardTitle>
        <CardDescription>
          Most recent platform activity and API usage
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Endpoint</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>User</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apiEvents.map((event) => (
                <TableRow key={event.id}>
                  <TableCell>{event.type}</TableCell>
                  <TableCell className="font-mono text-xs">{event.endpoint}</TableCell>
                  <TableCell>
                    {event.status === "success" ? (
                      <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Success
                      </Badge>
                    ) : event.status === "error" ? (
                      <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/20">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Error
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-muted-foreground">{event.timestamp}</TableCell>
                  <TableCell>{event.duration}</TableCell>
                  <TableCell className="text-muted-foreground">{event.user}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
