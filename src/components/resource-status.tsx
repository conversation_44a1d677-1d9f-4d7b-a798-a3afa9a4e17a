"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts"
import { useState } from "react"

const agentData = [
  { name: "Online", value: 42, color: "#10b981" },
  { name: "Idle", value: 8, color: "#6b7280" },
  { name: "Offline", value: 5, color: "#ef4444" },
  { name: "Erroring", value: 3, color: "#f97316" },
]

const toolData = [
  { name: "File Processing", value: 2800, color: "#8b5cf6" },
  { name: "Natural Language", value: 1908, color: "#8b5cf6" },
  { name: "Data Mining", value: 1500, color: "#8b5cf6" },
  { name: "Recommendation", value: 1200, color: "#8b5cf6" },
  { name: "Image Analysis", value: 800, color: "#8b5cf6" },
]

// Removed unused constant: const RADIAN = Math.PI / 180

// Define the type for tool data
type ToolDataItem = {
  name: string;
  value: number;
  color: string;
};

export function ResourceStatus() {
  const [activeTab, setActiveTab] = useState("agent");
  const [hoveredTool, setHoveredTool] = useState<ToolDataItem | null>(null);

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Resource Status Overview</CardTitle>
        <p className="text-sm text-muted-foreground">
          Current status of agents and tools
        </p>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="agent" onValueChange={setActiveTab}>
          <TabsList className="mb-4 w-full">
            <TabsTrigger
              value="agent"
              className={`flex-1 ${activeTab === "agent" ? "bg-background border-input" : ""}`}
            >
              Agent Status
            </TabsTrigger>
            <TabsTrigger
              value="tool"
              className={`flex-1 ${activeTab === "tool" ? "bg-background border-input" : ""}`}
            >
              Tool Usage
            </TabsTrigger>
          </TabsList>
          <TabsContent value="agent">
            <div className="flex flex-col items-center">
              <div className="relative h-[220px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={agentData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                      startAngle={90}
                      endAngle={-270}
                    >
                      {agentData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry.color}
                          stroke="transparent"
                        />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>

                {/* Status labels with colored dots */}
                <div className="absolute bottom-0 left-0 right-0 flex justify-center gap-4 text-xs">
                  {agentData.map((entry, index) => (
                    <div key={index} className="flex items-center gap-1">
                      <div className="h-3 w-3 rounded-full" style={{ backgroundColor: entry.color }}></div>
                      <span className="text-muted-foreground">{entry.name}</span>
                    </div>
                  ))}
                </div>

                {/* Status numbers */}
                <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-center">
                  <div className="text-lg font-semibold">
                    {agentData.reduce((sum, item) => sum + item.value, 0)}
                  </div>
                  <div className="text-xs text-muted-foreground">total agents</div>
                </div>

                {/* Status labels with values */}
                <div className="absolute left-0 right-0 top-0 flex justify-between px-8 pt-4">
                  <div className="text-left">
                    <div className="text-lg font-semibold text-green-500">Online: {agentData[0].value}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-orange-500">Erroring: {agentData[3].value}</div>
                  </div>
                </div>
                <div className="absolute bottom-12 left-0 right-0 flex justify-between px-8">
                  <div className="text-left">
                    <div className="text-lg font-semibold text-gray-500">Idle: {agentData[1].value}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-red-500">Offline: {agentData[2].value}</div>
                  </div>
                </div>
              </div>

              <div className="mt-4 text-sm text-muted-foreground text-center">
                58 total agents configured
              </div>
            </div>
          </TabsContent>
          <TabsContent value="tool">
            <div className="h-[300px] relative">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  layout="vertical"
                  data={toolData}
                  margin={{ top: 10, right: 30, left: 120, bottom: 10 }}
                  onMouseMove={(data) => {
                    if (data.isTooltipActive && typeof data.activeTooltipIndex === 'number') {
                      setHoveredTool(toolData[data.activeTooltipIndex]);
                    } else {
                      setHoveredTool(null);
                    }
                  }}
                  onMouseLeave={() => setHoveredTool(null)}
                >
                  <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                  <XAxis
                    type="number"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12 }}
                    domain={[0, 3000]}
                    tickFormatter={(value) => `${value}`}
                  />
                  <YAxis
                    dataKey="name"
                    type="category"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, textAnchor: 'end' }}
                    width={120}
                    dx={-10}
                  />
                  <Tooltip
                    cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-background border rounded p-2 shadow-sm">
                            <p className="font-medium">{payload[0].payload.name}</p>
                            <p className="text-sm text-muted-foreground">
                              usage : {payload[0].value}
                            </p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Bar
                    dataKey="value"
                    fill="#8b5cf6"
                    radius={[0, 4, 4, 0]}
                    barSize={30}
                  />
                </BarChart>
              </ResponsiveContainer>

              {hoveredTool && (
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background border rounded p-3 shadow-md">
                  <p className="font-medium">{hoveredTool.name}</p>
                  <p className="text-sm text-muted-foreground">
                    usage : {hoveredTool.value}
                  </p>
                </div>
              )}

              <div className="absolute bottom-0 left-0 right-0 flex justify-center gap-4 text-xs">
                <div className="flex items-center gap-1">
                  <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                  <span className="text-muted-foreground">Active Tools</span>
                </div>
              </div>

              <div className="mt-2 text-sm text-muted-foreground text-center">
                15 active tools configured
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
