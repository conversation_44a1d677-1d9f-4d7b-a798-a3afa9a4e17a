"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import {
  BarChart2,
  AlertTriangle,
  Clock,
  Users,
  Bell,
  BarC<PERSON>
} from "lucide-react"

interface MetricCardProps {
  title: string
  value: string
  change: string
  changeType: "positive" | "negative" | "neutral"
  iconName: string
  iconColor: string
  period?: string
  additionalInfo?: string
}

// Component to dynamically render the correct icon
const DynamicIcon = ({ name, className }: { name: string, className?: string }) => {
  switch (name) {
    case "BarChart2":
      return <BarChart2 className={className} />
    case "AlertTriangle":
      return <AlertTriangle className={className} />
    case "Clock":
      return <Clock className={className} />
    case "Users":
      return <Users className={className} />
    case "Bell":
      return <Bell className={className} />
    case "Workflow":
      // Use BarChart as a fallback for Workflow
      return <BarChart className={className} />
    default:
      return null
  }
}

export function MetricCard({
  title,
  value,
  change,
  changeType,
  iconName,
  iconColor,
  period = "Last 7 days",
  additionalInfo,
}: MetricCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium text-muted-foreground">{title}</div>
          <div
            className={cn(
              "flex h-8 w-8 items-center justify-center rounded-full",
              iconColor
            )}
          >
            <DynamicIcon name={iconName} className="h-4 w-4 text-white" />
          </div>
        </div>
        <div className="mt-2 text-3xl font-bold">{value}</div>
        <div className="mt-2 flex items-center text-xs">
          <span
            className={cn(
              "mr-1 font-medium",
              changeType === "positive" && "text-green-500",
              changeType === "negative" && "text-red-500"
            )}
          >
            {change}
          </span>
          <span className="text-muted-foreground">{period}</span>
        </div>
        {additionalInfo && (
          <div className="mt-2 text-xs text-muted-foreground">{additionalInfo}</div>
        )}
      </CardContent>
    </Card>
  )
}
