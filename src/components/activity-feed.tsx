"use client"

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertTriangle, CheckCircle, Info, XCircle, ArrowRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Activity as ApiActivity } from "@/shared/interfaces"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"

interface ActivityFeedProps {
activities: ApiActivity[];
isLoading: boolean;
}

// Helper function to map activity status to a specific icon and color
const getActivityIcon = (status: string) => {
  const lowerStatus = status?.toLowerCase();
  switch (lowerStatus) {
    case 'failed':
    case 'error':
      return <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
    case 'completed':
    case 'success':
      return <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
    case 'started':
    case 'running':
      return <Info className="h-5 w-5 text-blue-500 flex-shrink-0" />
    case 'warning':
      return <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0" />
    default:
      return <Info className="h-5 w-5 text-gray-500 flex-shrink-0" />
  }
}

// Helper function to determine the badge variant for visual consistency
const getStatusBadgeVariant = (status: string): "secondary" | "destructive" | "outline" => {
  const lowerStatus = status?.toLowerCase();
  if (lowerStatus === "completed" || lowerStatus === "started" || lowerStatus === "success") {
    return "secondary";
  }
  if (lowerStatus === "failed" || lowerStatus === "error") {
    return "destructive";
  }
  return "outline";
}


export function ActivityFeed({ activities, isLoading }: ActivityFeedProps) {
  const router = useRouter();

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="p-4 md:p-6">
        <CardTitle className="text-base font-bold">Recent Activity & Issues</CardTitle>
        <CardDescription>
          A summary of the latest system activities.
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0 flex-grow">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Activity</TableHead>
              <TableHead className="w-[100px] text-center">Status</TableHead>
              <TableHead className="w-[150px] text-right">Time</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Skeleton loading state that mimics the table structure
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-5 w-5 rounded-full" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-3 w-48" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-center"><Skeleton className="h-5 w-20 mx-auto" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-4 w-28 ml-auto" /></TableCell>
                </TableRow>
              ))
            ) : activities.length === 0 ? (
              // Clear empty state message spanning all columns
              <TableRow>
                <TableCell colSpan={3} className="h-48 text-center text-muted-foreground">
                  No recent activity found.
                </TableCell>
              </TableRow>
            ) : (
              // Map activities to table rows
              activities.map((item) => (
                <TableRow
                  key={item.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => router.push(`/dashboard/activity/${item.id}`)}
                >
                  <TableCell>
                    <div className="flex items-center gap-3">
                      {getActivityIcon(item.status)}
                      <div>
                        <div className="font-bold">{item.type}</div>
                        <div className="text-xs text-muted-foreground font-mono truncate">
                          {item.resource_id}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant={getStatusBadgeVariant(item.status)} className="capitalize">
                      {item.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right text-xs text-muted-foreground">
                    {new Date(item.created_at).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter className="p-2 border-t mt-auto">
        <Button asChild variant="ghost" size="sm" className="w-full">
          <Link href="/dashboard/activity" className="flex items-center justify-center gap-1">
            View Full Activity Log
            <ArrowRight className="h-3.5 w-3.5 ml-1" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}