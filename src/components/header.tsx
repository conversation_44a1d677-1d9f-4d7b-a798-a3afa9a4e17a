"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { UserAvatar } from "@/components/user-avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { getDocsUrl } from "@/lib/helpers";
import NotificationPanel from "./notification-panel";

interface HeaderProps {
  toggleSidebar: () => void;
}

export function Header({ toggleSidebar }: HeaderProps) {
  return (
    <header className="sticky top-0 z-30 flex h-14 justify-end items-center gap-4 border-b bg-background px-4 sm:px-6">
      <Button
        variant="ghost"
        size="icon"
        className="lg:hidden mr-auto"
        onClick={(e) => {
          // Prevent event bubbling
          e.stopPropagation();
          toggleSidebar();
        }}
      >
        <Menu className="h-5 w-5" />
        <span className="sr-only">Toggle sidebar</span>
      </Button>

      {/* <div className="flex-1">
        <div className="hidden md:flex items-center space-x-2">
          <div className="bg-primary rounded-md w-8 h-8 flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5 text-primary-foreground"
            >
              <path d="M12 2L2 7l10 5 10-5-10-5z" />
              <path d="M2 17l10 5 10-5" />
              <path d="M2 12l10 5 10-5" />
            </svg>
          </div>
          <div>
            <h1 className="font-bold text-lg">RUH AI</h1>
            <p className="text-xs text-muted-foreground">Developer</p>
          </div>
        </div>
      </div> */}

      <div className="flex items-center gap-2 md:gap-4">
        <ThemeToggle />

        <NotificationPanel />

        {/* <Button variant="outline" asChild className="hidden md:flex">
          <Link href={getDocsUrl()} target="_blank">
            <FileText className="mr-2 h-4 w-4" />
            Documentation
          </Link>
        </Button> */}

        <Button
          variant="outline"
          size="sm"
          className="hidden md:flex"
          onClick={() => window.open(getDocsUrl(), "_blank")}
        >
          API Reference
        </Button>

        {/* <UserAvatar /> */}
      </div>
    </header>
  );
}
