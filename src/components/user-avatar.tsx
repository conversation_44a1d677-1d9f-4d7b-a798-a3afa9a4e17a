// src/components/user-avatar.tsx

"use client"

import { useState } from "react"
import { useUserDetails, useLogout, authKeys } from "@/hooks/use-auth"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { User, LogOut } from "lucide-react"
import Link from "next/link"
import { useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

// Define props for the component
interface UserAvatarProps {
  isCollapsed?: boolean // To control the display style
  className?: string // For additional styling from the parent
}

export function UserAvatar({ isCollapsed = false, className }: UserAvatarProps) {
  const { data: userDetails, isLoading } = useUserDetails()
  const logout = useLogout()
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()

  const handleLogout = () => {
    setIsOpen(false)
    queryClient.removeQueries({ queryKey: authKeys.user })
    queryClient.removeQueries({ queryKey: authKeys.userDetails })
    logout.mutate(undefined, {
      onSuccess: () => toast.success("Logged out successfully"),
      onError: () => toast.error("Failed to log out"),
    })
  }

  const getInitials = (name: string) => {
    if (!name) return ""
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2)
  }

  const initials = userDetails ? getInitials(userDetails.fullName) : ""

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        {/* The Button is now the unified trigger for both states */}
        <Button
          variant="ghost"
          className={cn(
            "h-auto w-full transition-colors",
            // Apply different styles based on collapsed state
            isCollapsed
              ? "justify-center p-0 h-8 w-8 rounded-md" // Collapsed: Icon-only style
              : "justify-start gap-3 px-3 py-2 rounded-md", // Expanded: Full style
            className
          )}
        >
          {/* Avatar is always visible */}
          <Avatar className="h-8 w-8 bg-primary/10 rounded-md">
            {userDetails?.profileImage ? (
              <AvatarImage src={userDetails.profileImage} alt="User avatar" />
            ) : (
              <AvatarFallback className="text-primary font-medium">
                {initials}
              </AvatarFallback>
            )}
          </Avatar>
          
          {/* Name is only visible when not collapsed */}
          {!isCollapsed && (
            <span className="truncate text-sm font-medium text-sidebar-foreground">
              {userDetails?.fullName || 'User Profile'}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="start" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            {isLoading ? (
              <p className="text-sm font-medium leading-none">Loading...</p>
            ) : (
              <>
                <p className="text-sm font-medium leading-none">
                  {userDetails?.fullName || ""}
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  {userDetails?.email || ""}
                </p>
              </>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <Link href="/dashboard/profile">
          <DropdownMenuItem>
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </DropdownMenuItem>
        </Link>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}