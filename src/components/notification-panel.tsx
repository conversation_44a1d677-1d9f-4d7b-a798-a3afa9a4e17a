import React from 'react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from './ui/dropdown-menu'
import { Button } from './ui/button'
import { Bell } from 'lucide-react'
import { Badge } from './ui/badge'

type Props = {}

const NotificationPanel = (props: Props) => {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                    <Bell className="h-5 w-5" />
                    {/* <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center">
                        0
                    </Badge> */}
                    <span className="sr-only">Notifications</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div className="max-h-80 overflow-auto">
                    {/* Notification items would go here */}
                    {/* <div className="p-3 text-sm hover:bg-accent rounded-md">
                        <div className="font-medium">New agent deployed</div>
                        <div className="text-muted-foreground text-xs mt-1">
                            Your agent &quot;Customer Support&quot; is now live
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                            2 minutes ago
                        </div>
                    </div>
                    <div className="p-3 text-sm hover:bg-accent rounded-md">
                        <div className="font-medium">Workflow execution completed</div>
                        <div className="text-muted-foreground text-xs mt-1">
                            Document processing workflow completed successfully
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                            1 hour ago
                        </div>
                    </div> */}
                    <div className="p-3 text-sm text-muted-foreground justify-center flex items-center h-30">
                        No new notifications
                    </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="justify-center text-center cursor-pointer">
                    View all notifications
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

export default NotificationPanel