"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Workflow,
  Key,
  Activity,
  FileText,
  Wrench,
  User,
} from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export function QuickActions() {
  const router = useRouter();
  const handleRedirect = (
    url: string,
    external: boolean = false,
    message?: string
  ) => {
    if (external) {
      window.open(url, "_blank");
      if (message) {
        toast.success(message);
      }
    } else {
      router.push(url);
      if (message) {
        toast.success(message);
      }
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-base">Quick Actions</CardTitle>
        <CardDescription>Frequently used platform operations</CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="grid grid-cols-1 gap-2">
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() =>
              handleRedirect(
                "/dashboard/workflows",
                false,
              )
            }
          >
            <Workflow className="mr-2 h-4 w-4" />
            Create New Workflow
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() =>
              handleRedirect(
                "/dashboard/agents/create?step=foundation",
                false,
              )
            }
          >
            <User className="mr-2 h-4 w-4" />
            Create New Agent
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleRedirect("/dashboard/tools", false)}
          >
            <Wrench className="mr-2 h-4 w-4" />
            Add Tool/MCP Server
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleRedirect("/dashboard/api-keys", false)}
          >
            <Key className="mr-2 h-4 w-4" />
            View API Keys
          </Button>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleRedirect("/dashboard/activity", false)}
          >
            <Activity className="mr-2 h-4 w-4" />
            View Activity Log
          </Button>
          {/* <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleRedirect("/dashboard/webhooks", false)}
          >
            <FileText className="mr-2 h-4 w-4" />
            Manage Webhooks
          </Button> */}
        </div>
      </CardContent>
    </Card>
  );
}
