"use client"

import { useEffect } from "react"
import { useQueryClient } from "@tanstack/react-query"
import { authService } from "@/services/auth-service"
import { authKeys } from "@/hooks/use-auth"
import { toast } from "sonner"

export function UserProvider({ children }: { children: React.ReactNode }) {
  const queryClient = useQueryClient()

  useEffect(() => {
    // Prefetch user details when the dashboard loads
    const fetchUserDetails = async () => {
      try {
        const userDetails = await authService.getUserDetails()
        queryClient.setQueryData(authKeys.userDetails, userDetails)
      } catch (error) {
        console.error("Error fetching user details:", error)
        toast.error(
          "Failed to fetch user details",
          {
            description: error instanceof Error
              ? error.message
              : "An unexpected error occurred"
          }
        )
      }
    }

    fetchUserDetails()
  }, [queryClient])

  return <>{children}</>
}
