// "use client"

// import { Sidebar } from "@/components/sidebar"
// import { Header } from "@/components/header"
// import { ThemeProvider } from "@/components/theme-provider"

// interface LayoutProps {
//   children: React.ReactNode
// }

// export function Layout({ children }: LayoutProps) {
//   return (
//     <ThemeProvider>
//       <div className="flex h-screen">
//         <Sidebar />
//         <div className="flex flex-1 flex-col">
//           <Header />
//           <main className="flex-1 overflow-auto p-6">{children}</main>
//         </div>
//       </div>
//     </ThemeProvider>
//   )
// }
