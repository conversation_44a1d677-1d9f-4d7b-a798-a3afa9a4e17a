"use client";

import { useState, useEffect } from "react";
import { MemoizedSidebar } from "@/components/sidebar";
import { Header } from "@/components/header";
import { UserProvider } from "@/components/user-provider";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useUIStore } from "@/store/ui-store";

export function DashboardWrapper({ children }: { children: React.ReactNode }) {
  const isDesktop = useMediaQuery("(min-width: 1024px)");

  // Use the UI store for sidebar state
  const { sidebarOpen: storeSidebarOpen, setSidebarOpen: setStoreSidebarOpen } = useUIStore();

  // Local state for sidebar that syncs with store
  const [sidebarOpen, setSidebarOpen] = useState(false); // Initialize as closed to prevent flash

  // Initialize sidebar state from store or based on screen size
  useEffect(() => {
    // On first render, sync with the store value
    setSidebarOpen(storeSidebarOpen);

    // If we're on desktop and this is initial load, ensure sidebar is open
    if (isDesktop && typeof window !== 'undefined' && !localStorage.getItem('ui-storage')) {
      setSidebarOpen(true);
      setStoreSidebarOpen(true);
    }
  }, [isDesktop, storeSidebarOpen, setStoreSidebarOpen]);

  // Update both local state and store when sidebar changes
  const updateSidebarState = (open: boolean) => {
    setSidebarOpen(open);
    setStoreSidebarOpen(open);
  };

  // Toggle sidebar function to be passed to header
  const toggleSidebar = () => {
    updateSidebarState(!sidebarOpen);
  };

  return (
    <UserProvider>
      <div className="flex h-screen overflow-hidden bg-background">
        <MemoizedSidebar isOpen={sidebarOpen} setIsOpen={updateSidebarState} />
        <div className="flex flex-1 flex-col">
          <Header toggleSidebar={toggleSidebar} />
          <main className="flex-1 overflow-auto p-4 md:p-6">{children}</main>
        </div>
      </div>
    </UserProvider>
  );
}
