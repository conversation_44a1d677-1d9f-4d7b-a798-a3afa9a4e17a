"use client"

import { useState } from "react"
import { Check, ChevronDown, Plus, Search, Trash2 } from "lucide-react"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useCredentials, useDeleteCredential } from "@/hooks/use-credentials"
import { AddCredentialDialog } from "./add-credential-dialog"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

interface ApiKeySelectorProps {
  value?: string | null
  onValueChange: (value: string | null) => void
  placeholder?: string
  className?: string
}

export function ApiKeySelector({ value, onValueChange, placeholder = "Select an API key", className }: ApiKeySelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [credentialToDelete, setCredentialToDelete] = useState<{ id: string; name: string } | null>(null)

  const { data: credentialsResponse, isLoading } = useCredentials()
  const deleteCredential = useDeleteCredential()
  const credentials = credentialsResponse?.credentials || []

  // Filter credentials based on search query
  const filteredCredentials = credentials.filter(credential =>
    credential.key_name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const selectedCredential = credentials.find(cred => cred.id === value)

  const handleSelect = (credentialId: string) => {
    onValueChange(credentialId)
    setIsOpen(false)
    setSearchQuery("")
  }

  const handleAddSuccess = () => {
    // The credentials list will be automatically refetched due to query invalidation
  }

  const handleDeleteClick = (credentialId: string, credentialName: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setCredentialToDelete({ id: credentialId, name: credentialName })
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!credentialToDelete) return

    try {
      await deleteCredential.mutateAsync(credentialToDelete.id)
      toast.success("API key deleted successfully")

      // If the deleted credential was selected, clear the selection
      if (value === credentialToDelete.id) {
        onValueChange(null)
      }
    } catch (error) {
      console.error("Error deleting credential:", error)
      toast.error("Failed to delete API key")
    } finally {
      setDeleteDialogOpen(false)
      setCredentialToDelete(null)
    }
  }

  if (isLoading) {
    return (
      <div className={cn("h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm", className)}>
        <span className="text-muted-foreground">Loading...</span>
      </div>
    )
  }

  return (
    <>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className={cn("w-full justify-between", className)}
          >
            {selectedCredential ? selectedCredential.key_name : placeholder}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          {/* Search input and Add button */}
          <div className="p-2 border-b">
            <div className="flex gap-2 mb-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search API keys..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsAddDialogOpen(true)
                  setIsOpen(false)
                }}
                className="shrink-0"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Credentials list */}
          {filteredCredentials.length > 0 ? (
            <div className="p-1">
              {filteredCredentials.map((credential) => (
                <div
                  key={credential.id}
                  className="flex items-center justify-between p-2 hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer"
                  onClick={() => handleSelect(credential.id)}
                >
                  <div className="flex items-center flex-1">
                    <span className="text-sm">{credential.key_name}</span>
                    {value === credential.id && (
                      <Check className="ml-2 h-4 w-4 text-primary" />
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-50 hover:opacity-100 hover:text-destructive"
                    onClick={(e) => handleDeleteClick(credential.id, credential.key_name, e)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          ) : searchQuery ? (
            <div className="py-4 text-center text-sm text-muted-foreground">
              No API keys found matching &quot;{searchQuery}&quot;
            </div>
          ) : (
            <div className="py-4 text-center text-sm text-muted-foreground">
              No API keys available
            </div>
          )}
        </PopoverContent>
      </Popover>

      <AddCredentialDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSuccess={handleAddSuccess}
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete API Key</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the API key &quot;{credentialToDelete?.name}&quot;? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={deleteCredential.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteCredential.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
