"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from "recharts"
import { useState } from "react"

const data = [
  { name: "Order Processing", value: 3452, color: "#3b82f6" },
  { name: "User Onboarding", value: 2100, color: "#3b82f6" },
  { name: "Document Analysis", value: 1800, color: "#3b82f6" },
  { name: "Email Summarizer", value: 1500, color: "#3b82f6" },
  { name: "Data Validation", value: 1200, color: "#3b82f6" },
]

export function WorkflowChart() {
  const [hoveredWorkflow, setHoveredWorkflow] = useState<number | null>(null);

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Top Workflows by Execution</CardTitle>
        <p className="text-sm text-muted-foreground">
          Most frequently executed workflows in the last 7 days
        </p>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] relative">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              layout="vertical"
              data={data}
              margin={{ top: 10, right: 30, left: 120, bottom: 10 }}
              onMouseMove={(data) => {
                if (data.isTooltipActive && typeof data.activeTooltipIndex === 'number') {
                  setHoveredWorkflow(data.activeTooltipIndex);
                } else {
                  setHoveredWorkflow(null);
                }
              }}
              onMouseLeave={() => setHoveredWorkflow(null)}
            >
              <CartesianGrid strokeDasharray="3 3" horizontal={false} />
              <XAxis
                type="number"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
                domain={[0, 3600]}
                tickFormatter={(value) => `${value}`}
              />
              <YAxis
                dataKey="name"
                type="category"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, textAnchor: 'end' }}
                width={120}
                dx={-10}
              />
              <Tooltip
                cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    return (
                      <div className="bg-background border rounded p-2 shadow-sm">
                        <p className="font-medium">{payload[0].payload.name}</p>
                        <p className="text-sm text-muted-foreground">
                          executions : {payload[0].value}
                        </p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Bar
                dataKey="value"
                fill="#3b82f6"
                radius={[0, 4, 4, 0]}
                barSize={30}
                cursor="pointer"
              />
            </BarChart>
          </ResponsiveContainer>

          {hoveredWorkflow !== null && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background border rounded p-3 shadow-md">
              <p className="font-medium">{data[hoveredWorkflow].name}</p>
              <p className="text-sm text-muted-foreground">
                executions : {data[hoveredWorkflow].value}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
