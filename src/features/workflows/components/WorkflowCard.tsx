"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Eye, Wrench } from "lucide-react";

interface WorkflowCardProps {
  workflow: {
    id: string;
    name: string;
    description: string | null;
    tools: string[];
    agents?: string[];
    status: string;
    visibility: string;
    createdAt: string;
    lastRun?: string;
  };
  onViewApiDocs: (workflowId: string) => void;
}

export function WorkflowCard({ workflow, onViewApiDocs }: WorkflowCardProps) {
  const router = useRouter();

  const handleViewWorkflowDetails = (workflowId: string) => {
    router.push(`/dashboard/workflows/${workflowId}`);
  };

  // Get status color
  const getStatusColor = (status: string) => {
    return status === "active" ? "text-green-500" : "text-red-500";
  };

  return (
    <div className="rounded-lg border bg-card shadow-sm">
      <div className="p-3 xs:p-4 sm:p-5">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-xs xs:text-sm sm:text-base truncate">{workflow.name}</h3>
              <span className={`flex items-center gap-1 text-xs font-medium ml-1 xs:ml-2 flex-shrink-0 ${getStatusColor(workflow.status)}`}>
                <span className={`h-1.5 w-1.5 xs:h-2 xs:w-2 rounded-full ${workflow.status === "active" ? "bg-green-500" : "bg-red-500"}`}></span>
                {workflow.status}
              </span>
            </div>
            <p className="mt-0.5 xs:mt-1 text-xs text-muted-foreground truncate">
              ID: {workflow.id.substring(0, 8)}...
            </p>
            <p className="mt-1.5 xs:mt-2 sm:mt-3 text-xs sm:text-sm line-clamp-2">
              {workflow.description || "No description provided"}
            </p>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mt-2 xs:mt-3">
          <Badge variant="outline" className="text-xs">
            {workflow.visibility}
          </Badge>
          <Badge variant="outline" className="text-xs flex items-center gap-1">
            <Clock className="h-3 w-3" />
            {workflow.lastRun || "Never run"}
          </Badge>
        </div>

        <div className="mt-3 xs:mt-4 sm:mt-5">
          <div className="flex flex-col xs:flex-row gap-1.5 xs:gap-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full h-7 xs:h-8 sm:h-9 text-[10px] xs:text-xs sm:text-sm px-1.5 xs:px-2 sm:px-3"
              onClick={() => onViewApiDocs(workflow.id)}
            >
              <Wrench className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
              <span className="truncate">API Integration</span>
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="w-full h-7 xs:h-8 sm:h-9 text-[10px] xs:text-xs sm:text-sm px-1.5 xs:px-2 sm:px-3"
              onClick={() => handleViewWorkflowDetails(workflow.id)}
            >
              <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
              <span className="truncate">View</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
