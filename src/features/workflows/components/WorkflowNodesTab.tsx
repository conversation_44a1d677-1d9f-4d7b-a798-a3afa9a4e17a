"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Workflow } from "@/services/workflow-service";
import { AlertCircle, Server, Component } from "lucide-react";

interface AvailableNode {
  name: string;
  id: string;
  type: string;
  data?: {
    display_name?: string;
    input_schema?: any;
    output_schema?: any;
  };
}

interface WorkflowNodesTabProps {
  workflow: Workflow;
  available_nodes?: AvailableNode[];
  isLoading?: boolean;
}

export function WorkflowNodesTab({ workflow, available_nodes = [], isLoading = false }: WorkflowNodesTabProps) {
  // Render loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="overflow-hidden border rounded-md shadow-sm">
            <CardHeader className="pb-1 pt-3 px-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-6 w-6 rounded-md" />
                <Skeleton className="h-3 w-20" />
              </div>
              <Skeleton className="h-2 w-24 mt-1" />
            </CardHeader>
            <CardContent className="px-3 pb-3 pt-0">
              <div className="space-y-1">
                <Skeleton className="h-2 w-12" />
                <div className="flex flex-wrap gap-1 mt-1">
                  <Skeleton className="h-4 w-14 rounded-full" />
                  <Skeleton className="h-4 w-16 rounded-full" />
                  <Skeleton className="h-4 w-12 rounded-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // If no nodes are found
  if (!available_nodes || available_nodes.length === 0) {
    return (
      <div className="border rounded-md p-4 text-center text-muted-foreground bg-muted/10">
        <AlertCircle className="h-5 w-5 mx-auto mb-1.5 text-muted-foreground/70" />
        <p className="font-medium text-sm mb-0.5">No nodes configured</p>
        <p className="text-xs">This workflow doesnt have any processing nodes defined yet.</p>
      </div>
    );
  }

  // Render available nodes
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
      {available_nodes.map((node, index) => (
        <Card
          key={index}
          className="overflow-hidden border rounded-md shadow-sm"
        >
          <CardHeader className="pb-1 pt-3 px-3">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-md bg-muted">
                {node.type === "mcp" ? (
                  <Server className="h-3.5 w-3.5" />
                ) : (
                  <Component className="h-3.5 w-3.5" />
                )}
              </div>
              <CardTitle className="text-xs">
                {node.name}
              </CardTitle>
            </div>
            <CardDescription className="text-[10px] mt-0.5 truncate">
              Type: {node.type}
            </CardDescription>
          </CardHeader>
          <CardContent className="px-3 pb-3 pt-0">
            <div className="space-y-1">
              <h4 className="text-[10px] font-medium text-muted-foreground">Type:</h4>
              <div className="flex flex-wrap gap-1">
                <Badge
                  variant="outline"
                  className="flex items-center gap-0.5 text-[10px] py-0 px-1.5 border rounded-full"
                >
                  {node.type}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
