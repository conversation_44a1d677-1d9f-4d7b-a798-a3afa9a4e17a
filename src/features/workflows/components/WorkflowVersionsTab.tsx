import React from 'react';
import { useWorkflowVersions } from '@/hooks/use-workflows';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal } from 'lucide-react';
import { format } from 'date-fns';

interface WorkflowVersionsTabProps {
  workflowId: string;
}

export const WorkflowVersionsTab: React.FC<WorkflowVersionsTabProps> = ({
  workflowId,
}) => {
  const {
    data: versionsResponse,
    isLoading,
    isError,
    error,
  } = useWorkflowVersions(workflowId);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-1/4" />
        <Skeleton className="h-4 w-1/2" />
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[150px]">Version Number</TableHead>
                <TableHead>Changelog</TableHead>
                <TableHead className="w-[200px]">Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(3)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-full" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <Terminal className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load workflow versions: {error?.message || 'Unknown error'}
        </AlertDescription>
      </Alert>
    );
  }

  if (!versionsResponse || versionsResponse.versions.length === 0) {
    return (
      <div>
        <h2 className="text-2xl font-semibold tracking-tight mb-2">Version History</h2>
        <p className="text-sm text-muted-foreground mb-4">
          View all versions of this workflow and their changes over time.
        </p>
        <Alert>
          <Terminal className="h-4 w-4" />
          <AlertTitle>No Versions Found</AlertTitle>
          <AlertDescription>
            There are no versions available for this workflow yet.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const { versions, current_version_id } = versionsResponse;

  return (
    <div>
      <h2 className="text-2xl font-semibold tracking-tight mb-2">Version History</h2>
      <p className="text-sm text-muted-foreground mb-4">
        View all versions of this workflow and their changes over time.
      </p>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[150px]">Version Number</TableHead>
              <TableHead>Changelog</TableHead>
              <TableHead className="w-[200px] text-right">Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {versions.map((version) => (
              <TableRow key={version.id} className={version.id === current_version_id ? 'bg-muted/50' : ''}>
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    {version.id === current_version_id && (
                      <span className="w-1 h-6 bg-primary mr-3 rounded-full"></span>
                    )}
                    {version.version_number}
                    {version.id === current_version_id && (
                      <Badge variant="outline" className="ml-2 bg-green-100 text-green-700 border-green-300">
                        Current
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>{version.changelog || version.description || 'N/A'}</TableCell>
                <TableCell className="text-right">
                  {format(new Date(version.created_at), 'MMM d, yyyy')}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};