"use client";

import {
  AlertD<PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import {
  AlertCircle,
  AlertTriangle,
  ArrowLeft,
  CheckCircle2,
  Clock,
  Edit,
  ExternalLink,
  Trash2,
  Upload,
} from "lucide-react";
import { useRouter } from "next/navigation";

import { getWorkflowBuilderUrl } from "@/lib/helpers";
import { Workflow } from "@/services/workflow-service";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { WorkflowNodesTab } from "./WorkflowNodesTab";
import { WorkflowVersionsTab } from "./WorkflowVersionsTab"; // Moved import here
import { useToggleWorkflowVisibility } from "@/hooks/use-workflows";

interface WorkflowDetailsProps {
  workflowId: string;
  workflow: Workflow | undefined;
  isLoading: boolean;
  refetchWorkflow?: () => void;
}

export function WorkflowDetails({
  workflowId,
  workflow,
  isLoading,
  refetchWorkflow,
}: WorkflowDetailsProps) {
  const router = useRouter();
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [visibilityConfirmDialog, setVisibilityConfirmDialog] = useState(false);
  const [pendingVisibilityValue, setPendingVisibilityValue] = useState(false);
  const [publishChangesModal, setPublishChangesModal] = useState(false);
  const [showSyncAlert, setShowSyncAlert] = useState(workflow?.is_updated ?? false);

  // Hook for toggling workflow visibility
  const toggleVisibilityMutation = useToggleWorkflowVisibility();

  // Check if workflow has updates and show modal
  useEffect(() => {
    if (workflow && workflow.is_updated && !isLoading) {
      setPublishChangesModal(true);
    }
  }, [workflow, isLoading]);

  // Sync alert should always reflect workflow.is_updated
  useEffect(() => {
    setShowSyncAlert(!!workflow?.is_updated);
  }, [workflow?.is_updated]);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy h:mm a");
    } catch {
      return dateString || "N/A";
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      case "draft":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400";
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return (
          <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
        );
      case "inactive":
        return (
          <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
        );
      case "draft":
        return (
          <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
        );
      default:
        return null;
    }
  };

  // Handle back button
  const handleBack = () => {
    router.back();
  };

  const handleEditWorkflow = (workflowId: string) => {
    const url = getWorkflowBuilderUrl();
    router.push(`${url}/workflows/${workflowId}`);
  };

  const handleDeleteClick = () => {
    setDeleteDialog(true);
  };

  const handleDelete = async () => {
    try {
      // Close the dialog immediately to prevent further API calls
      setDeleteDialog(false);

      // Call the delete API directly
      const { workflowService } = await import("@/services/workflow-service");
      await workflowService.deleteWorkflow(workflowId);

      // Show success message and navigate away
      toast.success("Workflow deleted successfully");
      router.push("/dashboard/workflows");
    } catch (error) {
      console.error("Error deleting workflow:", error);
      toast.error(
        "Failed to delete workflow: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  };

  const handleVisibilityToggle = (value: boolean) => {
    // Check if workflow is imported
    if (workflow?.is_imported && value) {
      toast.error("Workflow is imported and cannot be made public");
      return;
    }

    setPendingVisibilityValue(value);
    setVisibilityConfirmDialog(true);
  };

  const confirmVisibilityToggle = async () => {
    try {
      setVisibilityConfirmDialog(false);

      // Call the API to toggle visibility
      const response = await toggleVisibilityMutation.mutateAsync(workflowId);

      if (response.success) {
        toast.success(
          `Workflow is now ${pendingVisibilityValue ? "public" : "private"}`
        );
      } else {
        toast.error(`Failed to update visibility: ${response.message}`);
      }
    } catch (error) {
      console.error("Error updating visibility:", error);
      toast.error(
        "Failed to update visibility: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  };

  // Handler for publishing changes
  const [isPublishing, setIsPublishing] = useState(false);
  const handlePublishChangesAlert = async () => {
    try {
      setIsPublishing(true);
      const { workflowService } = await import("@/services/workflow-service");
      const response = await workflowService.createVersionAndPublish(workflowId, true);
      if (response.success) {
        toast.success("Changes published to marketplace successfully!");
        setShowSyncAlert(false);
        if (refetchWorkflow) refetchWorkflow();
      } else {
        toast.error(`Failed to publish changes: ${response.message}`);
      }
    } catch (error) {
      console.error("Error publishing changes:", error);
      toast.error(
        "Failed to publish changes: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    } finally {
      setIsPublishing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBack}
              className="p-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <span className="text-sm text-muted-foreground">
              Back to Workflows
            </span>
          </div>
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold tracking-tight">
              {isLoading ? <Skeleton className="h-8 w-48" /> : workflow?.name}
            </h1>
          </div>
          {!isLoading && workflow && (
            <div className="flex items-center gap-2 mt-1">
              <span
                className={`px-2.5 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 ${getStatusColor(
                  workflow.status
                )}`}
              >
                {getStatusIcon(workflow.status)}
                <span className="capitalize">{workflow.status}</span>
              </span>
              {workflow.visibility && (
                <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                  {workflow.visibility === "private" ? "Private" : "Public"}
                </span>
              )}
              {workflow.version && (
                <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400">
                  v{workflow.version}
                </span>
              )}
              <span className="text-xs text-muted-foreground">
                Created: {formatDate(workflow.created_at).split(",")[0]}
              </span>
            </div>
          )}
        </div>
        <div>
          {!isLoading && workflow && (
            <Button
              variant="outline"
              className="gap-1"
              onClick={() => handleEditWorkflow(workflow.id)}
            >
              <Edit className="h-4 w-4" />
              Edit Workflow
            </Button>
          )}
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-1/3 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <Skeleton className="h-4 w-4/6" />
              <div className="flex gap-2 mt-4">
                <Skeleton className="h-6 w-24 rounded-full" />
                <Skeleton className="h-6 w-24 rounded-full" />
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card
                key={i}
                className="overflow-hidden border rounded-md shadow-sm"
              >
                <CardHeader className="pb-1 pt-3 px-3">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-6 w-6 rounded-md" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                  <Skeleton className="h-2 w-24 mt-1" />
                </CardHeader>
                <CardContent className="px-3 pb-3 pt-0">
                  <Skeleton className="h-10 w-full rounded-md" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Workflow details */}
      {!isLoading && workflow && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{workflow.description || "No description provided"}</p>
            </CardContent>
          </Card>

          {/* Sync Changes to Marketplace Alert - now after description, yellow color */}
          {showSyncAlert && workflow.is_updated && (
            <div className="flex items-center justify-between gap-4 p-4 my-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-md border border-yellow-300 dark:border-yellow-700">
              <div className="flex items-center gap-3">
                <Upload className="h-5 w-5 text-yellow-600" />
                <div>
                  <div className="font-medium text-yellow-900 dark:text-yellow-200">You have unpublished changes</div>
                  <div className="text-sm text-yellow-800 dark:text-yellow-200">Publish your latest changes to the marketplace so others can access the updated version.</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  className="bg-yellow-600 text-white hover:bg-yellow-700 dark:bg-yellow-600 dark:hover:bg-yellow-700"
                  onClick={handlePublishChangesAlert}
                  disabled={isPublishing}
                >
                  {isPublishing ? "Publishing..." : "Publish Changes"}
                </Button>
                <Button
                  size="icon"
                  variant="ghost"
                  className="text-yellow-600 hover:bg-yellow-100 dark:text-yellow-400 dark:hover:bg-yellow-800"
                  onClick={() => setShowSyncAlert(false)}
                  aria-label="Close alert"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                </Button>
              </div>
            </div>
          )}

          <div className="mt-6">
            <Tabs defaultValue="nodes">
              <TabsList>
                <TabsTrigger value="nodes">Nodes</TabsTrigger>
                <TabsTrigger value="triggers">Triggers</TabsTrigger>
                <TabsTrigger value="versions">Versions</TabsTrigger>
              </TabsList>

              <TabsContent value="nodes" className="mt-6">
                <WorkflowNodesTab
                  workflow={workflow}
                  available_nodes={workflow.available_nodes && workflow.available_nodes.length ? workflow.available_nodes : []}
                  isLoading={isLoading}
                />
              </TabsContent>

              <TabsContent value="triggers" className="mt-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {workflow.triggers && workflow.triggers.length > 0 ? (
                    workflow.triggers.map((trigger) => (
                      <Card
                        key={trigger.id}
                        className="overflow-hidden border rounded-md shadow-sm"
                      >
                        <CardHeader className="pb-1 pt-3 px-3">
                          <div className="flex items-center gap-2">
                            <div className="p-1.5 rounded-md bg-muted">
                              {trigger.type === "http" ? (
                                <ExternalLink className="h-3.5 w-3.5" />
                              ) : (
                                <Clock className="h-3.5 w-3.5" />
                              )}
                            </div>
                            <CardTitle className="text-xs">
                              {trigger.name}
                            </CardTitle>
                          </div>
                          <CardDescription className="text-[10px] mt-0.5">
                            {trigger.type}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="px-3 pb-3 pt-0">
                          {trigger.value && (
                            <div className="bg-muted p-1.5 rounded-md text-[10px] font-mono overflow-x-auto">
                              {trigger.value}
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <div className="col-span-3 border rounded-md p-4 text-center text-muted-foreground bg-muted/10">
                      <AlertCircle className="h-5 w-5 mx-auto mb-1.5 text-muted-foreground/70" />
                      <p className="font-medium text-sm mb-0.5">
                        No triggers configured
                      </p>
                      <p className="text-xs">
                        This workflow doesnt have any triggers defined yet.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="versions" className="mt-6">
                <WorkflowVersionsTab workflowId={workflowId} />
              </TabsContent>
            </Tabs>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Workflow Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Public Access</h3>
                    <p className="text-sm text-muted-foreground">
                      Make this workflow available in the public marketplace
                    </p>
                  </div>
                  <Switch
                    checked={workflow.visibility === "public"}
                    onCheckedChange={handleVisibilityToggle}
                  />
                </div>

                <div className="flex items-center justify-between border-t pt-6">
                  <div>
                    <h3 className="font-medium">Customizable Workflow</h3>
                    <p className="text-sm text-muted-foreground">
                      Allow users to customize this workflow when they use it
                    </p>
                  </div>
                  <Switch
                    checked={workflow.is_customizable}
                    onCheckedChange={() => {}}
                  />
                </div>

                <div className="flex gap-4">
                  <Button variant="outline" className="gap-1">
                    <AlertCircle className="h-4 w-4" />
                    Deactivate Workflow
                  </Button>
                  <Button
                    variant="outline"
                    className="gap-1 text-destructive hover:text-destructive"
                    onClick={handleDeleteClick}
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete Workflow
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteDialog} onOpenChange={setDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Workflow</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this workflow? This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="flex items-center gap-2 p-3 bg-destructive/10 rounded-md my-4">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <p className="text-sm text-destructive">
              This will permanently delete the workflow and all associated data.
            </p>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Visibility confirmation dialog */}
      <AlertDialog
        open={visibilityConfirmDialog}
        onOpenChange={setVisibilityConfirmDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {pendingVisibilityValue
                ? "Make Workflow Public"
                : "Make Workflow Private"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {pendingVisibilityValue
                ? "Are you sure you want to make this workflow public? It will be visible in the marketplace."
                : "Are you sure you want to make this workflow private? It will no longer be visible in the marketplace."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmVisibilityToggle}>
              {pendingVisibilityValue ? "Make Public" : "Make Private"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
