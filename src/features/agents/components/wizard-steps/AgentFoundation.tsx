"use client";

import { useState } from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  Form,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { AgentData } from "../AgentCreationWizard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Upload, Wand, Bot } from "lucide-react";
import { useForm } from "react-hook-form";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";

interface AgentFoundationProps {
  data: AgentData;
  updateData: (data: Partial<AgentData>) => void;
}

export function AgentFoundation({ data, updateData }: AgentFoundationProps) {
  const [avatarSource, setAvatarSource] = useState<"upload" | "generate">(
    "upload"
  );
  const [isGeneratingAvatar, setIsGeneratingAvatar] = useState(false);

  const form = useForm({
    defaultValues: {
      name: data.name,
      description: data.description,
      category: data.category,
    },
  });

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, this would upload the file to a server
      // For now, we'll just create a data URL
      const reader = new FileReader();
      reader.onload = () => {
        updateData({ image: reader.result as string });
      };
      reader.readAsDataURL(file);
      toast.success("Avatar uploaded successfully");
    }
  };

  const generateAvatar = () => {
    setIsGeneratingAvatar(true);

    // Simulate API call to generate avatar
    setTimeout(() => {
      // In a real app, this would call an API to generate an avatar
      updateData({
        image: `https://api.dicebear.com/7.x/bottts/svg?seed=${Date.now()}`,
      });
      setIsGeneratingAvatar(false);
      toast.success("Avatar generated successfully");
    }, 1500);
  };

  const onSubmit = (values: Record<string, unknown>) => {
    updateData({
      name: values.name as string,
      description: values.description as string,
      category: values.category as string,
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold tracking-tight">
          Agent Foundation
        </h2>
        <p className="text-muted-foreground">
          Set up the basic information for your AI agent
        </p>
      </div>

      <Form {...form}>
        <form
          onChange={() => form.handleSubmit(onSubmit)()}
          className="space-y-6"
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Agent Name</FormLabel>
                <FormControl>
                  <Input placeholder="Customer Support Assistant" {...field} />
                </FormControl>
                <FormDescription>
                  A clear, descriptive name for your agent
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Agent Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="This agent helps customers with support requests and guides them through troubleshooting steps..."
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Describe what the agent does and its purpose
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Agent Category</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="General">General</SelectItem>
                    <SelectItem value="Customer Support">
                      Customer Support
                    </SelectItem>
                    <SelectItem value="Sales">Sales</SelectItem>
                    <SelectItem value="Marketing">Marketing</SelectItem>
                    <SelectItem value="HR">HR</SelectItem>
                    <SelectItem value="IT">IT</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="Legal">Legal</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Categorize your agent for better organization
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div>
            <FormLabel>Agent Avatar</FormLabel>
            <div className="flex items-start gap-6 mt-2">
              <div className="flex-shrink-0">
                <Avatar className="h-24 w-24 rounded-md">
                  {data.image ? (
                    <AvatarImage src={data.image} alt="Agent avatar" />
                  ) : (
                    <AvatarFallback className="rounded-md bg-primary/10 text-primary">
                      <Bot className="h-12 w-12" />
                    </AvatarFallback>
                  )}
                </Avatar>
              </div>

              <div className="space-y-4 flex-1">
                <RadioGroup
                  defaultValue={avatarSource}
                  onValueChange={(value) =>
                    setAvatarSource(value as "upload" | "generate")
                  }
                  className="flex flex-col space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="upload" id="upload" />
                    <Label htmlFor="upload" className="flex items-center gap-1">
                      <Upload className="h-4 w-4" /> Upload Image
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="generate" id="generate" />
                    <Label
                      htmlFor="generate"
                      className="flex items-center gap-1"
                    >
                      <Wand className="h-4 w-4" /> Generate with AI
                    </Label>
                  </div>
                </RadioGroup>

                {avatarSource === "upload" ? (
                  <div>
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="max-w-md"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      Recommended: Square image, at least 256x256px
                    </p>
                  </div>
                ) : (
                  <div>
                    <Button
                      variant="outline"
                      onClick={generateAvatar}
                      disabled={isGeneratingAvatar}
                      className="flex items-center gap-1"
                    >
                      {isGeneratingAvatar ? "Generating..." : "Generate Avatar"}
                      <Wand className="h-4 w-4" />
                    </Button>
                    <p className="text-sm text-muted-foreground mt-1">
                      AI will generate an avatar based on the agent description
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
