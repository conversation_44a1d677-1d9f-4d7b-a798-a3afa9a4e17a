"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  CheckCircle2,
  ChevronRight,
  Info,
  Bot,
  Lightbulb,
  Wrench,
  Workflow,
  FileText,
  Variable,
} from "lucide-react";

// Step components
import { AgentFoundation } from "@/components/agents/wizard-steps/agent-foundation";
import { AgentCoreLogic } from "@/components/agents/wizard-steps/agent-core-logic";
import { AgentCapabilities } from "@/components/agents/wizard-steps/agent-capabilities";
import { AgentAutomation } from "@/components/agents/wizard-steps/agent-automation";
import { AgentKnowledge } from "@/components/agents/wizard-steps/agent-knowledge";
import { AgentConfiguration } from "@/components/agents/wizard-steps/agent-configuration";
import { AgentPreview } from "@/components/agents/wizard-steps/agent-preview";

export type AgentData = {
  // Foundation
  name: string;
  description: string;
  image: string | null;
  category: string;
  role: string;
  tone: string;

  // Core Logic
  systemPrompt: string;
  aiProvider: string | undefined;
  aiModel: string | undefined;
  apiKeyId: string | null;
  ruhCredentials: boolean;

  // Capabilities
  selectedTools: string[];
  marketplaceTools: string[];
  mcp_server_ids: string[]; // Added this field to match the type in agent-creation-wizard.tsx
  workflow_ids: string[]; // Added this field to match the type in agent-creation-wizard.tsx
  capabilities: Array<{
    title: string;
    description: string;
  }>;
  // Advanced capabilities
  streaming: boolean;
  pushNotifications: boolean;
  stateTransitionHistory: boolean;
  examples: string[];
  inputModes: string[];
  outputModes: string[];

  // Automation
  workflows: string[];

  // Knowledge
  knowledgeSources: Array<{
    type: "document" | "url" | "text";
    content: string;
    name: string;
  }>;

  // Configuration
  variables: Array<{
    name: string;
    description: string;
    type: "text" | "number" | "json";
    default_value: string | null;
  }>;
};

const steps = [
  { id: "foundation", label: "Foundation", icon: <Bot className="h-5 w-5" /> },
  {
    id: "core-logic",
    label: "Core Logic",
    icon: <Lightbulb className="h-5 w-5" />,
  },
  {
    id: "capabilities",
    label: "Capabilities",
    icon: <Wrench className="h-5 w-5" />,
  },
  {
    id: "automation",
    label: "Automation",
    icon: <Workflow className="h-5 w-5" />,
  },
  {
    id: "knowledge",
    label: "Knowledge",
    icon: <FileText className="h-5 w-5" />,
  },
  {
    id: "configuration",
    label: "Configuration",
    icon: <Variable className="h-5 w-5" />,
  },
  { id: "preview", label: "Preview", icon: <Info className="h-5 w-5" /> },
];

interface AgentCreationWizardProps {
  agentId?: string;
  currentStep: string;
  setCurrentStep: (step: string) => void;
  initialData?: Partial<AgentData> | null;
}

export function AgentCreationWizard({
  agentId,
  currentStep,
  setCurrentStep,
  initialData,
}: AgentCreationWizardProps) {
  const router = useRouter();
  const [stepIndex, setStepIndex] = useState(0);

  // Find the index of the current step
  useEffect(() => {
    const index = steps.findIndex((step) => step.id === currentStep);
    setStepIndex(index >= 0 ? index : 0);
  }, [currentStep]);

  // Load agent data if editing an existing agent or if initialData is provided
  useEffect(() => {
    const loadAgentData = async () => {
      // If initialData is provided, use it
      if (initialData) {
        updateAgentData(initialData);
        toast.success("Agent configuration loaded");
        return;
      }

      // Otherwise, if agentId is provided, use dummy data for now
      if (agentId) {
        try {
          // Use dummy data instead of making API calls
          const dummyAgent = {
            name: "Customer Support Agent",
            description:
              "This agent helps customers with support requests and guides them through troubleshooting steps.",
            avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=1234",
            category: "Customer Support",
            system_message:
              "You are a helpful customer support agent. Your goal is to assist customers with their inquiries and resolve their issues efficiently.",
            model_provider: "OpenAI",
            model_name: "gpt-4o",
          };

          // Map dummy data to our agent data structure
          updateAgentData({
            name: dummyAgent.name,
            description: dummyAgent.description,
            image: dummyAgent.avatar,
            category: dummyAgent.category,
            role: "assistant",
            tone: "professional",
            systemPrompt: dummyAgent.system_message,
            aiProvider: dummyAgent.model_provider,
            aiModel: dummyAgent.model_name,
            mcp_server_ids: [], // Added empty array for mcp_server_ids
            capabilities: [],
          });

          toast.success("Agent data loaded successfully");
        } catch (error) {
          console.error("Error loading agent data:", error);
          toast.error("Failed to load agent data");
        }
      }
    };

    loadAgentData();
  }, [agentId, initialData]);

  const [agentData, setAgentData] = useState<AgentData>({
    // Foundation
    name: "",
    description: "",
    image: null,
    category: "General",
    role: "",
    tone: "professional",

    // Core Logic
    systemPrompt: "",
    aiProvider: "OpenAI",
    aiModel: "gpt-4o",
    apiKeyId: null,
    ruhCredentials: false,

    // Capabilities
    selectedTools: ["web-search", "calculator"],
    marketplaceTools: ["mcp-slack"],
    mcp_server_ids: [], // Added this field to match the type in agent-creation-wizard.tsx
    workflow_ids: [], // Added this field to match the type in agent-creation-wizard.tsx
    capabilities: [],
    // Advanced capabilities
    streaming: false,
    pushNotifications: false,
    stateTransitionHistory: false,
    examples: ["How can I help you today?", "Tell me about your product"],
    inputModes: ["text"],
    outputModes: ["text"],

    // Automation
    workflows: [],

    // Knowledge
    knowledgeSources: [
      {
        type: "text",
        name: "Sample Knowledge",
        content:
          "This is a sample knowledge base item to help you get started.",
      },
    ],

    // Configuration
    variables: [],
  });

  // Update URL when step changes
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    params.set("step", steps[stepIndex].id);

    // Update URL without causing a navigation
    window.history.replaceState(
      {},
      "",
      `${window.location.pathname}?${params.toString()}`
    );

    // Update the parent component's state
    setCurrentStep(steps[stepIndex].id);
  }, [stepIndex, setCurrentStep]);

  const updateAgentData = (data: Partial<AgentData>) => {
    setAgentData((prev) => ({ ...prev, ...data }));
  };

  // Validate the current step
  const validateCurrentStep = (): boolean => {
    switch (steps[stepIndex].id) {
      case "foundation":
        if (!agentData.name || agentData.name.trim() === "") {
          toast.error("Please enter an agent name");
          return false;
        }
        if (!agentData.description || agentData.description.trim() === "") {
          toast.error("Please enter an agent description");
          return false;
        }
        return true;

      case "core-logic":
        if (!agentData.systemPrompt || agentData.systemPrompt.trim() === "") {
          toast.error("Please enter a system prompt");
          return false;
        }
        return true;

      case "capabilities":
        if (agentData.selectedTools.length === 0) {
          toast.error("Please select at least one tool");
          return false;
        }
        return true;

      // Other steps are optional
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (stepIndex < steps.length - 1) {
      // Validate the current step before proceeding
      if (!validateCurrentStep()) {
        return;
      }

      const nextStep = stepIndex + 1;
      setStepIndex(nextStep);
      window.scrollTo(0, 0);

      // Show success toast for completed steps
      if (steps[stepIndex].id === "foundation") {
        toast.success("Foundation details saved");
      } else if (steps[stepIndex].id === "core-logic") {
        toast.success("Core logic configured");
      } else if (steps[stepIndex].id === "capabilities") {
        toast.success("Capabilities configured");
      }
    }
  };

  const handlePrevious = () => {
    if (stepIndex > 0) {
      const prevStep = stepIndex - 1;
      setStepIndex(prevStep);
      window.scrollTo(0, 0);
    }
  };

  const handleSaveDraft = async () => {
    try {
      // Validate the final step
      if (!validateCurrentStep()) {
        return;
      }

      // Show loading toast
      const loadingToast = toast.loading(
        agentId ? "Updating agent..." : "Saving agent as draft..."
      );

      // Simulate API call with a delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success(
        agentId ? "Agent updated successfully" : "Agent saved as draft"
      );

      // Navigate back to agents list
      router.push("/dashboard/agents");
    } catch (error) {
      console.error("Error saving agent:", error);
      toast.error(
        "Failed to save agent: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  };

  const handlePublish = async () => {
    try {
      // Validate the final step
      if (!validateCurrentStep()) {
        return;
      }

      // Show loading toast
      const loadingToast = toast.loading(
        agentId ? "Updating agent..." : "Publishing agent..."
      );

      // Simulate API call with a delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success(
        agentId
          ? "Agent updated and published successfully"
          : "Agent published successfully"
      );

      // Navigate back to agents list
      router.push("/dashboard/agents");
    } catch (error) {
      console.error("Error publishing agent:", error);
      toast.error(
        "Failed to publish agent: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  };

  // Function to render the current step component
  const renderStepContent = () => {
    try {
      switch (steps[stepIndex].id) {
        case "foundation":
          return (
            <AgentFoundation data={agentData} updateData={updateAgentData} />
          );
        case "core-logic":
          return (
            <AgentCoreLogic data={agentData} updateData={updateAgentData} />
          );
        case "capabilities":
          return (
            <AgentCapabilities data={agentData} updateData={updateAgentData} />
          );
        case "automation":
          return (
            <AgentAutomation data={agentData} updateData={updateAgentData} />
          );
        case "knowledge":
          return (
            <AgentKnowledge data={agentData} updateData={updateAgentData} />
          );
        case "configuration":
          return (
            <AgentConfiguration data={agentData} updateData={updateAgentData} />
          );
        case "preview":
          return <AgentPreview data={agentData} />;
        default:
          return <div>Unknown step</div>;
      }
    } catch (error) {
      console.error("Error rendering step content:", error);
      return <div>Error loading step content</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress steps */}
      <div className="hidden md:block">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex flex-col items-center">
              <div
                className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                  index < stepIndex
                    ? "bg-primary border-primary text-primary-foreground"
                    : index === stepIndex
                    ? "border-primary text-primary"
                    : "border-muted-foreground/30 text-muted-foreground/50"
                }`}
              >
                {index < stepIndex ? (
                  <CheckCircle2 className="h-5 w-5" />
                ) : (
                  step.icon
                )}
              </div>
              <span
                className={`mt-2 text-xs font-medium ${
                  index <= stepIndex
                    ? "text-foreground"
                    : "text-muted-foreground/50"
                }`}
              >
                {step.label}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-4 h-0.5 w-full bg-muted-foreground/20">
          <div
            className="absolute h-0.5 bg-primary transition-all duration-300"
            style={{ width: `${(stepIndex / (steps.length - 1)) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Mobile steps */}
      <div className="md:hidden">
        <Tabs value={steps[stepIndex].id} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="foundation">Foundation</TabsTrigger>
            <TabsTrigger value="core-logic">Core Logic</TabsTrigger>
            <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
          </TabsList>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="automation">Automation</TabsTrigger>
            <TabsTrigger value="knowledge">Knowledge</TabsTrigger>
            <TabsTrigger value="configuration">Config</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Step content */}
      <Card className="p-6">
        {renderStepContent()}

        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={stepIndex === 0}
          >
            Previous
          </Button>

          <div className="space-x-2">
            {stepIndex === steps.length - 1 ? (
              <>
                <Button variant="outline" onClick={handleSaveDraft}>
                  {agentId ? "Save Changes" : "Save as Draft"}
                </Button>
                <Button onClick={handlePublish}>
                  {agentId ? "Update & Publish" : "Publish Agent"}
                </Button>
              </>
            ) : (
              <Button onClick={handleNext}>
                Next <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
}
