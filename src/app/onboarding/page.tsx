"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { toast } from "sonner"
import { ArrowRight, Building2, User, Bot, Workflow, Code, Activity, Search } from "lucide-react"
import { getDocsUrl } from "@/lib/helpers"

// Define the steps of the onboarding process
const STEPS = {
  WELCOME: 0,
  PERSONAL_INFO: 1,
  ORGANIZATION: 2,
  GOALS: 3,
  COMPLETE: 4,
}

export default function OnboardingPage() {
  const router = useRouter()
  const [step, setStep] = useState(STEPS.WELCOME)

  // Form state
  const [formData, setFormData] = useState({
    fullName: "",
    role: "",
    technicalExperience: "intermediate", // Default to intermediate
    organizationName: "",
    organizationSize: "",
    industry: "",
    isTeam: false,
    goal: "",
    additionalInfo: "",
  })

  // Update form data
  const updateFormData = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // Handle next step
  const handleNext = () => {
    // Validate current step
    if (step === STEPS.PERSONAL_INFO) {
      if (!formData.fullName) {
        toast.error("Please enter your full name")
        return
      }
      if (!formData.role) {
        toast.error("Please select your role")
        return
      }
    } else if (step === STEPS.ORGANIZATION) {
      // Organization name is optional, so no validation needed
    } else if (step === STEPS.GOALS) {
      if (!formData.goal) {
        toast.error("Please select a goal")
        return
      }
    }

    // If we're at the last step, submit the form
    if (step === STEPS.COMPLETE) {
      // Submit form data and redirect to dashboard
      router.push("/dashboard/overview")
      return
    }

    // Move to the next step
    setStep((prev) => prev + 1)
  }

  // Handle back
  const handleBack = () => {
    setStep((prev) => prev - 1)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    handleNext()
  }

  // Render the welcome step
  const renderWelcome = () => (
    <div className="space-y-6 text-center">
      <h1 className="text-3xl font-bold">Welcome to RUH AI Platform!</h1>
      <p className="text-muted-foreground">
        Let&apos;s get your workspace set up.
      </p>
      <p className="text-muted-foreground">
        Build, connect, and manage intelligent agents and workflows. First, tell us a bit about yourself and what you plan to build.
      </p>
      <Button onClick={handleNext} className="w-full">
        Get Started
      </Button>
    </div>
  )

  // Render the personal info step
  const renderPersonalInfo = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Tell Us About You</h1>
        <p className="text-muted-foreground mt-2">
          Let us know a bit about your background so we can personalize your experience.
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="fullName">Full Name</Label>
          <Input
            id="fullName"
            placeholder="John Doe"
            value={formData.fullName}
            onChange={(e) => updateFormData("fullName", e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="role">Your Role</Label>
          <Select
            value={formData.role}
            onValueChange={(value) => updateFormData("role", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select your role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="developer">Developer</SelectItem>
              <SelectItem value="data_scientist">Data Scientist</SelectItem>
              <SelectItem value="product_manager">Product Manager</SelectItem>
              <SelectItem value="business_analyst">Business Analyst</SelectItem>
              <SelectItem value="executive">Executive</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Technical Experience</Label>
          <RadioGroup
            value={formData.technicalExperience}
            onValueChange={(value) => updateFormData("technicalExperience", value)}
            className="flex flex-col space-y-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="beginner" id="beginner" />
              <Label htmlFor="beginner" className="font-normal">Beginner (Just starting with APIs/Automation)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="intermediate" id="intermediate" />
              <Label htmlFor="intermediate" className="font-normal">Intermediate (Comfortable building integrations)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="advanced" id="advanced" />
              <Label htmlFor="advanced" className="font-normal">Advanced (Experienced with complex systems/AI)</Label>
            </div>
          </RadioGroup>
          <p className="text-xs text-muted-foreground mt-1">This helps us tailor examples and documentation for you.</p>
        </div>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={handleBack}>
          Back
        </Button>
        <Button type="submit">
          Next
        </Button>
      </div>
    </form>
  )

  // Render the organization step
  const renderOrganization = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">About Your Organization</h1>
        <p className="text-muted-foreground mt-2">
          Tell us about where you work to help us suggest relevant use cases.
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="organizationName">Organization Name</Label>
          <Input
            id="organizationName"
            placeholder="Acme Corp (or Personal Use)"
            value={formData.organizationName}
            onChange={(e) => updateFormData("organizationName", e.target.value)}
          />
          <p className="text-xs text-muted-foreground">Leave blank if you&apos;re using this for personal projects.</p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="organizationSize">Organization Size (Employees)</Label>
          <Select
            value={formData.organizationSize}
            onValueChange={(value) => updateFormData("organizationSize", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Just Me / Personal" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="personal">Just Me / Personal</SelectItem>
              <SelectItem value="small">2-10 employees</SelectItem>
              <SelectItem value="medium">11-50 employees</SelectItem>
              <SelectItem value="large">51-200 employees</SelectItem>
              <SelectItem value="enterprise">201+ employees</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="industry">Industry</Label>
          <Select
            value={formData.industry}
            onValueChange={(value) => updateFormData("industry", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select your industry (optional)" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="technology">Technology</SelectItem>
              <SelectItem value="finance">Finance</SelectItem>
              <SelectItem value="healthcare">Healthcare</SelectItem>
              <SelectItem value="education">Education</SelectItem>
              <SelectItem value="retail">Retail</SelectItem>
              <SelectItem value="manufacturing">Manufacturing</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">This helps us suggest relevant use cases.</p>
        </div>

        <div className="space-y-2">
          <Label>Are you part of a team using RUH AI Platform?</Label>
          <RadioGroup
            value={formData.isTeam ? "yes" : "no"}
            onValueChange={(value) => updateFormData("isTeam", value === "yes")}
            className="flex flex-col space-y-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yes" id="team-yes" />
              <Label htmlFor="team-yes" className="font-normal">Yes</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" id="team-no" />
              <Label htmlFor="team-no" className="font-normal">No</Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={handleBack}>
          Back
        </Button>
        <Button type="submit">
          Next
        </Button>
      </div>
    </form>
  )

  // Render the goals step
  const renderGoals = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">What do you want to achieve first?</h1>
        <p className="text-muted-foreground mt-2">
          Select the main reason you&apos;re signing up for RUH AI Platform today. This will help us guide you to the right starting point.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card
          className={`cursor-pointer hover:border-primary transition-colors ${formData.goal === "build_agent" ? "border-primary bg-primary/5" : ""}`}
          onClick={() => updateFormData("goal", "build_agent")}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Bot className="h-5 w-5 mt-1 text-primary" />
              <div>
                <h3 className="font-medium">Build a Custom Agent</h3>
                <p className="text-sm text-muted-foreground">I want to create an intelligent agent with specific skills and goals.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:border-primary transition-colors ${formData.goal === "automate_process" ? "border-primary bg-primary/5" : ""}`}
          onClick={() => updateFormData("goal", "automate_process")}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Workflow className="h-5 w-5 mt-1 text-primary" />
              <div>
                <h3 className="font-medium">Automate a Process</h3>
                <p className="text-sm text-muted-foreground">I need to set up a workflow to connect different steps or services.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:border-primary transition-colors ${formData.goal === "integrate_app" ? "border-primary bg-primary/5" : ""}`}
          onClick={() => updateFormData("goal", "integrate_app")}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Code className="h-5 w-5 mt-1 text-primary" />
              <div>
                <h3 className="font-medium">Integrate with my Application</h3>
                <p className="text-sm text-muted-foreground">I plan to use the APIs to trigger agents/workflows from my own code.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:border-primary transition-colors ${formData.goal === "monitoring" ? "border-primary bg-primary/5" : ""}`}
          onClick={() => updateFormData("goal", "monitoring")}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Activity className="h-5 w-5 mt-1 text-primary" />
              <div>
                <h3 className="font-medium">Set up Monitoring</h3>
                <p className="text-sm text-muted-foreground">I need to monitor the performance and usage of my agents/workflows.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:border-primary transition-colors ${formData.goal === "explore_tools" ? "border-primary bg-primary/5" : ""}`}
          onClick={() => updateFormData("goal", "explore_tools")}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <ArrowRight className="h-5 w-5 mt-1 text-primary" />
              <div>
                <h3 className="font-medium">Explore Tools & Capabilities</h3>
                <p className="text-sm text-muted-foreground">I want to see the available tools and understand what&apos;s possible.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:border-primary transition-colors ${formData.goal === "just_exploring" ? "border-primary bg-primary/5" : ""}`}
          onClick={() => updateFormData("goal", "just_exploring")}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Search className="h-5 w-5 mt-1 text-primary" />
              <div>
                <h3 className="font-medium">Just Exploring / Learning</h3>
                <p className="text-sm text-muted-foreground">I&apos;m new and want to look around the platform first.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-2">
        <Label htmlFor="additionalInfo">Tell us more (Briefly)</Label>
        <Textarea
          id="additionalInfo"
          placeholder="e.g., Build a customer support chatbot, Automate lead qualification, Integrate with Slack..."
          value={formData.additionalInfo}
          onChange={(e) => updateFormData("additionalInfo", e.target.value)}
          className="min-h-[80px]"
        />
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={handleBack}>
          Back
        </Button>
        <Button type="submit">
          Finish Setup
        </Button>
      </div>
    </form>
  )

  // Render the completion step
  const renderComplete = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">You&apos;re All Set!</h1>
        <p className="text-muted-foreground mt-2">
          Thanks, User! Your workspace is ready.
        </p>
      </div>

      <div className="space-y-4">
        <Card className="bg-primary/5 border">
          <CardContent className="p-6 flex items-center space-x-4">
            <div className="bg-primary/10 p-3 rounded-lg">
              <Bot className="h-6 w-6 text-primary" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">Build Your First Agent</h3>
              <p className="text-sm text-muted-foreground">We recommend starting by creating your first Agent.</p>
              <Button className="mt-3 w-full">Get Started</Button>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-6 flex items-center space-x-3">
              <div className="bg-muted p-2 rounded-lg">
                <User className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium">Documentation</h3>
                <p className="text-xs text-muted-foreground">Explore our comprehensive documentation.</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2 w-full"
                  onClick={() => window.open(getDocsUrl(), "_blank")}
                >
                  View
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 flex items-center space-x-3">
              <div className="bg-muted p-2 rounded-lg">
                <Building2 className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium">Community</h3>
                <p className="text-xs text-muted-foreground">Join our community forum/Discord</p>
                <Button variant="outline" size="sm" className="mt-2 w-full">View</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Button onClick={() => router.push("/dashboard/overview")} className="w-full">
        Go to Dashboard
      </Button>
    </div>
  )

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case STEPS.WELCOME:
        return renderWelcome()
      case STEPS.PERSONAL_INFO:
        return renderPersonalInfo()
      case STEPS.ORGANIZATION:
        return renderOrganization()
      case STEPS.GOALS:
        return renderGoals()
      case STEPS.COMPLETE:
        return renderComplete()
      default:
        return renderWelcome()
    }
  }

  return (
    <div className="space-y-6">
      {renderStep()}
    </div>
  )
}
