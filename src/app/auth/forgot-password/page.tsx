"use client"

import Link from "next/link"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { MailIcon } from "lucide-react"
import { toast } from "sonner"
import { useForgotPassword } from "@/hooks/use-auth"

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)

  const forgotPassword = useForgotPassword()
  const isLoading = forgotPassword.isPending

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      toast.error("Please enter your email address")
      return
    }

    forgotPassword.mutate(
      { email },
      {
        onError: (error) => {
          console.error("Password reset error:", error)
          toast.error(error.response?.data?.message || "Failed to send password reset email. Please try again.")
        },
        onSuccess: () => {
          setIsSubmitted(true)
          toast.success("Password reset instructions sent to your email")
        }
      }
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Forgot your password?</h1>
        <p className="text-sm text-muted-foreground mt-1">
          Enter your email to receive password reset instructions
        </p>
      </div>

      {isSubmitted ? (
        <div className="space-y-4">
          <div className="rounded-lg border p-4 text-center">
            <p className="text-sm">
              If an account exists with the email <strong>{email}</strong>, you will receive password reset instructions.
            </p>
          </div>
          <Button asChild className="w-full">
            <Link href="/auth/login">Return to login</Link>
          </Button>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <div className="relative">
              <MailIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className="pl-10"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Sending..." : "Reset Password"}
          </Button>

          <div className="text-center">
            <Link
              href="/auth/login"
              className="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
            >
              Back to login
            </Link>
          </div>
        </form>
      )}
    </div>
  )
}
