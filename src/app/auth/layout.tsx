"use client"

import Link from "next/link";
import Image from "next/image";
import { ThemeToggle } from "@/components/theme-toggle";

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      <div className="w-full max-w-md">
        <div className="flex justify-center mb-8">
          <Link href="/" className="flex items-center gap-2">
            <div className="flex h-10 w-10 items-center justify-center">
              <Image
                src="/logo.svg"
                alt="RUH AI Logo"
                width={30}
                height={30}
                className="h-10 w-10"
              />
            </div>
          </Link>
        </div>
        {children}
      </div>
    </div>
  );
}
