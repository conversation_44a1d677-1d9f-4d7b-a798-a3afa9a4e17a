"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function DocumentationPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">API Documentation</h1>
        <p className="text-muted-foreground">
          Complete reference and guides for the Ruh.ai Integration API
        </p>
      </div>

      <Tabs defaultValue="authentication" className="space-y-4">
        <TabsList>
          <TabsTrigger value="authentication">Authentication</TabsTrigger>
          <TabsTrigger value="workflows">Workflows API</TabsTrigger>
          <TabsTrigger value="agents">Agents API</TabsTrigger>
          <TabsTrigger value="sse">SSE Events</TabsTrigger>
        </TabsList>

        {/* Authentication Tab */}
        <TabsContent value="authentication" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Authentication</CardTitle>
              <CardDescription>Learn how to authenticate your API requests.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>Authentication is performed using API keys. You can create and manage your API keys in the API Keys section.</p>
              <p>All API requests require an <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">Authorization</code> header with your API key.</p>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Example Request</h3>
                <div className="rounded-md bg-muted p-4">
                  <pre className="font-mono text-sm">
                    <code>
{`curl -X GET https://api.ruh.ai/v1/workflows \\
  -H "Authorization: Bearer YOUR_API_KEY"`}
                    </code>
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Workflows API Tab */}
        <TabsContent value="workflows" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Workflows API</CardTitle>
              <CardDescription>Endpoints for managing workflows</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">List Workflows</h3>
                <p className="text-sm text-muted-foreground">Get a list of all workflows for the authenticated user.</p>
                
                <div className="rounded-md bg-muted p-4">
                  <p className="font-mono text-sm mb-2">GET /workflows/user</p>
                  <pre className="font-mono text-sm">
                    <code>
{`curl -X GET https://api.ruh.ai/v1/workflows/user \\
  -H "Authorization: Bearer YOUR_API_KEY"`}
                    </code>
                  </pre>
                </div>
                
                <h4 className="text-md font-medium mt-4">Response</h4>
                <div className="rounded-md bg-muted p-4">
                  <pre className="font-mono text-sm">
                    <code>
{`{
  "success": true,
  "message": "Workflows retrieved successfully",
  "data": [
    {
      "id": "wf_123456789",
      "name": "Customer Support Workflow",
      "description": "Handles customer support requests",
      "workflow_url": "https://api.ruh.ai/workflows/wf_123456789",
      "builder_url": "https://builder.ruh.ai/workflows/wf_123456789",
      "status": "active",
      "created_at": "2023-06-15T10:30:00Z",
      "updated_at": "2023-06-16T14:20:00Z"
    },
    // More workflows...
  ],
  "metadata": {
    "total": 10,
    "page": 1,
    "per_page": 20
  }
}`}
                    </code>
                  </pre>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Get Workflow Details</h3>
                <p className="text-sm text-muted-foreground">Get detailed information about a specific workflow.</p>
                
                <div className="rounded-md bg-muted p-4">
                  <p className="font-mono text-sm mb-2">GET /workflows/{'{workflow_id}'}</p>
                  <pre className="font-mono text-sm">
                    <code>
{`curl -X GET https://api.ruh.ai/v1/workflows/wf_123456789 \\
  -H "Authorization: Bearer YOUR_API_KEY"`}
                    </code>
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Agents API Tab */}
        <TabsContent value="agents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Agents API</CardTitle>
              <CardDescription>Endpoints for managing AI agents</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">List Agents</h3>
                <p className="text-sm text-muted-foreground">Get a list of all agents for the authenticated user.</p>
                
                <div className="rounded-md bg-muted p-4">
                  <p className="font-mono text-sm mb-2">GET /agents/user/agents</p>
                  <pre className="font-mono text-sm">
                    <code>
{`curl -X GET https://api.ruh.ai/v1/agents/user/agents \\
  -H "Authorization: Bearer YOUR_API_KEY"`}
                    </code>
                  </pre>
                </div>
                
                <h4 className="text-md font-medium mt-4">Response</h4>
                <div className="rounded-md bg-muted p-4">
                  <pre className="font-mono text-sm">
                    <code>
{`{
  "success": true,
  "message": "Agents retrieved successfully",
  "data": [
    {
      "id": "agent_123456789",
      "name": "Customer Support Agent",
      "description": "Handles customer inquiries",
      "model": "gpt-4",
      "status": "active",
      "created_at": "2023-07-10T08:15:00Z",
      "updated_at": "2023-07-12T11:45:00Z"
    },
    // More agents...
  ],
  "metadata": {
    "total": 5,
    "page": 1,
    "per_page": 20
  }
}`}
                    </code>
                  </pre>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Get Agent Details</h3>
                <p className="text-sm text-muted-foreground">Get detailed information about a specific agent.</p>
                
                <div className="rounded-md bg-muted p-4">
                  <p className="font-mono text-sm mb-2">GET /agents/{'{agent_id}'}</p>
                  <pre className="font-mono text-sm">
                    <code>
{`curl -X GET https://api.ruh.ai/v1/agents/agent_123456789 \\
  -H "Authorization: Bearer YOUR_API_KEY"`}
                    </code>
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* SSE Events Tab */}
        <TabsContent value="sse" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Server-Sent Events</CardTitle>
              <CardDescription>Real-time event streaming from the API</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                The Ruh.ai API supports Server-Sent Events (SSE) for real-time updates. 
                This allows you to receive updates about your workflows and agents as they happen.
              </p>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Connecting to the Events Stream</h3>
                <p className="text-sm text-muted-foreground">
                  To connect to the events stream, make a GET request to the events endpoint.
                </p>
                
                <div className="rounded-md bg-muted p-4">
                  <p className="font-mono text-sm mb-2">GET /events</p>
                  <pre className="font-mono text-sm">
                    <code>
{`// JavaScript example
const eventSource = new EventSource('https://api.ruh.ai/v1/events?api_key=YOUR_API_KEY');

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Received event:', data);
};

eventSource.onerror = (error) => {
  console.error('EventSource error:', error);
  eventSource.close();
};`}
                    </code>
                  </pre>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Event Types</h3>
                <p className="text-sm text-muted-foreground">
                  The API emits different types of events that you can listen for.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div className="rounded-md border p-4">
                    <h4 className="font-medium">workflow.started</h4>
                    <p className="text-sm text-muted-foreground">Emitted when a workflow execution starts</p>
                  </div>
                  <div className="rounded-md border p-4">
                    <h4 className="font-medium">workflow.completed</h4>
                    <p className="text-sm text-muted-foreground">Emitted when a workflow execution completes</p>
                  </div>
                  <div className="rounded-md border p-4">
                    <h4 className="font-medium">workflow.failed</h4>
                    <p className="text-sm text-muted-foreground">Emitted when a workflow execution fails</p>
                  </div>
                  <div className="rounded-md border p-4">
                    <h4 className="font-medium">agent.message</h4>
                    <p className="text-sm text-muted-foreground">Emitted when an agent sends a message</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
