import type { Metada<PERSON> } from "next";
import { DashboardWrapper } from "@/components/dashboard-wrapper";

export const metadata: Metadata = {
  title: "RUH AI Developer Platform",
  description: "Monitor and manage your AI platform",
};

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <DashboardWrapper>{children}</DashboardWrapper>;
}
