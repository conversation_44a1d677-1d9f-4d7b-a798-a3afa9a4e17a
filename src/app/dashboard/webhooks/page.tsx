"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { PlusIcon, PlayIcon, PencilIcon, TrashIcon } from "lucide-react"
import { Switch } from "@/components/ui/switch"

const webhookData = [
  {
    id: 1,
    url: "https://example.com/...",
    events: ["Workflow Completed", "API Request Error"],
    status: true,
    created: "2025-03-15"
  },
  {
    id: 2,
    url: "https://myapp.vercel.app/...",
    events: ["Workflow Completed", "Workflow Started"],
    status: false,
    created: "2025-04-02"
  },
  {
    id: 3,
    url: "https://webhook.site/...",
    events: ["API Request Success", "API Request Error"],
    status: true,
    created: "2025-04-10"
  }
]

export default function WebhooksPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Webhooks</h1>
          <p className="text-muted-foreground">
            Manage webhook endpoints for receiving event notifications.
          </p>
        </div>
        <Button className="gap-1">
          <PlusIcon className="h-4 w-4" />
          Add Webhook Endpoint
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[300px]">URL</TableHead>
              <TableHead className="w-[300px]">Events</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {webhookData.map((webhook) => (
              <TableRow key={webhook.id}>
                <TableCell className="font-medium">{webhook.url}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {webhook.events.map((event, index) => (
                      <span 
                        key={index} 
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          event === 'Workflow Completed' || event === 'Workflow Started'
                            ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                            : event === 'API Request Error'
                              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                              : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        }`}
                      >
                        {event}
                      </span>
                    ))}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Switch checked={webhook.status} />
                    <span className={webhook.status ? "text-green-500" : "text-muted-foreground"}>
                      {webhook.status ? "Active" : "Inactive"}
                    </span>
                  </div>
                </TableCell>
                <TableCell>{webhook.created}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <PlayIcon className="h-4 w-4" />
                      <span className="sr-only">Test</span>
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <PencilIcon className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                      <TrashIcon className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
