"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, FileText, Bell } from "lucide-react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { useActivities, useLogs, useEvents } from "@/hooks/use-activities";
import { Activity as ApiActivity, ActivityLog, ActivityEvent } from "@/shared/interfaces"; // Renamed to avoid conflict
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogDescription } from "@/components/ui/dialog";

// Define proper interfaces for log and event items (local page types)
/*
interface LogItem {
  id: string;
  timestamp: string;
  method: string;
  endpoint: string;
  statusCode: number;
  duration: string;
  type: "log";
  request: Record<string, unknown>;
  response: Record<string, unknown>;
}

interface EventItem {
  id: string;
  timestamp: string;
  type: "event";
  eventType: string;
  status: string;
  resourceId: string;
  payload: Record<string, unknown>;
}
*/
// Define a union type for activity items
type ActivityItem = ApiActivity | ActivityLog | ActivityEvent;

// Sample log data (now fetched from API)
// const logs: LogItem[] = [ ... ];
// Sample event data (now fetched from API)
// const events: EventItem[] = [ ... ];

// Combined activity data - will be replaced by API data
// const combinedActivity: ActivityItem[] = [...logs, ...events].sort(
//   (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
// );

const eventTypeLabels: Record<string, string> = {
  "workflow.completed": "Workflow Completed",
  "workflow.started": "Workflow Started",
  "api.request.success": "API Request Success",
  "api.request.error": "API Request Error",
};

// --- New filter functions to handle type narrowing --- 
const filterApiActivities = (items: ActivityItem[], typeFilter: string, statusFilter: string, searchQuery: string): ApiActivity[] => {
  return items.filter((item): item is ApiActivity => {
    if (!('type' in item) || !('status' in item) || !('resource_id' in item) || !('logs' in item) || !('events' in item)) return false; // Ensure it's an ApiActivity
    const apiItem = item; // item is now inferred as ApiActivity

    return (typeFilter === "all" || apiItem.type === typeFilter) &&
           (statusFilter === "all" || apiItem.status.toLowerCase() === statusFilter.toLowerCase()) &&
           (!searchQuery || apiItem.resource_id.toLowerCase().includes(searchQuery.toLowerCase()) || apiItem.id.toLowerCase().includes(searchQuery.toLowerCase()));
  });
};

const filterLogs = (items: ActivityItem[], statusFilter: string, searchQuery: string): ActivityLog[] => {
  return items.filter((item): item is ActivityLog => {
    if (!('log_type' in item) || !('log_status' in item) || !('activity_id' in item)) return false; // Ensure it's an ActivityLog
    const logItem = item; // item is now inferred as ActivityLog

    return (statusFilter === "all" ||
           (statusFilter === "SUCCESS" && logItem.log_status === "SUCCESS") ||
           (statusFilter === "FAILURE" && logItem.log_status === "FAILURE")) &&
           (!searchQuery || logItem.activity_id?.toLowerCase().includes(searchQuery.toLowerCase()) || logItem.id.toLowerCase().includes(searchQuery.toLowerCase()));
  });
};

const filterEvents = (items: ActivityItem[], typeFilter: string, statusFilter: string, searchQuery: string): ActivityEvent[] => {
  return items.filter((item): item is ActivityEvent => {
    if (!('event_name' in item) || !('activity_id' in item)) return false; // Ensure it's an ActivityEvent
    const eventItem = item; // item is now inferred as ActivityEvent

    return (typeFilter === "all" || eventItem.event_name === typeFilter) &&
           (statusFilter === "all" ||
            (statusFilter === "delivered" && (eventItem.event_name.includes("completed") || eventItem.event_name.includes("success"))) ||
            (statusFilter === "failed" && (eventItem.event_name.includes("error") || eventItem.event_name.includes("failed")))) &&
           (!searchQuery || eventItem.id.toLowerCase().includes(searchQuery.toLowerCase()) || eventItem.activity_id.toLowerCase().includes(searchQuery.toLowerCase()));
  });
};
// --- End new filter functions --- 

export default function ActivityPage() {
  const [activeTab, setActiveTab] = useState<"all" | "logs" | "events">("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10); // Or your default

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedItemDetails, setSelectedItemDetails] = useState<ActivityLog | ActivityEvent | null>(null);

  const router = useRouter();

  // Use different hooks based on active tab
  const {
    data: activitiesResponse,
    isLoading: isLoadingActivities,
    error: activitiesError,
    isFetching: isFetchingActivities,
  } = useActivities({
    page: currentPage,
    enabled: activeTab === "all",
  });

  const {
    data: logsResponse,
    isLoading: isLoadingLogs,
    error: logsError,
    isFetching: isFetchingLogs,
  } = useLogs({
    page: currentPage,
    enabled: activeTab === "logs",
  });

  const {
    data: eventsResponse,
    isLoading: isLoadingEvents,
    error: eventsError,
    isFetching: isFetchingEvents,
  } = useEvents({
    page: currentPage,
    enabled: activeTab === "events",
  });

  // Determine current loading/error state based on active tab
  const isLoading = activeTab === "all" ? isLoadingActivities :
                   activeTab === "logs" ? isLoadingLogs : isLoadingEvents;
  const isFetching = activeTab === "all" ? isFetchingActivities :
                    activeTab === "logs" ? isFetchingLogs : isFetchingEvents;
  const error = activeTab === "all" ? activitiesError :
               activeTab === "logs" ? logsError : eventsError;

  const [transformedActivity, setTransformedActivity] = useState<ActivityItem[]>([]);

  useEffect(() => {
    let transformed: ActivityItem[] = [];

    if (activeTab === "all" && activitiesResponse?.activities) {
      transformed = activitiesResponse.activities.map((apiItem: ApiActivity): ApiActivity => {
        return apiItem; // Use ApiActivity directly for the "all" tab
      });
    } else if (activeTab === "logs" && logsResponse?.data) {
      transformed = logsResponse.data.map((logItem: ActivityLog): ActivityLog => ({
        id: logItem.id,
        activity_id: logItem.activity_id,
        log_type: logItem.log_type,
        log_status: logItem.log_status,
        log_details: logItem.log_details,
        created_at: logItem.created_at,
      }));
    } else if (activeTab === "events" && eventsResponse?.data) {
      transformed = eventsResponse.data.map((eventItem: ActivityEvent): ActivityEvent => ({
        id: eventItem.id,
        activity_id: eventItem.activity_id,
        event_name: eventItem.event_name,
        event_details: eventItem.event_details,
        created_at: eventItem.created_at,
      }));
    }

    setTransformedActivity(transformed.sort((a, b) => {
      const timestampA = new Date(a.created_at).getTime();
      const timestampB = new Date(b.created_at).getTime();
      return timestampB - timestampA;
    }));
  }, [activeTab, activitiesResponse, logsResponse, eventsResponse]);

  // --- Updated filtering logic using the new filter functions ---
  const currentFilteredData = useMemo(() => {
    if (activeTab === "all") {
      return filterApiActivities(transformedActivity, typeFilter, statusFilter, searchQuery);
    } else if (activeTab === "logs") {
      return filterLogs(transformedActivity, statusFilter, searchQuery);
    }
    return filterEvents(transformedActivity, typeFilter, statusFilter, searchQuery);
  }, [activeTab, transformedActivity, typeFilter, statusFilter, searchQuery]);
  // --- End updated filtering logic ---

  const handleViewItem = (item: ActivityItem) => {
    // Navigate to the activity detail page or show modal
    if ('type' in item && 'status' in item && 'resource_id' in item) { // This is an ApiActivity
      router.push(`/dashboard/activity/${item.id}`);
    } else { // This is either an ActivityLog or ActivityEvent
      setSelectedItemDetails(item as ActivityLog | ActivityEvent);
      setIsModalOpen(true);
    }
  };

  // Determine which filters to show based on active tab
  const renderFilters = () => {
    if (activeTab === "logs") {
      return (
        <>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[160px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="SUCCESS">Success</SelectItem>
              <SelectItem value="FAILURE">Failure</SelectItem>
            </SelectContent>
          </Select>
        </>
      );
    }

    if (activeTab === "events") {
      return (
        <>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Event Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Events</SelectItem>
              <SelectItem value="workflow.completed">
                Workflow Completed
              </SelectItem>
              <SelectItem value="workflow.started">Workflow Started</SelectItem>
              <SelectItem value="api.request.success">
                API Request Success
              </SelectItem>
              <SelectItem value="api.request.error">
                API Request Error
              </SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[160px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </>
      );
    }

    // For "All" tab - show both filters
    return (
      <>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Event Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="WORKFLOW">
              Workflow
            </SelectItem>
            <SelectItem value="API_REQUEST">
              API Request
            </SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[160px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="started">Started</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="failed">Failed</SelectItem>
          </SelectContent>
        </Select>
      </>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Platform Activity
          </h1>
          <p className="text-muted-foreground">
            Monitor activity across your platform including API requests, logs,
            and events.
          </p>
        </div>
      </div>

      <Tabs
        defaultValue="all"
        className="w-full"
        onValueChange={(value) => {
          setActiveTab(value as "all" | "logs" | "events");
          setCurrentPage(1); // Reset to first page when switching tabs
        }}
      >
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
          <TabsList className="mb-4 md:mb-0">
            <TabsTrigger value="all" className="flex items-center gap-2">
              All Activity
            </TabsTrigger>
            <TabsTrigger value="logs" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Logs
            </TabsTrigger>
            <TabsTrigger value="events" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Events
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by ID or resource ID..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {renderFilters()}
          <Button
            variant="outline"
            onClick={() => toast.info("Filters applied")}
          >
            Filter
          </Button>
        </div>

        {/* Common table for all tabs */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Created</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Resource ID</TableHead>
                <TableHead>Activity ID</TableHead>
                {activeTab === "all" && <TableHead>Logs/Events</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading || isFetching ? (
                Array.from({ length: itemsPerPage }).map((_, index) => (
                  <TableRow key={`skeleton-${index}`}>
                    <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[250px]" /></TableCell>
                    {activeTab === "all" && <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>}
                  </TableRow>
                ))
              ) : error ? (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center py-8 text-red-500"
                  >
                    Error fetching activities: {error.message}
                  </TableCell>
                </TableRow>
              ) : currentFilteredData.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center py-8 text-muted-foreground"
                  >
                    No items found matching your filters or no activities yet.
                  </TableCell>
                </TableRow>
              ) : (
                currentFilteredData.map((item) => {
                  if (activeTab === "all") {
                    const activity = item as ApiActivity;
                    return (
                      <TableRow
                        key={activity.id}
                        className="cursor-pointer"
                        onClick={() => handleViewItem(activity)}
                      >
                        <TableCell className="text-sm">
                          {new Date(activity.created_at).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="bg-accent font-medium">
                            {activity.type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              activity.status?.toLowerCase() === "completed" || activity.status?.toLowerCase() === "started"
                                ? "secondary"
                                : activity.status?.toLowerCase() === "failed"
                                ? "destructive"
                                : "outline"
                            }
                            className="capitalize"
                          >
                            {activity.status || "N/A"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm font-mono">
                          {activity.resource_id}
                        </TableCell>
                        <TableCell className="text-sm font-mono">
                          {activity.id}
                        </TableCell>
                        {activeTab === "all" && (
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {activity.logs && activity.logs.length > 0 && (
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <FileText className="h-3 w-3" />
                                  <span>{activity.logs.length} logs</span>
                                </div>
                              )}
                              {activity.events && activity.events.length > 0 && (
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <Bell className="h-3 w-3" />
                                  <span>{activity.events.length} events</span>
                                </div>
                              )}
                            </div>
                          </TableCell>
                        )}
                      </TableRow>
                    );
                  } else if (activeTab === "logs") {
                    const log = item as ActivityLog;
                    return (
                      <TableRow
                        key={log.id}
                        className="cursor-pointer"
                        onClick={() => handleViewItem(log)}
                      >
                        <TableCell className="text-sm">
                          {new Date(log.created_at).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                            {log.log_type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              log.log_status === "SUCCESS" ? "secondary" : "destructive"
                            }
                          >
                            {log.log_status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm font-mono">
                          {log.activity_id}
                        </TableCell>
                        <TableCell className="text-sm font-mono">
                          {log.id}
                        </TableCell>
                      </TableRow>
                    );
                  } else if (activeTab === "events") {
                    const event = item as ActivityEvent;
                    return (
                      <TableRow
                        key={event.id}
                        className="cursor-pointer"
                        onClick={() => handleViewItem(event)}
                      >
                        <TableCell className="text-sm">
                          {new Date(event.created_at).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="bg-accent font-medium">
                            {eventTypeLabels[event.event_name] || event.event_name}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              event.event_name.includes("completed") || event.event_name.includes("success")
                                ? "secondary"
                                : event.event_name.includes("error")
                                ? "destructive"
                                : "outline"
                            }
                            className="capitalize"
                          >
                            {event.event_name.includes("completed") || event.event_name.includes("success") ? "Delivered" : "Failed"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm font-mono">
                          {event.activity_id}
                        </TableCell>
                        <TableCell className="text-sm font-mono">
                          {event.id}
                        </TableCell>
                      </TableRow>
                    );
                  }
                  return null; // Fallback for unexpected types
                })
              )}
            </TableBody>
          </Table>
        </div>
      </Tabs>

      {selectedItemDetails && (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>Details for {selectedItemDetails.id}</DialogTitle>
              <DialogDescription>
                {(() => {
                  if ('log_type' in selectedItemDetails) {
                    const log = selectedItemDetails as ActivityLog;
                    return `Log Type: ${log.log_type}, Status: ${log.log_status}`;
                  } else if ('event_name' in selectedItemDetails) {
                    const event = selectedItemDetails as ActivityEvent;
                    return `Event Name: ${eventTypeLabels[event.event_name] || event.event_name}`;
                  }
                  return "";
                })()}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4 max-h-[70vh] overflow-y-auto">
              {(() => {
                if ('log_type' in selectedItemDetails) {
                  const log = selectedItemDetails as ActivityLog;
                  return (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Created At: {new Date(log.created_at).toLocaleString()}</p>
                      <p className="text-sm font-medium">Activity ID: <span className="font-mono">{log.activity_id}</span></p>
                      {log.log_details && Object.keys(log.log_details).length > 0 && (
                        <div>
                          <p className="text-muted-foreground text-sm mb-2">Log Details</p>
                          <pre className="font-mono text-xs overflow-auto bg-gray-100 p-2 rounded">
                            {JSON.stringify(log.log_details, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  );
                } else if ('event_name' in selectedItemDetails) {
                  const event = selectedItemDetails as ActivityEvent;
                  return (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Created At: {new Date(event.created_at).toLocaleString()}</p>
                      <p className="text-sm font-medium">Activity ID: <span className="font-mono">{event.activity_id}</span></p>
                      {event.event_details && Object.keys(event.event_details).length > 0 && (
                        <div>
                          <p className="text-muted-foreground text-sm mb-2">Event Details</p>
                          <pre className="font-mono text-xs overflow-auto bg-gray-100 p-2 rounded">
                            {JSON.stringify(event.event_details, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  );
                }
                return null;
              })()}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
