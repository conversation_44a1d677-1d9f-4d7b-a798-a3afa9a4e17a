"use client";

import { useParams } from "next/navigation";
import {
  <PERSON>,
  <PERSON>Content,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { useActivityById } from "@/hooks/use-activities";
import { Activity as ApiActivity, ActivityLog, ActivityEvent } from "@/shared/interfaces";
import { Button } from "@/components/ui/button";

export default function ActivityDetailPage() {
  const params = useParams();
  const activityId = params.id as string;

  const {
    data: activity,
    isLoading,
    error,
    isFetching,
  } = useActivityById(activityId);

  const eventTypeLabels: Record<string, string> = {
    "workflow.completed": "Workflow Completed",
    "workflow.started": "Workflow Started",
    "api.request.success": "API Request Success",
    "api.request.error": "API Request Error",
  };

  if (isLoading || isFetching) {
    return (
      <div className="space-y-6 p-6">
        <Skeleton className="h-10 w-3/4" />
        <Skeleton className="h-6 w-1/2" />
        <div className="grid grid-cols-2 gap-4">
          <Skeleton className="h-[100px]" />
          <Skeleton className="h-[100px]" />
        </div>
        <Skeleton className="h-[300px]" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center text-red-500">
        Error loading activity details: {error.message}
      </div>
    );
  }

  if (!activity) {
    return (
      <div className="p-6 text-center text-muted-foreground">
        Activity not found.
      </div>
    );
  }

  const logs = activity.logs || [];
  const events = activity.events || [];

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col items-start gap-2">
        <Button variant="outline" onClick={() => window.history.back()}>
          ← Back to Activity
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Activity Details
          </h1>
          <p className="text-muted-foreground">
            Activity ID: <span className="font-mono">{activity.id}</span>
          </p>
        </div>

      </div>

      <Card>
        <CardHeader>
          <CardTitle>Activity Overview</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-y-4 gap-x-8 text-sm">
          <div>
            <p className="text-muted-foreground">Type</p>
            <p className="font-medium">
              <Badge variant="outline" className="bg-accent font-medium">
                {activity.type}
              </Badge>
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">Status</p>
            <p className="font-medium">
              <Badge
                variant={
                  activity.status?.toLowerCase() === "completed" || activity.status?.toLowerCase() === "started"
                    ? "secondary"
                    : activity.status?.toLowerCase() === "failed"
                      ? "destructive"
                      : "outline"
                }
                className="capitalize"
              >
                {activity.status || "N/A"}
              </Badge>
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">Resource ID</p>
            <p className="font-medium font-mono">{activity.resource_id}</p>
          </div>
          {activity.user_id && (
            <div>
              <p className="text-muted-foreground">User ID</p>
              <p className="font-medium font-mono">{activity.user_id}</p>
            </div>
          )}
          <div>
            <p className="text-muted-foreground">Created</p>
            <p className="font-medium">
              {new Date(activity.created_at).toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">Updated</p>
            <p className="font-medium">
              {new Date(activity.updated_at).toLocaleString()}
            </p>
          </div>
        </CardContent>
      </Card>

      {activity.user_metadata && Object.keys(activity.user_metadata).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Metadata</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="font-mono text-xs overflow-auto bg-gray-100 p-4 rounded">
              {JSON.stringify(activity.user_metadata, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="logs" className="w-full">
        <TabsList>
          <TabsTrigger value="logs">Logs ({logs.length})</TabsTrigger>
          <TabsTrigger value="events">Events ({events.length})</TabsTrigger>
        </TabsList>
        <TabsContent value="logs" className="space-y-4">
          {logs.length > 0 ? (
            logs.map((log) => (
              <Card key={log.id}>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                      {log.log_type}
                    </Badge>
                    <Badge
                      variant={
                        log.log_status === "SUCCESS" ? "secondary" : "destructive"
                      }
                    >
                      {log.log_status}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {new Date(log.created_at).toLocaleString()}
                  </p>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm font-mono text-muted-foreground">
                    ID: <span className="font-semibold text-foreground">{log.id}</span>
                  </p>
                  {log.log_details && Object.keys(log.log_details).length > 0 && (
                    <div>
                      <p className="text-muted-foreground text-sm mb-2">Log Details</p>
                      <pre className="font-mono text-xs overflow-auto bg-gray-100 p-2 rounded">
                        {JSON.stringify(log.log_details, null, 2)}
                      </pre>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No logs available for this activity.
            </div>
          )}
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          {events.length > 0 ? (
            events.map((event) => (
              <Card key={event.id}>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-accent font-medium">
                      {eventTypeLabels[event.event_name] || event.event_name}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {new Date(event.created_at).toLocaleString()}
                  </p>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-sm font-mono text-muted-foreground">
                    ID: <span className="font-semibold text-foreground">{event.id}</span>
                  </p>
                  {event.event_details && Object.keys(event.event_details).length > 0 && (
                    <div>
                      <p className="text-muted-foreground text-sm mb-2">Event Details</p>
                      <pre className="font-mono text-xs overflow-auto bg-gray-100 p-2 rounded">
                        {JSON.stringify(event.event_details, null, 2)}
                      </pre>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No events available for this activity.
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 