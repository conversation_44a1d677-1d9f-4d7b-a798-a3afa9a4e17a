"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlusIcon, SettingsIcon, TrashIcon, BarChartIcon } from "lucide-react"
import { useState } from 'react'
import { CreateAppDialog } from "@/components/apps/create-app-dialog"
import { useRouter } from 'next/navigation'
import { useUserApplications, useCreateApplication, useDeleteApplication } from "@/hooks/use-applications";
import { useUserDetails } from "@/hooks/use-auth";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function AppsPage() {
  const [isCreateDialogO<PERSON>, setIsCreateDialogOpen] = useState(false)
  const router = useRouter()

  // Fetch user details for user_id
  const { data: userDetails } = useUserDetails();
  // Fetch applications
  const { data, isLoading, isError, error } = useUserApplications();
  // Create application mutation
  const createApplication = useCreateApplication();
  // Delete application mutation
  const deleteApplication = useDeleteApplication();

  // State for delete confirmation dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [appToDeleteId, setAppToDeleteId] = useState<string | null>(null);

  const handleCreateApp = (name: string, description: string) => {
    if (!userDetails?.id) {
      toast.error("User not loaded. Please try again.");
      return;
    }
    createApplication.mutate(
      {
        user_id: String(userDetails.id),
        name,
        description,
      },
      {
        onSuccess: () => {
          toast.success("App created successfully!");
          setIsCreateDialogOpen(false);
        },
        onError: (err: any) => {
          toast.error(err?.response?.data?.message || "Failed to create app");
        },
      }
    );
  };

  // Open delete confirmation dialog
  const confirmDeleteApp = (id: string) => {
    setAppToDeleteId(id);
    setShowDeleteDialog(true);
  };

  // Handle delete app
  const handleDeleteApp = () => {
    if (!appToDeleteId) return;

    deleteApplication.mutate(appToDeleteId, {
      onSuccess: () => {
        toast.success("App deleted successfully!");
        setShowDeleteDialog(false);
        setAppToDeleteId(null);
        // The applications list will automatically refetch due to query invalidation in the hook
      },
      onError: (err: any) => {
        toast.error(err?.response?.data?.message || "Failed to delete app");
        setShowDeleteDialog(false);
        setAppToDeleteId(null);
      },
    });
  };

  // Safe access to applications data
  const applications = data?.data || [];

  // Determine if there are no apps after loading and no filtering
  const showEmptyState = !isLoading && !isError && applications.length === 0;

  // Determine if there is an error and no data
  const showErrorMessage = isError && !isLoading;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Apps</h1>
          <p className="text-muted-foreground">
            Organize your resources and workflows into logical apps.
          </p>
        </div>
        <Button className="gap-1" onClick={() => setIsCreateDialogOpen(true)}>
          <PlusIcon className="h-4 w-4" />
          Create New App
        </Button>
      </div>

      <CreateAppDialog
        isOpen={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onCreateApp={handleCreateApp}
      />

      {/* Loading state with skeleton */}
      {isLoading && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="rounded-lg border bg-card shadow-sm p-6">
              <Skeleton className="h-6 w-2/3 mb-4" />
              <Skeleton className="h-4 w-1/3 mb-2" />
              <Skeleton className="h-4 w-full mb-4" />
              <Skeleton className="h-4 w-3/4 mb-6" />
              <div className="space-y-2">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
              </div>
              <div className="mt-6 flex justify-between">
                <Skeleton className="h-9 w-24" />
                <Skeleton className="h-9 w-20" />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error state */}
      {showErrorMessage && (
         <Card className="border-destructive ring-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Apps</CardTitle>
            <CardDescription className="text-destructive/80">
              {error?.message || "An unexpected error occurred while fetching applications."}
            </CardDescription>
          </CardHeader>
           {/* Optionally add a retry button */}
            {/* <CardFooter>
              <Button variant="outline" onClick={() => refetch()}>Retry</Button>
            </CardFooter> */}
        </Card>
      )}

      {/* Empty state */}
      {showEmptyState && (
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle>No Apps Found</CardTitle>
            <CardDescription>
              You haven&apos;t created any applications yet. Create your first application
              to get started.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Your First App
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Apps grid */}
      {!isLoading && !showErrorMessage && applications.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {applications.map((app) => (
            <div key={app.id} className="rounded-lg border bg-card shadow-sm">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">{app.name}</h3>
                  <span className="text-xs text-muted-foreground">{app.created_at?.split("T")[0]}</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  {app.workflow_ids.length} associated workflows
                </p>
                <p className="text-sm text-muted-foreground">
                  {app.agent_ids.length} associated agents
                </p>
                <div className="mt-6 flex items-center justify-between">
                  <Button variant="outline" size="sm" className="gap-1" onClick={() => router.push(`/dashboard/apps/${app.id}`)}>
                    <SettingsIcon className="h-4 w-4" />
                    Settings
                  </Button>
                  <Button variant="ghost" size="sm" className="gap-1 text-destructive" onClick={() => confirmDeleteApp(app.id)} disabled={deleteApplication.isPending}>
                    <TrashIcon className="h-4 w-4" />
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete confirmation dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Application</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this application? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteApp}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              disabled={deleteApplication.isPending}
            >
              {deleteApplication.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
