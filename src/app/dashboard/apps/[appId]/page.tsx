"use client";

import { useApplication, useUpdateApplication } from "@/hooks/use-applications";
import { useRouter, useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CopyIcon, EyeIcon, EyeOffIcon, TrashIcon, SettingsIcon, BarChartIcon, PlusIcon, Server, Workflow, Search, RefreshCw, ExternalLink, X } from 'lucide-react';
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from 'sonner';
import { useUserAgents } from "@/hooks/use-agents";
import { useUserWorkflows } from "@/hooks/use-workflows";
import { useDebounce } from "@/hooks/use-debounce";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription, SheetFooter } from "@/components/ui/sheet";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge as UIBadge } from "@/components/ui/badge";
import { getWorkflowBuilderUrl } from '@/lib/helpers';
import { format } from "date-fns";

export default function AppDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const appId = params.appId as string;
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch application details
  const { data, isLoading, isError, error, refetch } = useApplication(appId);
  const app = data?.application;
  const metrics = data?.metrics;

  // Mutation for updating application
  const updateApplicationMutation = useUpdateApplication();

  // Sheet states
  const [showAgentSheet, setShowAgentSheet] = useState(false);
  const [showWorkflowSheet, setShowWorkflowSheet] = useState(false);

  // Local selection state for sheets
  const [selectedAgentIds, setSelectedAgentIds] = useState<string[]>([]);
  const [selectedWorkflowIds, setSelectedWorkflowIds] = useState<string[]>([]);

  // Search states for sheets
  const [agentSearchQuery, setAgentSearchQuery] = useState("");
  const [workflowSearchQuery, setWorkflowSearchQuery] = useState("");

  // Debounced search queries
  const debouncedAgentSearchQuery = useDebounce(agentSearchQuery, 500);
  const debouncedWorkflowSearchQuery = useDebounce(workflowSearchQuery, 500);

  // Fetch available agents and workflows
  const { data: agentsData, isLoading: isLoadingAgents, isError: isAgentsError, error: agentsError, refetch: refetchAgents } = useUserAgents({ search: debouncedAgentSearchQuery || undefined, page_size: 100 });
  const { data: workflowsData, isLoading: isLoadingWorkflows, isError: isWorkflowsError, error: workflowsError, refetch: refetchWorkflows } = useUserWorkflows({ search: debouncedWorkflowSearchQuery || undefined, page_size: 100 });

  // Show error toast when API call fails - only once for app details
  useEffect(() => {
    if (isError && error) {
      console.error('App details API error:', error);
      const errorMessage = error.response?.data?.message || 'Failed to fetch app details';
      const statusCode = error.response?.status;
      toast.error(`Error ${statusCode}: ${errorMessage}`);
    }
  }, [isError, error]);

  // When opening sheets, initialize local selection from app data
  useEffect(() => {
    if (showAgentSheet && app) {
      setSelectedAgentIds(app.agent_ids);
    }
  }, [showAgentSheet, app]);
  useEffect(() => {
    if (showWorkflowSheet && app) {
      setSelectedWorkflowIds(app.workflow_ids);
    }
  }, [showWorkflowSheet, app]);

  // Handle agent toggle in sheet (local state)
  const handleAgentToggle = (agentId: string) => {
    setSelectedAgentIds((prev) =>
      prev.includes(agentId)
        ? prev.filter((id) => id !== agentId)
        : [...prev, agentId]
    );
  };

  // Handle workflow toggle in sheet (local state)
  const handleWorkflowToggle = (workflowId: string) => {
    setSelectedWorkflowIds((prev) =>
      prev.includes(workflowId)
        ? prev.filter((id) => id !== workflowId)
        : [...prev, workflowId]
    );
  };

  // Handle update application after selecting agents/workflows
  const handleUpdateAgents = () => {
    if (!app) return;
    updateApplicationMutation.mutate(
      {
        id: appId,
        data: {
          agent_ids: selectedAgentIds,
          workflow_ids: app.workflow_ids, // keep workflows unchanged
          name: app.name,
          description: app.description,
          status: app.status,
        },
      },
      {
        onSuccess: () => {
          toast.success("Agents updated successfully!");
          setShowAgentSheet(false);
        },
        onError: (err: any) => {
          toast.error(err?.response?.data?.message || "Failed to update agents");
        },
      }
    );
  };

  const handleUpdateWorkflows = () => {
    if (!app) return;
    updateApplicationMutation.mutate(
      {
        id: appId,
        data: {
          agent_ids: app.agent_ids, // keep agents unchanged
          workflow_ids: selectedWorkflowIds,
          name: app.name,
          description: app.description,
          status: app.status,
        },
      },
      {
        onSuccess: () => {
          toast.success("Workflows updated successfully!");
          setShowWorkflowSheet(false);
        },
        onError: (err: any) => {
          toast.error(err?.response?.data?.message || "Failed to update workflows");
        },
      }
    );
  };

  const handleOpenWorkflowBuilder = () => {
    const workflowUrl = getWorkflowBuilderUrl();
    window.open(workflowUrl, "_blank");
    toast.info("Opening workflow builder in a new tab");
  };

  const agents = agentsData?.data || [];
  const workflows = workflowsData?.data || [];

  return (
    <div className="space-y-6">
      <Button variant="outline" size="sm" onClick={() => router.back()} className="mb-4 w-fit">
        ← Back to Apps
      </Button>
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{isLoading ? 'Loading App...' : app ? `${app.name} Settings` : 'App Settings'}</h1>
        <p className="text-muted-foreground">
          Configure and manage your RUH AI application
        </p>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="space-y-4">
          <Skeleton className="h-10 w-40" /> {/* Tabs skeleton */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-1/3 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Skeleton className="h-4 w-16 mb-1" />
                  <Skeleton className="h-5 w-full" />
                </div>
                <div>
                  <Skeleton className="h-4 w-16 mb-1" />
                  <Skeleton className="h-5 w-full" />
                </div>
              </div>
              <div>
                <Skeleton className="h-4 w-20 mb-1" />
                <Skeleton className="h-5 w-full" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Skeleton className="h-4 w-16 mb-1" />
                  <Skeleton className="h-5 w-full" />
                </div>
                <div>
                  <Skeleton className="h-4 w-16 mb-1" />
                  <Skeleton className="h-5 w-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error state */}
      {isError && !isLoading && (
         <Card className="border-destructive ring-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading App Details</CardTitle>
            <CardDescription className="text-destructive/80">
              {error?.message || "An unexpected error occurred while fetching application details."}
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* App details content */}
      {!isLoading && !isError && app && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="agents">Agents</TabsTrigger>
            <TabsTrigger value="workflows">Workflows</TabsTrigger>
            <TabsTrigger value="api-keys">API Keys</TabsTrigger>
            <TabsTrigger value="usage">Usage & Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <Card>
              <CardHeader>
                <CardTitle>App Information</CardTitle>
                <CardDescription>Basic information about your RUH AI application</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium">App Name</div>
                    <div>{app.name}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">App ID</div>
                    <div>{app.id}</div>
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium">Description</div>
                  <div>{app.description}</div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium">Created</div>
                    <div>{app.created_at?.split("T")[0]}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">Status</div>
                    <div>{app.status}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="agents">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Connected Agents</CardTitle>
                <Button size="sm" className="gap-1" onClick={() => setShowAgentSheet(true)}>
                  <PlusIcon className="h-4 w-4" /> Connect Agent
                </Button>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Agent</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Model</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Sync</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {app.agent_ids.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-muted-foreground text-center">No agents connected</TableCell>
                        </TableRow>
                      ) : (
                        app.agent_ids.map((agentId) => {
                          const agent = agentsData?.data.find(a => a.id === agentId);
                          const initials = agent?.name
                            ? agent.name.split(" ").map(w => w[0]).join("").slice(0, 2).toUpperCase()
                            : "?";
                          const status = agent?.status?.toLowerCase() === "active" ? "Online" : "Offline";
                          const statusColor = status === "Online" ? "bg-green-500" : "bg-gray-400";
                          return (
                            <TableRow key={agentId}>
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center font-bold text-base text-foreground/80">
                                    {initials}
                                  </div>
                                  <div>
                                    <div className="font-semibold text-base leading-tight">{agent?.name || agentId}</div>
                                    <div className="text-xs text-muted-foreground">{agentId}</div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {agent?.agent_category && (
                                  <UIBadge variant="outline" className="font-medium text-xs px-2 py-0.5">
                                    {agent.agent_category.charAt(0).toUpperCase() + agent.agent_category.slice(1)}
                                  </UIBadge>
                                )}
                              </TableCell>
                              <TableCell className="font-mono text-sm">{agent?.model_name || "N/A"}</TableCell>
                              <TableCell>
                                <span className="flex items-center gap-1 text-sm font-medium">
                                  <span className={`h-2 w-2 rounded-full inline-block ${statusColor}`}></span>
                                  {status}
                                </span>
                              </TableCell>
                              <TableCell className="text-sm">
                                {agent?.updated_at ? format(new Date(agent.updated_at), "dd/MM/yyyy, HH:mm") : "N/A"}
                              </TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Agent Selection Sheet */}
                <Sheet open={showAgentSheet} onOpenChange={setShowAgentSheet}>
                  <SheetContent className="w-full sm:max-w-md">
                     <SheetHeader className="pb-4 flex flex-row items-center justify-between">
                        <div>
                          <SheetTitle>Select Agents</SheetTitle>
                          <SheetDescription>Select agents to connect with this application</SheetDescription>
                        </div>
                        <Button
                          variant="outline"
                          size="icon"
                          className="ml-2"
                          onClick={() => router.push('/dashboard/agents/create')}
                          title="Create Agent"
                        >
                          <PlusIcon className="h-5 w-5" />
                        </Button>
                     </SheetHeader>
                     <div className="py-4 px-1 space-y-6">
                        <div className="flex items-center gap-2">
                           <div className="relative flex-1">
                              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                              <Input
                                 type="search"
                                 placeholder="Search agents..."
                                 className="pl-8"
                                 value={agentSearchQuery}
                                 onChange={(e) => setAgentSearchQuery(e.target.value)}
                              />
                           </div>
                           <Button variant="outline" size="icon" onClick={() => refetchAgents()} title="Refresh agents">
                              <RefreshCw className="h-4 w-4" />
                           </Button>
                        </div>
                        <div className="flex items-center justify-between">
                           <h3 className="text-sm font-medium">Available Agents</h3>
                        </div>
                        {isLoadingAgents && (
                           <div className="space-y-4">
                             {[1, 2, 3].map((i) => (
                               <Card key={i} className="overflow-hidden">
                                 <CardContent className="p-4">
                                   <div className="flex items-start gap-2">
                                     <Skeleton className="h-4 w-4 mt-1" />
                                     <div className="space-y-2 flex-1">
                                       <Skeleton className="h-5 w-3/4" />
                                       <Skeleton className="h-4 w-full" />
                                     </div>
                                   </div>
                                 </CardContent>
                               </Card>
                             ))}
                           </div>
                        )}
                        {isAgentsError && !isLoadingAgents && (
                           <div className="text-center p-6 border rounded-lg">
                              <p className="text-muted-foreground">Failed to load agents. Please try again.</p>
                              <Button variant="outline" size="sm" className="mt-4" onClick={() => refetchAgents()}>Retry</Button>
                           </div>
                        )}
                        {!isLoadingAgents && !isAgentsError && agents.length > 0 && (
                            <div className="space-y-4 max-h-[50vh] overflow-y-auto pr-1">
                               {agents.map((agent) => (
                                  <Card key={agent.id} className="overflow-hidden">
                                     <CardContent className="p-4">
                                        <div className="flex items-start gap-2">
                                           <Checkbox
                                              id={`agent-${agent.id}`}
                                              checked={selectedAgentIds.includes(agent.id)}
                                              onCheckedChange={() => handleAgentToggle(agent.id)}
                                              className="mt-1 flex-shrink-0"
                                           />
                                           <div className="min-w-0 flex-1">
                                              <Label htmlFor={`agent-${agent.id}`} className="font-medium cursor-pointer line-clamp-1">{agent.name}</Label>
                                              {agent.description && <p className="text-sm text-muted-foreground mt-1 line-clamp-2">{agent.description}</p>}
                                           </div>
                                        </div>
                                     </CardContent>
                                  </Card>
                               ))}
                            </div>
                        )}
                         {!isLoadingAgents && !isAgentsError && agents.length === 0 && (
                            <div className="text-center p-6 border rounded-lg">
                                <p className="text-muted-foreground">No agents available.</p>
                            </div>
                        )}
                     </div>
                     <SheetFooter className="pt-2">
                        <Button onClick={handleUpdateAgents} disabled={updateApplicationMutation.isPending}>Done</Button>
                     </SheetFooter>
                  </SheetContent>
                </Sheet>

              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="workflows">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Workflows</CardTitle>
                <Button size="sm" className="gap-1" onClick={() => setShowWorkflowSheet(true)}>
                  <PlusIcon className="h-4 w-4" /> Connect Workflow
                </Button>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Workflow</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Run</TableHead>
                        <TableHead>Total Runs</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {app.workflow_ids.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-muted-foreground text-center">No workflows connected</TableCell>
                        </TableRow>
                      ) : (
                        app.workflow_ids.map((workflowId) => {
                          const workflow = workflowsData?.data.find(w => w.id === workflowId);
                          return (
                            <TableRow key={workflowId}>
                              <TableCell>
                                <div className="font-semibold text-base leading-tight">{workflow?.name || workflowId}</div>
                              </TableCell>
                              <TableCell className="text-sm">{workflow?.description || "N/A"}</TableCell>
                              <TableCell>
                                <UIBadge variant="outline" className="font-medium text-xs px-2 py-0.5">
                                  {workflow?.status || "N/A"}
                                </UIBadge>
                              </TableCell>
                              <TableCell className="text-sm">{workflow?.updated_at ? format(new Date(workflow.updated_at), "dd/MM/yyyy, HH:mm") : "N/A"}</TableCell>
                              <TableCell className="text-sm">N/A</TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>

                 {/* Workflow Selection Sheet */}
                <Sheet open={showWorkflowSheet} onOpenChange={setShowWorkflowSheet}>
                  <SheetContent className="w-full sm:max-w-md">
                     <SheetHeader className="pb-4 flex flex-row items-center justify-between">
                        <div>
                          <SheetTitle>Select Workflows</SheetTitle>
                          <SheetDescription>Select workflows to connect with this application</SheetDescription>
                        </div>
                        <Button
                          variant="outline"
                          size="icon"
                          className="ml-2"
                          onClick={handleOpenWorkflowBuilder}
                          title="Create Workflow"
                        >
                          <PlusIcon className="h-5 w-5" />
                        </Button>
                     </SheetHeader>
                     <div className="py-4 px-1 space-y-6">
                         <div className="flex items-center gap-2">
                           <div className="relative flex-1">
                              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                              <Input
                                 type="search"
                                 placeholder="Search workflows..."
                                 className="pl-8"
                                 value={workflowSearchQuery}
                                 onChange={(e) => setWorkflowSearchQuery(e.target.value)}
                              />
                           </div>
                           <Button variant="outline" size="icon" onClick={() => refetchWorkflows()} title="Refresh workflows">
                              <RefreshCw className="h-4 w-4" />
                           </Button>
                        </div>
                        <div className="flex items-center justify-between">
                           <h3 className="text-sm font-medium">Available Workflows</h3>
                        </div>
                        {isLoadingWorkflows && (
                           <div className="space-y-4">
                             {[1, 2, 3].map((i) => (
                               <Card key={i} className="overflow-hidden">
                                 <CardContent className="p-4">
                                   <div className="flex items-start gap-2">
                                     <Skeleton className="h-4 w-4 mt-1" />
                                     <div className="space-y-2 flex-1">
                                       <Skeleton className="h-5 w-3/4" />
                                       <Skeleton className="h-4 w-full" />
                                     </div>
                                   </div>
                                 </CardContent>
                               </Card>
                             ))}
                           </div>
                        )}
                        {isWorkflowsError && !isLoadingWorkflows && (
                           <div className="text-center p-6 border rounded-lg">
                              <p className="text-muted-foreground">Failed to load workflows. Please try again.</p>
                              <Button variant="outline" size="sm" className="mt-4" onClick={() => refetchWorkflows()}>Retry</Button>
                           </div>
                        )}
                        {!isLoadingWorkflows && !isWorkflowsError && workflows.length > 0 && (
                           <div className="space-y-4 max-h-[50vh] overflow-y-auto pr-1">
                               {workflows.map((workflow) => (
                                  <Card key={workflow.id} className="overflow-hidden">
                                     <CardContent className="p-4">
                                        <div className="flex items-start gap-2">
                                           <Checkbox
                                              id={`workflow-${workflow.id}`}
                                              checked={selectedWorkflowIds.includes(workflow.id)}
                                              onCheckedChange={() => handleWorkflowToggle(workflow.id)}
                                              className="mt-1 flex-shrink-0"
                                           />
                                           <div className="min-w-0 flex-1">
                                              <Label htmlFor={`workflow-${workflow.id}`} className="font-medium cursor-pointer line-clamp-1">{workflow.name}</Label>
                                              {workflow.description && <p className="text-sm text-muted-foreground mt-1 line-clamp-2">{workflow.description}</p>}
                                           </div>
                                        </div>
                                     </CardContent>
                                  </Card>
                               ))}
                            </div>
                        )}
                        {!isLoadingWorkflows && !isWorkflowsError && workflows.length === 0 && (
                           <div className="text-center p-6 border rounded-lg">
                                <p className="text-muted-foreground">No workflows available.</p>
                            </div>
                        )}
                     </div>
                     <SheetFooter className="pt-2">
                        <Button onClick={handleUpdateWorkflows} disabled={updateApplicationMutation.isPending}>Done</Button>
                     </SheetFooter>
                  </SheetContent>
                </Sheet>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="api-keys">
            <Card>
              <CardHeader>
                <CardTitle>API Keys</CardTitle>
                <CardDescription>Manage API keys for authenticating with the RUH AI API.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2 items-end">
                  <div className="grid gap-2 flex-grow">
                    <Label htmlFor="new-api-key">Create a new API key</Label>
                    <Input id="new-api-key" placeholder="Key name (e.g., Production, Development)" />
                  </div>
                  <Button size="sm">Create API Key</Button>
                </div>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>API Key ID</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {app.api_keys.length === 0 ? (
                      <TableRow>
                        <TableCell className="text-muted-foreground">No API keys</TableCell>
                      </TableRow>
                    ) : (
                      app.api_keys.map((apiKeyId) => (
                        <TableRow key={apiKeyId}>
                          <TableCell className="font-medium">{apiKeyId}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="usage">
            <Card>
              <CardHeader>
                <CardTitle>Usage & Analytics</CardTitle>
                <CardDescription>View usage data and analytics for your application.</CardDescription>
              </CardHeader>
              <CardContent>
                {metrics ? (
                  <div>
                    <div>Total Requests: {metrics.total_requests}</div>
                    <div>Successful Requests: {metrics.successful_requests}</div>
                    <div>Failed Requests: {metrics.failed_requests}</div>
                    <div>Credits Used: {metrics.credits_used}</div>
                    <div>Last Request At: {metrics.last_request_at}</div>
                    {/* Usage trend can be rendered as a chart if needed */}
                  </div>
                ) : (
                  <p>Usage and analytics data will be displayed here.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
} 