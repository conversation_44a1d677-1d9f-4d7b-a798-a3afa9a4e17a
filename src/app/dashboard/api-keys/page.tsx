"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  CopyIcon,
} from "lucide-react"
import { toast } from "sonner"
import { useApi<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>pi<PERSON><PERSON>, useDeleteApiKey } from "@/hooks/use-api-keys"
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { format } from "date-fns"
import { CreateApiKeyDialog, CreateApiKeyButton } from "@/components/api-keys/create-api-key-dialog"
import { ApiKeyDetailsDialog } from "@/components/api-keys/api-key-details-dialog"
import { DeleteApiKeyDialog, DeleteApiKeyButton } from "@/components/api-keys/delete-api-key-dialog"
import { useUserApplications } from "@/hooks/use-applications"

export default function ApiKeysPage() {
  // State for the create API key dialog
  const [createDialogOpen, setCreateDialogOpen] = useState(false)

  // State for the new key alert dialog
  const [keyDetailsDialogOpen, setKeyDetailsDialogOpen] = useState(false)
  const [newKeyDetails, setNewKeyDetails] = useState<{ public_key: string, private_key: string } | null>(null)

  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [keyToDelete, setKeyToDelete] = useState<string | null>(null)

  // Handle key details dialog close
  const handleKeyDetailsDialogClose = (open: boolean) => {
    if (!open) {
      // Use setTimeout to ensure the dialog is fully closed before resetting state
      setTimeout(() => {
        setKeyDetailsDialogOpen(false);
        setNewKeyDetails(null);
      }, 300);
    } else {
      setKeyDetailsDialogOpen(open);
    }
  }

  // Handle delete dialog close
  const handleDeleteDialogClose = (open: boolean) => {
    if (!open) {
      // Use setTimeout to ensure the dialog is fully closed before resetting state
      setTimeout(() => {
        setDeleteDialogOpen(false);
        setKeyToDelete(null);
      }, 300);
    } else {
      setDeleteDialogOpen(open);
    }
  }

  // Fetch API keys
  const { data, isLoading } = useApiKeys()
  const createApiKey = useCreateApiKey()
  const deleteApiKey = useDeleteApiKey()
  
  // Fetch applications for reference
  const { data: appsData } = useUserApplications()
  const applications = appsData?.data || []

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy')
    } catch {
      return dateString || 'N/A'
    }
  }

  // Safe access to data
  const apiKeys = data?.api_keys || []

  // Handle copy to clipboard
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text)
    toast("Copied to clipboard", {
      description: "API key has been copied to clipboard",
    })
  }

  // Get app name from app ID
  const getAppNameById = (appId: string) => {
    const app = applications.find(app => app.id === appId)
    return app ? app.name : appId
  }

  // Handle create API key
  const handleCreateApiKey = (name: string, appId: string) => {
    // Find the app name for the selected app ID
    const appName = getAppNameById(appId)
    
    createApiKey.mutate(
      {
        name: name,
        project: appName // Use the app name as the project value
      },
      {
        onSuccess: (data) => {
          // Reset form and close dialog
          setCreateDialogOpen(false)

          // Wait for the create dialog to fully close before showing the details dialog
          setTimeout(() => {
            // Store the new key details and show the alert dialog
            setNewKeyDetails({
              public_key: data.public_key,
              private_key: data.private_key
            })
            setKeyDetailsDialogOpen(true)
  
            // Show success toast
            toast.success("API Key Created", {
              description: "Your new API key has been created successfully",
            })
          }, 300)

          // The query will automatically refetch due to invalidation
        },
        onError: (error) => {
          console.error("Error creating API key:", error)
          toast.error("Failed to create API key", {
            description: error.response?.data?.message || error.message || "An error occurred"
          })
        }
      }
    )
  }

  // Open delete confirmation dialog
  const confirmDeleteApiKey = (id: string) => {
    setKeyToDelete(id)
    setDeleteDialogOpen(true)
  }

  // Handle delete API key
  const handleDeleteApiKey = () => {
    if (!keyToDelete) return

    deleteApiKey.mutate(keyToDelete, {
      onSuccess: (data) => {
        // Close the dialog
        setDeleteDialogOpen(false)
        setKeyToDelete(null)

        // Show success toast
        toast.success("API Key Deleted", {
          description: data.message || "The API key has been deleted successfully",
        })

        // The query will automatically refetch due to invalidation
      },
      onError: (error) => {
        console.error("Error deleting API key:", error)
        toast.error("Failed to delete API key", {
          description: error.response?.data?.message || error.message || "An error occurred"
        })

        // Close the dialog
        setDeleteDialogOpen(false)
        setKeyToDelete(null)
      }
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">API Keys</h1>
          <p className="text-muted-foreground">
            Manage API keys for authenticating with the Ruh.ai API.
          </p>
        </div>
        <CreateApiKeyButton onClick={() => setCreateDialogOpen(true)} />
      </div>

      {/* Create API Key Dialog */}
      <CreateApiKeyDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onCreateApiKey={handleCreateApiKey}
        isCreating={createApiKey.isPending}
      />

      {/* API Key Details Dialog */}
      <ApiKeyDetailsDialog
        open={keyDetailsDialogOpen}
        onOpenChange={handleKeyDetailsDialogClose}
        keyDetails={newKeyDetails}
      />

      {/* Delete API Key Dialog */}
      <DeleteApiKeyDialog
        open={deleteDialogOpen}
        onOpenChange={handleDeleteDialogClose}
        onDeleteApiKey={handleDeleteApiKey}
        isDeleting={deleteApiKey.isPending}
      />

      {/* Loading state */}
      {isLoading && (
        <div className="space-y-4">
          <div className="rounded-md border">
            <div className="p-6 space-y-4">
              <Skeleton className="h-5 w-1/4" />
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && apiKeys.length === 0 && (
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle>No API Keys Found</CardTitle>
            <CardDescription>
              You haven&apos;t created any API keys yet. Create your first API key to get started.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => setCreateDialogOpen(true)}>
              Create Your First API Key
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* API Keys table */}
      {!isLoading && apiKeys.length > 0 && (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px]">Name</TableHead>
                <TableHead className="w-[250px]">Public Key</TableHead>
                <TableHead>App</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apiKeys.map((apiKey) => (
                <TableRow key={apiKey.id}>
                  <TableCell className="font-medium">{apiKey.name}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                        {apiKey.public_key}
                      </code>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => handleCopy(apiKey.public_key)}
                      >
                        <CopyIcon className="h-4 w-4" />
                        <span className="sr-only">Copy</span>
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>{apiKey.project}</TableCell>
                  <TableCell>{formatDate(apiKey.created_at)}</TableCell>
                  <TableCell className="text-right">
                    <DeleteApiKeyButton
                      onClick={() => confirmDeleteApiKey(apiKey.id)}
                      disabled={deleteApiKey.isPending}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
