"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getDocsUrl } from "@/lib/helpers";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Bot, Code, Copy, Download, RefreshCw, Send, Settings, User, Variable } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

// Sample agent data
const agent = {
  id: "agent_12345",
  name: "Customer Support Assistant",
  description: "Handles tier-1 support requests and escalates as needed",
  model: "gpt-4o",
  provider: "OpenAI",
  variables: [
    { id: "var_1", name: "companyName", type: "text", defaultValue: "Acme Inc." },
    { id: "var_2", name: "supportEmail", type: "text", defaultValue: "<EMAIL>" },
    { id: "var_3", name: "maxResponseTime", type: "number", defaultValue: "24" }
  ]
};

// Message type definition
interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
}

export default function AgentTestPage() {
  const params = useParams();
  const router = useRouter();
  const agentId = params.id as string;

  const [messages, setMessages] = useState<Message[]>([
    {
      id: "msg_1",
      role: "assistant",
      content: "Hello! I'm the Customer Support Assistant for Acme Inc. How can I help you today?",
      timestamp: new Date()
    }
  ]);

  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("chat");
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [variablesOpen, setVariablesOpen] = useState(false);
  const [streamingEnabled, setStreamingEnabled] = useState(true);
  const [temperature, setTemperature] = useState("0.7");
  const [maxTokens, setMaxTokens] = useState("2048");
  const [variables, setVariables] = useState<Record<string, string>>(
    agent.variables.reduce((acc, v) => ({ ...acc, [v.name]: v.defaultValue }), {})
  );

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `msg_${Date.now()}`,
      role: "user",
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      // Generate a response based on the input
      let response = "I'm processing your request...";

      if (inputMessage.toLowerCase().includes("hello") || inputMessage.toLowerCase().includes("hi")) {
        response = `Hello! I'm the Customer Support Assistant for ${variables.companyName}. How can I help you today?`;
      } else if (inputMessage.toLowerCase().includes("refund")) {
        response = `I understand you're inquiring about a refund. ${variables.companyName}'s refund policy allows returns within 30 days of purchase. Would you like me to help you initiate a refund request?`;
      } else if (inputMessage.toLowerCase().includes("contact")) {
        response = `You can contact our support team at ${variables.supportEmail}. Our team typically responds within ${variables.maxResponseTime} hours.`;
      } else if (inputMessage.toLowerCase().includes("help")) {
        response = `I'd be happy to help! As ${variables.companyName}'s support assistant, I can assist with product information, troubleshooting, returns, and general inquiries. What specific help do you need today?`;
      } else {
        response = `Thank you for your message. I'll do my best to assist you with your inquiry about "${inputMessage}". Is there any specific information you're looking for regarding this topic?`;
      }

      // Add assistant response
      const assistantMessage: Message = {
        id: `msg_${Date.now()}`,
        role: "assistant",
        content: response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1500);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleClearChat = () => {
    setMessages([
      {
        id: "msg_1",
        role: "assistant",
        content: "Hello! I'm the Customer Support Assistant for Acme Inc. How can I help you today?",
        timestamp: new Date()
      }
    ]);
  };

  const handleDownloadTranscript = () => {
    const transcript = messages
      .map(msg => `[${msg.timestamp.toLocaleTimeString()}] ${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`)
      .join("\n\n");

    const blob = new Blob([transcript], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `agent-conversation-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Transcript downloaded successfully");
  };

  const handleCopyTranscript = () => {
    const transcript = messages
      .map(msg => `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`)
      .join("\n\n");

    navigator.clipboard.writeText(transcript);
    toast.success("Transcript copied to clipboard");
  };

  const handleUpdateVariable = (name: string, value: string) => {
    setVariables(prev => ({ ...prev, [name]: value }));
  };

  const handleBack = () => {
    router.push(`/dashboard/agents/${agentId}`);
  };


  return (
    <div className="flex flex-col h-[calc(100vh-4rem)]">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold">{agent.name}</h1>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {agent.model}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {agent.provider}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setVariablesOpen(true)}
          >
            <Variable className="h-4 w-4 mr-2" />
            Variables
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSettingsOpen(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Main content */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex-1 flex flex-col"
      >
        <div className="border-b px-4">
          <TabsList className="h-10">
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="api">API</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="chat" className="flex-1 flex flex-col p-0">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
              >
                <div className="flex items-start gap-3 max-w-[80%]">
                  {message.role === "assistant" && (
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mt-1">
                      <Bot className="h-4 w-4 text-primary" />
                    </div>
                  )}
                  <div
                    className={`rounded-lg px-4 py-2 ${
                      message.role === "user"
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted"
                    }`}
                  >
                    <div className="whitespace-pre-wrap">{message.content}</div>
                    <div className="text-xs opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                  {message.role === "user" && (
                    <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center mt-1">
                      <User className="h-4 w-4 text-primary-foreground" />
                    </div>
                  )}
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex items-start gap-3 max-w-[80%]">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mt-1">
                    <Bot className="h-4 w-4 text-primary" />
                  </div>
                  <div className="rounded-lg px-4 py-2 bg-muted">
                    <div className="flex items-center gap-2">
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      <span>Thinking...</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t">
            <div className="flex items-center gap-2">
              <Textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type your message..."
                className="min-h-[60px] resize-none"
              />
              <Button
                onClick={handleSendMessage}
                disabled={isLoading || !inputMessage.trim()}
                className="h-[60px] px-4"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex justify-between mt-2">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearChat}
                >
                  Clear Chat
                </Button>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyTranscript}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadTranscript}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="api" className="flex-1 p-4 overflow-auto">
          <Card>
            <CardHeader>
              <CardTitle>API Integration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>API Endpoint</Label>
                <div className="flex items-center gap-2">
                  <Input
                    readOnly
                    value={`https://api.ruh.ai/v1/agents/${agentId}/invoke`}
                    className="font-mono text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(`https://api.ruh.ai/v1/agents/${agentId}/invoke`);
                      toast.success("API endpoint copied to clipboard");
                    }}
                  >
                    Copy
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Example Request</Label>
                <div className="bg-muted p-4 rounded-md font-mono text-xs overflow-x-auto">
                  {`fetch('https://api.ruh.ai/v1/agents/${agentId}/invoke', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    input: "Your message here",
    variables: {
${Object.entries(variables).map(([key, value]) => `      ${key}: "${value}"`).join(',\n')}
    }
  })
})`}
                </div>
              </div>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => {
                  window.open(getDocsUrl(), "_blank");
                  toast.info("Opening documentation in a new tab");
                }}
              >
                <Code className="h-4 w-4 mr-2" />
                View Full API Documentation
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Settings Dialog */}
      <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Agent Settings</DialogTitle>
            <DialogDescription>
              Configure settings for this agent test session
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="streaming">Streaming Responses</Label>
                <p className="text-sm text-muted-foreground">
                  Enable token-by-token streaming
                </p>
              </div>
              <Switch
                id="streaming"
                checked={streamingEnabled}
                onCheckedChange={setStreamingEnabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="temperature">Temperature</Label>
              <div className="flex items-center gap-4">
                <Input
                  id="temperature"
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={temperature}
                  onChange={(e) => setTemperature(e.target.value)}
                />
                <span className="text-sm text-muted-foreground w-12">
                  {parseFloat(temperature).toFixed(1)}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">
                Higher values (0.7-1.0) make output more random, lower values (0.2-0.5) make it more deterministic
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="max-tokens">Max Tokens</Label>
              <Select value={maxTokens} onValueChange={setMaxTokens}>
                <SelectTrigger>
                  <SelectValue placeholder="Select max tokens" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1024">1024 tokens</SelectItem>
                  <SelectItem value="2048">2048 tokens</SelectItem>
                  <SelectItem value="4096">4096 tokens</SelectItem>
                  <SelectItem value="8192">8192 tokens</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Maximum number of tokens to generate in the response
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setSettingsOpen(false)}>
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Variables Dialog */}
      <Dialog open={variablesOpen} onOpenChange={setVariablesOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Agent Variables</DialogTitle>
            <DialogDescription>
              Configure variables for this agent test session
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {agent.variables.map((variable) => (
              <div key={variable.id} className="space-y-2">
                <Label htmlFor={variable.id}>{variable.name}</Label>
                <Input
                  id={variable.id}
                  value={variables[variable.name]}
                  onChange={(e) => handleUpdateVariable(variable.name, e.target.value)}
                  type={variable.type === "number" ? "number" : "text"}
                />
                <p className="text-xs text-muted-foreground">
                  Type: {variable.type}
                </p>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button onClick={() => setVariablesOpen(false)}>
              Save Variables
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
