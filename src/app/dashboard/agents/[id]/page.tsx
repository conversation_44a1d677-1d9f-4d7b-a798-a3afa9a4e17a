"use client";

import {
  AlertD<PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { useAgent, usePublishAgentChanges, agent<PERSON><PERSON>s } from "@/hooks/use-agents"; // Import usePublishAgentChanges
import { useQueryClient } from "@tanstack/react-query";
import {
  AlertTriangle,
  ArrowLeft,
  Bot,
  CheckCircle,
  CheckCircle2, // Added for marketplace modal
  Code,
  Edit,
  FileText,
  Globe,
  Link2,
  Loader2,
  Lock,
  MessageSquare,
  PauseCircle,
  Rocket,
  Trash2,
  Upload, // Added for marketplace modal
  Workflow,
  Wrench,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import Image from "next/image";
import { AgentVersionsTab } from "@/features/agents/components/AgentVersionsTab";

export default function AgentDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const agentId = params.id as string;
  const queryClient = useQueryClient();

  // Fetch agent data using the useAgent hook
  const { data: agentResponse, isLoading, isError, error } = useAgent(agentId);

  // Move this hook call to the top, before any conditional returns
  const publishChangesMutation = usePublishAgentChanges();

  const [isPublic, setIsPublic] = useState(false);
  const [isCustomizable, setIsCustomizable] = useState(false);
  const [syncToMarketplace, setSyncToMarketplace] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [deactivateDialog, setDeactivateDialog] = useState(false);
  const [a2aDialog, setA2aDialog] = useState(false);
  const [a2aDeployDialog, setA2aDeployDialog] = useState(false);
  const [a2aEndpoint, setA2aEndpoint] = useState("");
  const [publicConfirmDialog, setPublicConfirmDialog] = useState(false);
  const [customizableConfirmDialog, setCustomizableConfirmDialog] = useState(false);
  const [syncMarketplaceConfirmDialog, setSyncMarketplaceConfirmDialog] = useState(false);
  const [pendingPublicValue, setPendingPublicValue] = useState(false);
  const [pendingSyncValue, setPendingSyncValue] = useState(false);
  const [publishChangesModalOpen, setPublishChangesModalOpen] = useState(false);
  const [showSyncAlert, setShowSyncAlert] = useState(false);

  // Update states when agent data is loaded
  useEffect(() => {
    if (agentResponse?.agent) {
      setIsPublic(agentResponse.agent.visibility === "public");
      setSyncToMarketplace(!!agentResponse.agent.is_changes_marketplace);
      setIsCustomizable(!!agentResponse.agent.is_customizable);
      console.log(syncToMarketplace);
    }
  }, [agentResponse, syncToMarketplace]); // Added syncToMarketplace to dependencies

  // Track if we should show sync alert based on actual changes
  useEffect(() => {
    if (agentResponse?.agent && agentResponse.agent.is_updated && !isLoading) {
      setShowSyncAlert(true);
    } else {
      setShowSyncAlert(false);
    }
  }, [agentResponse, isLoading]);

  // Refetch agent data when page becomes visible or focused (user navigates back from edit)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, refetch agent data
        queryClient.invalidateQueries({ queryKey: agentKeys.details(agentId) });
      }
    };

    const handleFocus = () => {
      // Window gained focus, refetch agent data
      queryClient.invalidateQueries({ queryKey: agentKeys.details(agentId) });
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [agentId, queryClient]);

  const handleBack = () => {
    router.push("/dashboard/agents");
  };

  const handleEdit = () => {
    router.push(`/dashboard/agents/edit/${agentId}`);
  };

  const handleMakePublic = (value: boolean) => {
    // Check if agent is imported
    if (agentResponse?.agent?.is_imported && value) {
      toast.error("Imported agents cannot be made public");
      return;
    }

    setPendingPublicValue(value);
    setPublicConfirmDialog(true);
  };

  const confirmMakePublic = async () => {
    try {
      setPublicConfirmDialog(false);

      // Call the API to toggle visibility
      const { agentUpdateService } = await import("@/services/agent-update-service");
      const visibilityResponse = await agentUpdateService.toggleVisibility(agentId);

      if (visibilityResponse.success) {
        setIsPublic(pendingPublicValue);
        toast.success(`Agent is now ${pendingPublicValue ? 'public' : 'private'}`);
      } else {
        toast.error(`Failed to update visibility: ${visibilityResponse.message}`);
      }
    } catch (error) {
      console.error("Error updating visibility:", error);
      toast.error("Failed to update visibility: " +
        (error instanceof Error ? error.message : "Unknown error"));
    }
  };

  const handleCustomizable = (value: boolean) => {
    setIsCustomizable(value);
    setCustomizableConfirmDialog(true);
  };

  const confirmCustomizable = () => {
    setCustomizableConfirmDialog(false);
    // No API call for customizable toggle as per requirements
    toast.success(`Agent is now ${isCustomizable ? 'customizable' : 'not customizable'}`);
  };

  const handleSyncToMarketplace = (value: boolean) => {
    // Check if agent is public
    if (!isPublic && value) {
      toast.error("Agent must be public to enable sync to marketplace");
      return;
    }

    setPendingSyncValue(value);
    setSyncMarketplaceConfirmDialog(true);
  };

  const confirmSyncToMarketplace = async () => {
    try {
      setSyncMarketplaceConfirmDialog(false);

      // Call the API to update settings
      const { agentUpdateService } = await import("@/services/agent-update-service");
      const payload = {
        is_changes_marketplace: pendingSyncValue
      };

      const marketplaceResponse = await agentUpdateService.updateSettings(agentId, payload);

      if (marketplaceResponse.success) {
        setSyncToMarketplace(pendingSyncValue);
        toast.success(`Marketplace sync ${pendingSyncValue ? 'enabled' : 'disabled'}`);
      } else {
        toast.error(`Failed to update marketplace sync: ${marketplaceResponse.message}`);
      }
    } catch (error) {
      console.error("Error updating marketplace sync:", error);
      toast.error("Failed to update marketplace sync: " +
        (error instanceof Error ? error.message : "Unknown error"));
    }
  };

  const handleDeactivate = () => {
    setDeactivateDialog(false);
    toast.success("Agent deactivated successfully");
    // In a real implementation, you would update the agent status via API here
  };

  const handleDelete = async () => {
    try {
      // Close the dialog immediately to prevent further API calls
      setDeleteDialog(false);

      // Call the delete API directly instead of using the mutation
      const { agentService } = await import("@/services/agent-service");
      await agentService.deleteAgent(agentId);

      // Show success message and navigate away
      toast.success("Agent deleted successfully");
      router.push("/dashboard/agents");
    } catch (error) {
      console.error("Error deleting agent:", error);
      toast.error("Failed to delete agent: " + (error instanceof Error ? error.message : "Unknown error"));
    }
  };

  const handleA2ADeploy = () => {
    if (!a2aEndpoint) {
      toast.error("Please enter an A2A endpoint URL");
      return;
    }

    setA2aDeployDialog(false);
    toast.success("Agent deployed to A2A endpoint successfully");
    // In a real implementation, you would deploy the agent to A2A via API here
  };

  const handleTestAgent = () => {
    router.push(`/dashboard/agents/${agentId}/test`);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <Button
              variant="ghost"
              className="mb-2 -ml-2 h-8"
              onClick={handleBack}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Agents
            </Button>
            <Skeleton className="h-10 w-64 mb-2" />
            <div className="flex items-center gap-2 mt-1">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-32" />
            </div>
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Description</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-16 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state
  if (isError) {
    return (
      <div className="space-y-6">
        <Button
          variant="ghost"
          className="mb-2 -ml-2 h-8"
          onClick={handleBack}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Agents
        </Button>
        <Card className="border-destructive">
          <CardHeader className="pb-3">
            <CardTitle className="text-destructive flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Error Loading Agent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>Failed to load agent details: {error?.message || "Unknown error"}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              <Loader2 className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If no agent data is available
  if (!agentResponse?.agent) {
    return (
      <div className="space-y-6">
        <Button
          variant="ghost"
          className="mb-2 -ml-2 h-8"
          onClick={handleBack}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Agents
        </Button>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Agent Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p>The requested agent could not be found.</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={handleBack}
            >
              Return to Agents
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Destructure agent data for easier access
  const { agent } = agentResponse;
  const isActive = agent.status === "active";

  const handlePublishChangesAlert = () => {
    publishChangesMutation.mutate(
      { agentId, publishToMarketplace: true },
      {
        onSuccess: (data) => {
          if (data.success) {
            toast.success("Agent changes published to marketplace successfully!");
            setShowSyncAlert(false);
          } else {
            toast.error(`Failed to publish agent changes: ${data.message}`);
          }
        },
        onError: (error) => {
          console.error("Error publishing agent changes:", error);
          toast.error(
            "Failed to publish agent changes: " +
            (error instanceof Error ? error.message : "Unknown error")
          );
        },
      }
    );
  };

  return (
    <div className="space-y-6">
      {/* Back button */}
      <Button
        variant="ghost"
        className="mb-2 -ml-2 h-8"
        onClick={handleBack}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Agents
      </Button>

      {/* Agent Header: Avatar, Name, Details, and Action Buttons */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          {/* Agent Avatar or Icon */}
          <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20 flex-shrink-0">
            {agent.avatar && agent.avatar.startsWith("http") ? (
              <Image
                src={agent.avatar}
                alt={agent.name}
                className="h-12 w-12 rounded"
                width={48}
                height={48}
                onError={(e) => {
                  // Replace with fallback icon on error
                  e.currentTarget.style.display = "none";
                  const parent = e.currentTarget.parentElement;
                  if (parent) {
                    const fallback = document.createElement("div");
                    fallback.innerHTML =
                      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8 text-purple-600 dark:text-purple-400"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>';
                    parent.appendChild(fallback.firstChild as Node);
                  }
                }}
              />
            ) : (
              <Bot className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            )}
          </div>

          {/* Agent Title and Details */}
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">{agent.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={isActive ? "default" : "secondary"}>
                {isActive ? "Active" : "Inactive"}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1">
                {isPublic ? <Globe className="h-3 w-3" /> : <Lock className="h-3 w-3" />}
                {isPublic ? "Public" : "Private"}
              </Badge>
              <p className="text-sm text-muted-foreground">
                Created: {new Date(agent.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
        <div className="flex gap-2 flex-shrink-0">
          <Button
            variant="outline"
            onClick={handleEdit}
            disabled={agent.is_imported}
            title={agent.is_imported ? "Imported agents cannot be edited" : "Edit this agent"}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Agent
          </Button>
          <Button onClick={handleTestAgent} disabled>
            <MessageSquare className="mr-2 h-4 w-4" />
            Test Agent
          </Button>
        </div>
      </div>

      {/* Description */}
      <div className="border-b pb-6">
        <h2 className="text-lg font-semibold mb-2">Description</h2>
        <p>{agent.description}</p>
      </div>

      {/* Sync Changes to Marketplace Alert */}
      {showSyncAlert && agent.is_updated && (
        <div className="flex items-center justify-between gap-4 p-4 my-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-md border border-orange-300 dark:border-yellow-700">
          <div className="flex items-center gap-3">
            <Upload className="h-5 w-5 text-yellow-600" />
            <div>
              <div className="font-medium text-yellow-900 dark:text-yellow-200">You have unpublished changes</div>
              <div className="text-sm text-yellow-800 dark:text-yellow-200">Publish your latest changes to the marketplace so others can access the updated version.</div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              className="bg-yellow-600 text-white hover:bg-yellow-700 dark:bg-yellow-600 dark:hover:bg-yellow-700"
              onClick={handlePublishChangesAlert}
              disabled={publishChangesMutation.isPending}
            >
              {publishChangesMutation.isPending ? "Publishing..." : "Publish Changes"}
            </Button>
            <Button
              size="icon"
              variant="ghost"
              className="text-orange-600 hover:bg-orange-100 dark:text-orange-400 dark:hover:bg-orange-800"
              onClick={() => setShowSyncAlert(false)}
              aria-label="Close alert"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
            </Button>
          </div>
        </div>
      )}

      {/* System Prompt and Capabilities in grid */}
      <div className="border-b pb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* System Prompt */}
          <div className="border rounded-xl p-6">
            <div className="flex items-center gap-2 mb-4">
              <Code className="h-5 w-5" />
              <h2 className="text-lg font-semibold">System Prompt</h2>
            </div>
            <div className="bg-muted p-4 rounded-md text-sm font-mono whitespace-pre-wrap max-h-48 overflow-y-auto">
              {agent.system_message || "No system prompt specified"}
            </div>
          </div>

          {/* Capabilities */}
          <div className="border rounded-xl p-6">
            <div className="flex items-center gap-2 mb-4">
              <Bot className="h-5 w-5" />
              <h2 className="text-lg font-semibold">Capabilities</h2>
            </div>
            <div className="space-y-4 max-h-48 overflow-y-auto">
              {agent.agent_capabilities && agent.agent_capabilities.capabilities &&
                agent.agent_capabilities.capabilities.length > 0 ? (
                agent.agent_capabilities.capabilities.map((capability, index) => (
                  <div key={index}>
                    <h3 className="font-medium text-base mb-1">{capability.title}</h3>
                    <p className="text-sm text-muted-foreground">{capability.description}</p>
                  </div>
                ))
              ) : (
                <div className="text-center p-6 border rounded-lg">
                  <p className="text-muted-foreground">No capabilities defined</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="border-b pb-6 mb-5">
        <h2 className="text-lg font-semibold">Example Prompts</h2>
        <div className="space-y-4 max-h-48 overflow-y-auto mt-4">
          {agent.example_prompts && agent.example_prompts.length > 0 ? (
            agent.example_prompts.map((prompt, index) => (
              <div key={index} className="p-4 border rounded-lg bg-muted">
                <p className="text-sm text-muted-foreground">{prompt}</p>
              </div>
            ))
          ) : (
            <div className="text-center p-6 border rounded-lg">
              <p className="text-muted-foreground">No example prompts defined</p>
            </div>
          )}
        </div>
      </div>

      {/* Deployment */}
      <div className="border-b pb-6">
        <div className="flex items-center gap-2 mb-4">
          <Rocket className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Deployment</h2>
        </div>
        <p className="text-sm text-muted-foreground mb-4">Deploy your agent to make it available in your system</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Configure A2A */}
          {/* <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Configure A2A</CardTitle>
              <CardDescription>Set up this agent as an A2A compatible agent</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>A2A protocol compatible</span>
                </li>
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>Connect to other systems</span>
                </li>
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>Customize endpoint</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => setA2aDialog(true)}>
                <Code className="mr-2 h-4 w-4" />
                Configure A2A
              </Button>
            </CardFooter>
          </Card> */}

          {/* A2A Deployment */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-base">A2A Deployment</CardTitle>
                  <CardDescription>Advanced deployment with A2A protocol</CardDescription>
                </div>
                <Badge className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200">
                  Coming Soon
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>Skills & capabilities configuration</span>
                </li>
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>Multi-modal support</span>
                </li>
                <li className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>Advanced monitoring</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button disabled className="w-full" onClick={() => setA2aDialog(true)}>
                <Rocket className="mr-2 h-4 w-4" />
                Deploy as A2A
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Connected Tools and Workflows */}
      <div className="border-b pb-6">
        <div className="border rounded-xl p-6">
          <Tabs defaultValue="tools">
            <TabsList className="mb-4">
              <TabsTrigger value="tools">Connected Tools</TabsTrigger>
              <TabsTrigger value="workflows">Associated Workflows</TabsTrigger>
              <TabsTrigger value="knowledge">Knowledge</TabsTrigger>
              <TabsTrigger value="versions">Versions</TabsTrigger>
            </TabsList>

            <TabsContent value="tools" className="space-y-6">
              {agent.mcps && agent.mcps.length > 0 ? (
                <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                  {agent.mcps.map((mcp) => (
                    <Card key={mcp.id} className="h-72 flex flex-col">
                      <CardHeader className="pb-3 flex-shrink-0">
                        <CardTitle className="text-base flex items-center gap-2">
                          <Wrench className="h-4 w-4 flex-shrink-0" />
                          {mcp.name}
                        </CardTitle>
                        <CardDescription className="text-sm line-clamp-2">
                          {mcp.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0 flex-1 overflow-hidden flex flex-col">
                        <div className="flex items-center gap-2 mb-3 flex-shrink-0">
                          <Badge variant="outline" className="text-xs">
                            {mcp.status}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {mcp.visibility}
                          </Badge>
                        </div>

                        {mcp.mcp_tools_config?.tools && mcp.mcp_tools_config.tools.length > 0 ? (
                          <div className="flex-1 overflow-hidden flex flex-col">
                            <h4 className="text-sm font-medium mb-2 flex-shrink-0">Tools ({mcp.mcp_tools_config.tools.length})</h4>
                            <div className="overflow-y-auto flex-1">
                              <div className="grid grid-cols-2 gap-2">
                                {mcp.mcp_tools_config.tools.map((tool, toolIndex) => (
                                  <div key={toolIndex} className="text-xs p-1">
                                    <div className="font-medium truncate">{tool.name}</div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex-1 flex items-center justify-center">
                            <p className="text-xs text-muted-foreground">No tools available</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center p-6 border rounded-lg">
                  <Wrench className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No connected MCP servers</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    MCP servers provide tools and capabilities to your agent
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="workflows" className="space-y-6">
              {agent.workflows && agent.workflows.length > 0 ? (
                <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                  {agent.workflows.map((workflow) => (
                    <Card key={workflow.id} className="h-72 flex flex-col">
                      <CardHeader className="pb-3 flex-shrink-0">
                        <CardTitle className="text-base flex items-center gap-2">
                          <Workflow className="h-4 w-4 flex-shrink-0" />
                          {workflow.name}
                        </CardTitle>
                        <CardDescription className="text-sm line-clamp-2">
                          {workflow.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0 flex-1 overflow-hidden flex flex-col">
                        <div className="flex items-center gap-2 mb-3 flex-shrink-0">
                          <Badge variant="outline" className="text-xs">
                            {workflow.status}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {workflow.visibility}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            v{workflow.version}
                          </Badge>
                        </div>

                        {workflow.start_nodes && workflow.start_nodes.length > 0 ? (
                          <div className="flex-1 overflow-hidden flex flex-col">
                            <h4 className="text-sm font-medium mb-2 flex-shrink-0">Start Nodes ({workflow.start_nodes.length})</h4>
                            <div className="overflow-y-auto flex-1">
                              <div className="grid grid-cols-2 gap-2">
                                {workflow.start_nodes.map((node, nodeIndex) => (
                                  <div key={nodeIndex} className="text-xs p-1">
                                    <div className="font-medium truncate">{node.field}</div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex-1 flex items-center justify-center">
                            <p className="text-xs text-muted-foreground">No start nodes available</p>
                          </div>
                        )}

                        <div className="text-xs text-muted-foreground mt-3 flex-shrink-0">
                          Created: {new Date(workflow.created_at).toLocaleDateString()}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center p-6 border rounded-lg">
                  <Workflow className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No associated workflows</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Workflows help automate complex tasks and processes
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="knowledge" className="space-y-6">
              <div className="space-y-6">
                {/* Files Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Files
                  </h3>
                  {agent.files && agent.files.length > 0 ? (
                    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                      {agent.files.map((fileUrl, index) => (
                        <Card key={`file-${index}`}>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base flex items-center gap-2">
                              <FileText className="h-4 w-4 flex-shrink-0" />
                              <a href={fileUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                {fileUrl.split('/').pop()}
                              </a>
                            </CardTitle>
                          </CardHeader>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center p-6 border rounded-lg">
                      <p className="text-muted-foreground">No files attached</p>
                    </div>
                  )}
                </div>

                {/* URLs Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                    <Link2 className="h-5 w-5" />
                    URLs
                  </h3>
                  {agent.urls && agent.urls.length > 0 ? (
                    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                      {agent.urls.map((url, index) => (
                        <Card key={`url-${index}`}>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base flex items-center gap-2">
                              <Link2 className="h-4 w-4 flex-shrink-0" />
                              <a href={url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                {url}
                              </a>
                            </CardTitle>
                          </CardHeader>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center p-6 border rounded-lg">
                      <p className="text-muted-foreground">No URLs attached</p>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="versions" className="space-y-6">
              <AgentVersionsTab agentId={agentId} />
            </TabsContent>
          </Tabs>
        </div>
      </div>


      {/* Agent Settings */}
      <div>
        <h2 className="text-xl font-semibold mb-6">Agent Settings</h2>

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Public Access</h3>
              <p className="text-sm text-muted-foreground">Make this agent available in the public marketplace</p>
            </div>
            <Switch
              checked={isPublic}
              onCheckedChange={handleMakePublic}
            />
          </div>

          {/* <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Sync changes to marketplace</h3>
              <p className="text-sm text-muted-foreground">Automatically update this agent in the marketplace when changes are made</p>
            </div>
            <Switch
              checked={syncToMarketplace}
              onCheckedChange={handleSyncToMarketplace}
              disabled={!isPublic}
            />
          </div> */}

          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Customizable Agent</h3>
              <p className="text-sm text-muted-foreground">Allow users to customize this agent when they use it</p>
            </div>
            <Switch
              checked={isCustomizable}
              onCheckedChange={handleCustomizable}
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mt-8">
          <Button
            variant="outline"
            className="text-amber-500 border-amber-500/20 hover:bg-amber-500/10"
            onClick={() => setDeactivateDialog(true)}
          >
            <PauseCircle className="mr-2 h-4 w-4" />
            Deactivate Agent
          </Button>

          <Button
            variant="outline"
            className="text-destructive border-destructive/20 hover:bg-destructive/10"
            onClick={() => setDeleteDialog(true)}
            disabled={agent.is_imported} // Disable delete for imported agents
            title={agent.is_imported ? "Imported agents cannot be deleted" : "Delete this agent"}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete Agent
          </Button>

          {/* Temporarily hide A2A Deploy button */}
          {/* <Button
            variant="outline"
            onClick={() => setA2aDialog(true)}
          >
            <Rocket className="mr-2 h-4 w-4" />
            Deploy to A2A
          </Button> */}

        </div>
      </div>

      {/* Dialogs */}
      {/* Delete Dialog */}
      <AlertDialog open={deleteDialog} onOpenChange={setDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your
              agent and remove your data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Deactivate Dialog */}
      <AlertDialog open={deactivateDialog} onOpenChange={setDeactivateDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to deactivate this agent?</AlertDialogTitle>
            <AlertDialogDescription>
              Deactivating the agent will make it unavailable for use. You can reactivate it later.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeactivate}>
              Deactivate
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* A2A Configure Dialog */}
      <Dialog open={a2aDialog} onOpenChange={setA2aDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Configure A2A Endpoint</DialogTitle>
            <DialogDescription>
              Enter the endpoint URL for your A2A compatible agent.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="a2aEndpoint" className="text-right">
                Endpoint URL
              </Label>
              <Input
                id="a2aEndpoint"
                value={a2aEndpoint}
                onChange={(e) => setA2aEndpoint(e.target.value)}
                className="col-span-3"
                placeholder="https://your-a2a-endpoint.com"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" onClick={() => setA2aDeployDialog(true)} disabled={!a2aEndpoint}>
              Deploy to A2A
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* A2A Deploy Confirmation Dialog */}
      <AlertDialog open={a2aDeployDialog} onOpenChange={setA2aDeployDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm A2A Deployment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to deploy this agent to the A2A endpoint:
              <br />
              <strong>{a2aEndpoint}</strong>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleA2ADeploy}>
              Deploy
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Public Confirmation Dialog */}
      <AlertDialog open={publicConfirmDialog} onOpenChange={setPublicConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Making Agent {pendingPublicValue ? 'Public' : 'Private'}</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to make this agent {pendingPublicValue ? 'public' : 'private'}? This will affect its visibility in the marketplace.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsPublic(!pendingPublicValue)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmMakePublic}>Confirm</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Sync Marketplace Confirmation Dialog */}
      <AlertDialog open={syncMarketplaceConfirmDialog} onOpenChange={setSyncMarketplaceConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Syncing to Marketplace</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {pendingSyncValue ? 'enable' : 'disable'} syncing changes to the marketplace?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSyncToMarketplace(!pendingSyncValue)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmSyncToMarketplace}>Confirm</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
