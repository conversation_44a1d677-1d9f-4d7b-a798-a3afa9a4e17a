"use client";

import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState, Suspense } from "react";
import { AgentCreationWizard } from "@/components/agents/agent-creation-wizard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";

function AgentCreationPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const step = searchParams.get("step");
  const type = searchParams.get("type");
  const endpoint = searchParams.get("endpoint");

  const [currentStep, setCurrentStep] = useState(step || "foundation");
  const [initialData, setInitialData] = useState<Record<string, unknown> | null>(null);

  // Handle A2A agent creation
  useEffect(() => {
    if (type === "a2a" && endpoint) {
      // Set initial data for A2A agent
      setInitialData({
        name: "A2A Agent",
        description: "Agent imported via A2A protocol",
        category: "imported",
        aiProvider: "external",
        aiModel: "a2a-integration",
        systemPrompt: `This agent is connected via the A2A protocol at endpoint: ${endpoint}`,
        selectedTools: ["a2a-connector"],
      });

      // Show toast notification
      toast.success("A2A agent configuration loaded");
    }
  }, [type, endpoint]);

  // Map step IDs to more readable names
  const stepNames = {
    foundation: "Foundation",
    "core-logic": "Core Logic",
    capabilities: "Capabilities",
    automation: "Automation",
    knowledge: "Knowledge",
    configuration: "Configuration",
    preview: "Preview",
  };

  const currentStepName =
    stepNames[currentStep as keyof typeof stepNames] || currentStep;

  const handleBack = () => {
    router.push('/dashboard/agents');
  };

  return (
    <div className="space-y-6">
      <div>
        <Button
          variant="ghost"
          className="mb-2 -ml-2 h-8"
          onClick={handleBack}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Agents
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Create AI Agent</h1>
        <p className="text-muted-foreground">
          Configure your custom AI agent with the following steps
        </p>
        <p className="text-sm text-muted-foreground mt-1">
          Current step: <span className="font-medium">{currentStepName}</span>
        </p>
      </div>
      <AgentCreationWizard
        currentStep={currentStep}
        setCurrentStep={setCurrentStep}
        initialData={initialData}
      />
    </div>
  );
}

export default function CreateAgentPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AgentCreationPage />
    </Suspense>
  );
}
