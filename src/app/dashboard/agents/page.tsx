"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useCreateAgent, useUserAgents } from "@/hooks/use-agents";
import { useDebounce } from "@/hooks/use-debounce";
import { getMarketplaceUrl } from "@/lib/helpers";
import { Agent } from "@/services/agent-service";
import { format } from "date-fns";
import {
  <PERSON>t,
  Eye,
  Filter,
  PlusIcon,
  Search,
  Store,
  X
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

export default function AgentsPage() {
  const router = useRouter();
  const createAgent = useCreateAgent();

  // State for create dialog
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [creationType, setCreationType] = useState<
    "scratch" | "marketplace" | "a2a"
  >("scratch");
  const [endpointUrl, setEndpointUrl] = useState("");
  const [agentTypeFeedback, setAgentTypeFeedback] = useState("");

  // State for filters
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedVisibilities, setSelectedVisibilities] = useState<string[]>([]);
  const [filterType, setFilterType] = useState("all");
  const [filterSource, setFilterSource] = useState("all");
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Debounce search query with 2 second delay
  const debouncedSearchQuery = useDebounce(searchQuery, 2000);

  // Create filter params for API using useMemo to avoid recreating on every render
  const filterParams = useMemo(() => ({
    page: currentPage,
    page_size: pageSize,
    search: debouncedSearchQuery || undefined,
    status: selectedStatuses.length > 0 ? selectedStatuses : undefined,
    visibility: selectedVisibilities.length > 0 ? selectedVisibilities : undefined,
  }), [currentPage, pageSize, debouncedSearchQuery, selectedStatuses, selectedVisibilities]);

  // Log filter params for debugging
  useEffect(() => {
    console.log('Agent filter params:', filterParams);
  }, [filterParams]);

  // Fetch agents from API with filters
  const { data, isLoading, isError, error } = useUserAgents(filterParams);

  // Handle status filter change - only one can be selected
  const handleStatusChange = (status: string) => {
    setSelectedStatuses(prev => {
      // If the status is already selected, deselect it
      if (prev.includes(status)) {
        return [];
      }
      // Otherwise, replace any existing selection with the new status
      return [status];
    });
    // Reset to page 1 when filters change
    setCurrentPage(1);
  };

  // Handle visibility filter change - only one can be selected
  const handleVisibilityChange = (visibility: string) => {
    setSelectedVisibilities(prev => {
      // If the visibility is already selected, deselect it
      if (prev.includes(visibility)) {
        return [];
      }
      // Otherwise, replace any existing selection with the new visibility
      return [visibility];
    });
    // Reset to page 1 when filters change
    setCurrentPage(1);
  };

  // Handle reset filters
  const handleResetFilters = () => {
    setSelectedStatuses([]);
    setSelectedVisibilities([]);
    setSearchQuery("");
    setFilterType("all");
    setFilterSource("all");
    setCurrentPage(1);
  };

  // Handle search with debounce
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setCurrentPage(1);
  };

  // Show error toast when API call fails - only once
  useEffect(() => {
    if (isError && error) {
      console.error('Agent API error:', error);
      const errorMessage = error.response?.data?.message || 'Failed to fetch agents';
      const statusCode = error.response?.status;
      toast.error(`Error ${statusCode}: ${errorMessage}`);
    }
  }, [isError, error]);

  // Handle creating a new agent
  const handleCreateAgent = () => {
    setShowCreateDialog(true);
  };

  const handleExploreMarketplace = () => {
    const marketplaceUrl = getMarketplaceUrl() || "https://ruh-marketplace.rapidinnovation.dev";
    window.open(marketplaceUrl, "_blank");
    toast.info("Opening marketplace in a new tab");
  };

  const handleDetectAgentType = () => {
    if (!endpointUrl) {
      toast.error("Please enter an agent endpoint URL");
      return;
    }

    setAgentTypeFeedback("Detecting agent type...");

    // In a real app, this would call an API to detect the agent type
    setTimeout(() => {
      setAgentTypeFeedback("Detected agent type: Multimodal Assistant");
      toast.success("Agent detected successfully!");
    }, 1000);
  };

  const handleCreateConfirm = () => {
    if (creationType === "a2a" && !endpointUrl) {
      toast.error("Please enter an agent endpoint URL");
      return;
    }

    setShowCreateDialog(false);

    switch (creationType) {
      case "scratch":
        // Navigate to the agent creation wizard
        router.push("/dashboard/agents/create");
        break;
      case "marketplace":
        const marketplaceUrl = getMarketplaceUrl() || "https://ruh-marketplace.rapidinnovation.dev";
        window.open(marketplaceUrl, "_blank");
        toast.info("Opening marketplace in a new tab");
        break;
      case "a2a":
        toast.success("Agent detected! Configuring A2A integration...");
        // In a real app, this would send the endpoint URL to the backend
        setTimeout(() => {
          // Navigate to the agent creation wizard with A2A parameters
          router.push(
            `/dashboard/agents/create?type=a2a&endpoint=${encodeURIComponent(
              endpointUrl
            )}`
          );
        }, 1500);
        break;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch {
      return dateString || 'N/A';
    }
  };

  // Determine agent status based on available fields
  const getAgentStatus = (agent: Agent) => {
    if (agent.status) {
      return agent.status.toLowerCase() === 'active' ? 'Online' : 'Offline';
    }
    return 'Offline';
  };

  // Safe access to data
  const agents = data?.data || [];

  // Filter agents based on local filters (type and source)
  const filteredAgents = agents.filter((agent) => {
    // Type filter (would need to be added to the agent model)
    if (filterType !== "all" && agent.agent_category !== filterType) {
      return false;
    }

    // Source filter (would need to be added to the agent model)
    if (filterSource !== "all" && agent.model_provider !== filterSource) {
      return false;
    }

    return true;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">AI Agents</h1>
          <p className="text-muted-foreground">
            Deploy and manage AI agents to automate tasks and processes
          </p>
        </div>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            onClick={handleExploreMarketplace}
            className="w-full sm:w-auto"
          >
            <Store className="h-4 w-4 mr-2" />
            Explore Marketplace
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button onClick={handleCreateAgent} className="w-full sm:w-auto">
                <PlusIcon className="h-4 w-4 mr-2" />
                Create New Agent
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Agent</DialogTitle>
                <DialogDescription>
                  Choose how you want to create your new AI agent
                </DialogDescription>
              </DialogHeader>

              <div className="py-4">
                <RadioGroup
                  value={creationType}
                  onValueChange={(value) =>
                    setCreationType(value as "scratch" | "marketplace" | "a2a")
                  }
                  className="space-y-4"
                >
                  <div className="flex items-start gap-3 p-3 rounded hover:bg-accent/50 transition-colors">
                    <RadioGroupItem value="scratch" id="scratch" className="mt-1" />
                    <div className="flex-1">
                      <Label htmlFor="scratch" className="font-medium cursor-pointer">
                        Create from Scratch
                      </Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        Build a custom agent with our wizard
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-3 rounded hover:bg-accent/50 transition-colors">
                    <RadioGroupItem value="marketplace" id="marketplace" className="mt-1" />
                    <div className="flex-1">
                      <Label htmlFor="marketplace" className="font-medium cursor-pointer">
                        Add from Marketplace
                      </Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        Browse pre-built agents from our marketplace
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-3 rounded hover:bg-accent/50 transition-colors">
                    <RadioGroupItem value="a2a" id="a2a" className="mt-1" />
                    <div className="flex-1">
                      <Label htmlFor="a2a" className="font-medium cursor-pointer">
                        Add A2A Agent
                      </Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        Connect an existing agent via the A2A protocol
                      </p>
                    </div>
                  </div>
                </RadioGroup>

                {creationType === "a2a" && (
                  <div className="space-y-3 mt-6 p-3 border rounded-md bg-muted/30">
                    <div>
                      <Label htmlFor="endpoint" className="font-medium">Agent Endpoint URL</Label>
                      <div className="flex gap-2 mt-2">
                        <Input
                          id="endpoint"
                          placeholder="https://your-agent-endpoint.com"
                          value={endpointUrl}
                          onChange={(e) => setEndpointUrl(e.target.value)}
                          className="flex-1"
                        />
                        <Button variant="outline" onClick={handleDetectAgentType}>
                          Detect
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        We&apos;ll automatically detect the agent type and capabilities
                      </p>
                    </div>

                    {agentTypeFeedback && (
                      <div className="text-sm p-3 bg-accent/30 rounded-md border border-accent">
                        {agentTypeFeedback}
                      </div>
                    )}
                  </div>
                )}
              </div>

              <DialogFooter className="mt-2">
                <Button
                  variant="outline"
                  onClick={() => setShowCreateDialog(false)}
                  className="w-full sm:w-auto"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateConfirm}
                  disabled={creationType === "a2a" && !endpointUrl}
                  className="w-full sm:w-auto"
                >
                  {creationType === "scratch"
                    ? "Create Agent"
                    : creationType === "marketplace"
                    ? "Browse Marketplace"
                    : "Connect Agent"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search and filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search agents..."
            className="pl-8 w-full"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
        <Button
          variant="outline"
          className="sm:w-auto w-full"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </Button>
      </div>

      {/* Filters row */}
      {showFilters && (
        <div className="grid grid-cols-1 gap-4">
          {/* Status and Visibility Filters */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 bg-card rounded-lg border">
            <div>
              <div className="flex items-center gap-2 text-sm font-medium mb-3">
                <Filter className="h-4 w-4" />
                Status
              </div>
              <RadioGroup
                value={selectedStatuses.length > 0 ? selectedStatuses[0] : ""}
                onValueChange={handleStatusChange}
                className="flex flex-wrap gap-3"
              >
                <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                  <RadioGroupItem
                    id="status-active"
                    value="active"
                  />
                  <label htmlFor="status-active" className="text-sm cursor-pointer">
                    Active
                  </label>
                </div>
                <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                  <RadioGroupItem
                    id="status-inactive"
                    value="inactive"
                  />
                  <label htmlFor="status-inactive" className="text-sm cursor-pointer">
                    Inactive
                  </label>
                </div>
                <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                  <RadioGroupItem
                    id="status-all"
                    value=""
                  />
                  <label htmlFor="status-all" className="text-sm cursor-pointer">
                    All
                  </label>
                </div>
              </RadioGroup>
            </div>

            <div>
              <div className="flex items-center gap-2 text-sm font-medium mb-3">
                <Filter className="h-4 w-4" />
                Visibility
              </div>
              <RadioGroup
                value={selectedVisibilities.length > 0 ? selectedVisibilities[0] : ""}
                onValueChange={handleVisibilityChange}
                className="flex flex-wrap gap-3"
              >
                <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                  <RadioGroupItem
                    id="visibility-public"
                    value="public"
                  />
                  <label htmlFor="visibility-public" className="text-sm cursor-pointer">
                    Public
                  </label>
                </div>
                <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                  <RadioGroupItem
                    id="visibility-private"
                    value="private"
                  />
                  <label htmlFor="visibility-private" className="text-sm cursor-pointer">
                    Private
                  </label>
                </div>
                <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                  <RadioGroupItem
                    id="visibility-all"
                    value=""
                  />
                  <label htmlFor="visibility-all" className="text-sm cursor-pointer">
                    All
                  </label>
                </div>
              </RadioGroup>
            </div>
          </div>

          {/* Additional Filters */}
          {/* <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="type-filter" className="text-sm">
                Agent Type
              </Label>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger id="type-filter">
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="ai_agent">AI Agent</SelectItem>
                  <SelectItem value="chatbot">Chatbot</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="source-filter" className="text-sm">
                Source
              </Label>
              <Select value={filterSource} onValueChange={setFilterSource}>
                <SelectTrigger id="source-filter">
                  <SelectValue placeholder="All Sources" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  <SelectItem value="OpenAI">OpenAI</SelectItem>
                  <SelectItem value="Anthropic">Anthropic</SelectItem>
                  <SelectItem value="Cohere">Cohere</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div> */}

          {/* Reset Filters Button */}
          <div className="flex justify-end">
            <Button variant="outline" onClick={handleResetFilters}>
              <X className="h-4 w-4 mr-2" />
              Reset Filters
            </Button>
          </div>
        </div>
      )}

      {/* Loading state */}
      {isLoading && (
        <>
          <div className="grid gap-3 xs:gap-4 sm:gap-5 lg:gap-6 grid-cols-1 xs:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="rounded-lg border bg-card shadow-sm p-3 xs:p-4 sm:p-5">
                <div className="flex items-start gap-2 xs:gap-3 sm:gap-4">
                  <Skeleton className="h-9 w-9 xs:h-10 xs:w-10 sm:h-12 sm:w-12 rounded-lg flex-shrink-0" />
                  <div className="flex-1 min-w-0 space-y-1.5 xs:space-y-2">
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-4 xs:h-5 w-2/3" />
                      <Skeleton className="h-3 xs:h-4 w-12 xs:w-16 ml-1 xs:ml-2" />
                    </div>
                    <Skeleton className="h-3 xs:h-4 w-1/3" />
                    <Skeleton className="h-3 xs:h-4 w-full" />
                    <Skeleton className="h-3 xs:h-4 w-full" />
                  </div>
                </div>
                <div className="mt-3 xs:mt-4 sm:mt-5 pt-2 xs:pt-3 sm:pt-4 border-t">
                  <div className="flex justify-between">
                    <div className="w-[45%]">
                      <Skeleton className="h-2 xs:h-3 w-8 xs:w-12 mb-1" />
                      <Skeleton className="h-3 xs:h-4 w-full" />
                    </div>
                    <div className="w-[45%]">
                      <Skeleton className="h-2 xs:h-3 w-8 xs:w-12 mb-1 ml-auto" />
                      <Skeleton className="h-3 xs:h-4 w-full" />
                    </div>
                  </div>
                </div>
                <div className="mt-3 xs:mt-4 sm:mt-5">
                  <div className="flex flex-col xs:flex-row gap-1.5 xs:gap-2">
                    <Skeleton className="h-7 xs:h-8 sm:h-9 w-full" />
                    <Skeleton className="h-7 xs:h-8 sm:h-9 w-full" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination skeleton */}
          <div className="flex items-center justify-between mt-6">
            <Skeleton className="h-5 w-40" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>
        </>
      )}

      {/* Error state - Handled by error boundary */}

      {/* Empty state */}
      {!isLoading && agents.length === 0 && (
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle>No Agents Found</CardTitle>
            <CardDescription>
              You haven&apos;t created any agents yet. Create your first agent
              to get started.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button
              onClick={handleCreateAgent}
              disabled={createAgent.isPending}
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Your First Agent
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* No results state */}
      {!isLoading && agents.length > 0 && filteredAgents.length === 0 && (
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle>No Matching Agents</CardTitle>
            <CardDescription>
              No agents match your search criteria. Try adjusting your filters
              or search term.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button
              variant="outline"
              onClick={handleResetFilters}
            >
              Clear Filters
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Agents grid */}
      {!isLoading && filteredAgents.length > 0 && (
        <>
          <div className="grid gap-3 xs:gap-4 sm:gap-5 lg:gap-6 grid-cols-1 xs:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
            {filteredAgents.map((agent) => {
              const status = getAgentStatus(agent);
              return (
                <div
                  key={agent.id}
                  className="rounded-lg border bg-card shadow-sm flex flex-col h-full"
                >
                  <div className="p-3 xs:p-4 sm:p-5 flex-grow">
                    <div className="flex items-start gap-2 xs:gap-3 sm:gap-4">
                      <div className="flex h-9 w-9 xs:h-10 xs:w-10 sm:h-12 sm:w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20 flex-shrink-0">
                        {agent.avatar && agent.avatar.startsWith("http") ? (
                          <Image
                            src={agent.avatar}
                            alt={agent.name}
                            className="h-6 w-6 xs:h-7 xs:w-7 sm:h-8 sm:w-8 rounded"
                            width={32}
                            height={32}
                            onError={(e) => {
                              // Replace with fallback icon on error
                              e.currentTarget.style.display = "none";
                              const parent = e.currentTarget.parentElement;
                              if (parent) {
                                const fallback = document.createElement("div");
                                fallback.innerHTML =
                                  '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 xs:h-5 xs:w-5 sm:h-6 sm:w-6 text-purple-600 dark:text-purple-400"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>';
                                parent.appendChild(fallback.firstChild as Node);
                              }
                            }}
                          />
                        ) : (
                          <Bot className="h-4 w-4 xs:h-5 xs:w-5 sm:h-6 sm:w-6 text-purple-600 dark:text-purple-400" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-xs xs:text-sm sm:text-base truncate">{agent.name}</h3>
                          <span
                            className={`flex items-center gap-1 text-xs font-medium ml-1 xs:ml-2 flex-shrink-0 ${
                              status === "Online"
                                ? "text-green-500"
                                : status === "Offline"
                                ? "text-red-500"
                                : "text-orange-500"
                            }`}
                          >
                            <span
                              className={`h-1.5 w-1.5 xs:h-2 xs:w-2 rounded-full ${
                                status === "Online"
                                  ? "bg-green-500"
                                  : status === "Offline"
                                  ? "bg-red-500"
                                  : "bg-orange-500"
                              }`}
                            ></span>
                            {status}
                          </span>
                        </div>
                        <p className="mt-0.5 xs:mt-1 text-xs text-muted-foreground truncate">
                          ID: {agent.id.substring(0, 8)}...
                        </p>
                        <p className="mt-1.5 xs:mt-2 sm:mt-3 text-xs sm:text-sm line-clamp-2">
                          {agent.description}
                        </p>
                      </div>
                    </div>

                    <div className="mt-3 xs:mt-4 sm:mt-5 flex items-center justify-between border-t pt-2 xs:pt-3 sm:pt-4">
                      <div className="min-w-0 max-w-[45%]">
                        <p className="text-[10px] xs:text-xs text-muted-foreground">Model</p>
                        <p className="text-[10px] xs:text-xs sm:text-sm font-medium truncate">
                          {agent.model_name || "Not specified"}
                        </p>
                      </div>
                      <div className="text-right min-w-0 max-w-[45%]">
                        <p className="text-[10px] xs:text-xs text-muted-foreground">Created</p>
                        <p className="text-[10px] xs:text-xs sm:text-sm font-medium truncate">
                          {formatDate(agent.created_at)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-3 xs:p-4 sm:p-5 pt-0 flex-shrink-0">
                    <div className="flex flex-col xs:flex-row gap-1.5 xs:gap-2">
                      <Button
                        variant="secondary"
                        size="sm"
                        className="w-full h-7 xs:h-8 sm:h-9 text-[10px] xs:text-xs sm:text-sm px-1.5 xs:px-2 sm:px-3"
                        onClick={() =>
                          router.push(`/dashboard/agents/${agent.id}`)
                        }
                      >
                        <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                        <span className="truncate">View</span>
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Pagination */}
          {data?.metadata && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                Showing {filteredAgents.length} of {data.metadata.total} agents
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                >
                  Previous
                </Button>
                <div className="text-sm font-medium">
                  Page {currentPage} of {Math.ceil(data.metadata.total / pageSize)}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage >= Math.ceil(data.metadata.total / pageSize)}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
