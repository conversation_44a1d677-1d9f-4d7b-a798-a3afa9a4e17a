"use client";

import { usePara<PERSON>, useSearchPara<PERSON>, useRouter } from "next/navigation";
import { useState, Suspense } from "react";
import { AgentUpdateWizard } from "@/components/agents/agent-update-wizard";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

function EditAgentPageContent() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const agentId = params.id as string;
  const step = searchParams.get("step");
  const [currentStep, setCurrentStep] = useState(step || "foundation");

  // Map step IDs to more readable names
  const stepNames = {
    foundation: "Foundation",
    "core-logic": "Core Logic",
    capabilities: "Capabilities",
    automation: "Automation",
    knowledge: "Knowledge",
    configuration: "Configuration",
    preview: "Preview",
  };

  const currentStepName =
    stepNames[currentStep as keyof typeof stepNames] || currentStep;

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2 mb-2"
          size="sm"
        >
          <ChevronLeft className="h-4 w-4" />
          Back to Agent Details
        </Button>

        <div>
          <h1 className="text-2xl font-bold tracking-tight">Edit AI Agent</h1>
          <p className="text-muted-foreground">
            Update your AI agent configuration with the following steps
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Current step: <span className="font-medium">{currentStepName}</span>
          </p>
        </div>
      </div>

      <AgentUpdateWizard
        agentId={agentId}
        currentStep={currentStep}
        setCurrentStep={setCurrentStep}
      />
    </div>
  );
}

export default function EditAgentPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EditAgentPageContent />
    </Suspense>
  );
}
