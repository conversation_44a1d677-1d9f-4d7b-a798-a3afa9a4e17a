"use client";
import { QuickActions } from "@/components/quick-actions";
import { AnalyticsOverview } from "@/components/analytics-overview";
import { ActivityFeed } from "@/components/activity-feed";
import { Button } from "@/components/ui/button";
import { RefreshCcw, Filter } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { CreditUsageBreakdownChart } from "@/components/charts/CreditUsageBreakdownChart";
import { PlatformAnalyticsChart } from "@/components/charts/PlatformAnalyticsChart";
import { AppCreditUsageChart } from "@/components/charts/AppCreditUsageChart";
import { useActivities, useLogs } from "@/hooks/use-activities";

export default function OverviewPage() {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const { data: activitiesData, isLoading: isLoadingActivities } = useActivities({ page_size: 10 });
  const { data: logsData, isLoading: isLoadingLogs } = useLogs({ page_size: 10 });

  const handleRefresh = () => {
    setIsRefreshing(true);
    // Simulate refresh
    setTimeout(() => {
      setIsRefreshing(false);
      toast.success("Dashboard data refreshed");
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Platform Overview
          </h1>
          <p className="text-muted-foreground">
            Monitor your platform&apos;s performance and activity
          </p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="w-full sm:w-auto"
        >
          <RefreshCcw
            className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
          />
          {isRefreshing ? "Refreshing..." : "Refresh Data"}
        </Button>
      </div>

      {/* Filters Section */}
      {/* <div className="flex flex-col sm:flex-row gap-4 items-center">
        <Button variant="outline" className="w-full sm:w-auto">
          <Filter className="h-4 w-4 mr-2" />
          Filter by Date Range
        </Button>
        <div className="text-sm text-muted-foreground">
          Showing data for: <span className="font-medium">Last 30 days</span>
        </div>
      </div> */}

      {/* Analytics Overview */}
      <AnalyticsOverview />

      {/* Credit Usage Chart */}
      <CreditUsageBreakdownChart />

      {/* Platform Analytics */}
      <PlatformAnalyticsChart />

      {/* API Events Table */}
      <AppCreditUsageChart />

      {/* Activity & Actions */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-4">
        <div className="lg:col-span-3">
          <ActivityFeed activities={activitiesData?.activities || []} isLoading={isLoadingActivities} />
        </div>
        <div className="lg:col-span-1">
          <QuickActions />
        </div>
      </div>
    </div>
  );
}
