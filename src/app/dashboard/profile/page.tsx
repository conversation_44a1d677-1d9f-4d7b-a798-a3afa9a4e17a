"use client"

import { User, Mail, Building2, Calendar, BarChart2, Key, Briefcase } from "lucide-react"
import { useUserDetails } from "@/hooks/use-auth"
import { Skeleton } from "@/components/ui/skeleton"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export default function ProfilePage() {
  const { data: userDetails, isLoading } = useUserDetails();

  // Get initials from full name for the avatar
  const getInitials = (name: string) => {
    if (!name) return '';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="space-y-8 max-w-5xl">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">My Profile</h1>
          <p className="text-muted-foreground">
            View your personal profile information and account details.
          </p>
        </div>

      </div>

      <div className="space-y-6">
        {/* Profile Header Section */}
        <div className="border rounded-lg">
          <div className="p-6">
            <div className="flex items-center gap-6">
              {isLoading ? (
                <Skeleton className="w-20 h-20 rounded-full flex-shrink-0" />
              ) : (
                <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <Avatar className="h-full w-full bg-primary/10">
                      {userDetails?.profileImage ? (
                        <AvatarImage src={userDetails.profileImage} alt="User avatar" />
                      ) : (
                        <AvatarFallback className="text-primary font-medium">
                          {getInitials(userDetails?.fullName || '')}
                        </AvatarFallback>
                      )}
                    </Avatar>
                </div>
              )}
              <div className="flex-1">
                {isLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                ) : (
                  <div>
                    <h2 className="text-xl font-semibold">{userDetails?.fullName || ''}</h2>
                    <p className="text-muted-foreground">{userDetails?.jobRole || ''}</p>
                    <div className="mt-2 bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 text-xs font-medium px-2 py-1 rounded inline-block">
                      Pro Plan
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Personal Information Section */}
        <div className="border rounded-lg">
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Full Name</p>
                  {isLoading ? (
                    <Skeleton className="h-4 w-32 mt-1" />
                  ) : (
                    <p className="text-sm text-muted-foreground">{userDetails?.fullName || 'Not specified'}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Email Address</p>
                  {isLoading ? (
                    <Skeleton className="h-4 w-48 mt-1" />
                  ) : (
                    <p className="text-sm text-muted-foreground">{userDetails?.email || 'Not specified'}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Organization</p>
                  {isLoading ? (
                    <Skeleton className="h-4 w-36 mt-1" />
                  ) : (
                    <p className="text-sm text-muted-foreground">{userDetails?.company || 'Not specified'}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Briefcase className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Department</p>
                  {isLoading ? (
                    <Skeleton className="h-4 w-36 mt-1" />
                  ) : (
                    <p className="text-sm text-muted-foreground">{userDetails?.department || 'Not specified'}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Account Information Section */}
        <div className="border rounded-lg">
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">Account Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Member Since</p>
                  <p className="text-sm text-muted-foreground">May 2024</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <BarChart2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Projects</p>
                  <p className="text-sm text-muted-foreground">12</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Key className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium">API Keys</p>
                  <p className="text-sm text-muted-foreground">3</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
