"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Plus,
  Filter,
  FileCode,
  FileJson,
  Terminal,
  Search,
  AlertCircle,
  FolderPlus,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { WorkflowCard } from "@/features/workflows/components/WorkflowCard";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { getWorkflowBuilderUrl, getMarketplaceUrl } from "@/lib/helpers";
import { useUserWorkflows } from "@/hooks/use-workflows";
import { format } from "date-fns";
import { useDebounce } from "@/hooks/use-debounce";

// Skeleton component for workflow card loading state
function WorkflowCardSkeleton() {
  return (
    <div className="border rounded-lg overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <Skeleton className="h-6 w-16" />
          <Skeleton className="h-4 w-24" />
        </div>
        <Skeleton className="h-7 w-3/4 mb-2" />
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-5/6 mb-6" />

        <div className="space-y-6">
          <div>
            <Skeleton className="h-5 w-12 mb-2" />
            <div className="flex flex-wrap gap-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-6 w-16" />
            </div>
          </div>

          <div>
            <Skeleton className="h-5 w-14 mb-2" />
            <div className="flex flex-wrap gap-2">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-6 w-20" />
            </div>
          </div>

          <div>
            <Skeleton className="h-5 w-16 mb-2" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>
      <div className="border-t p-4 flex justify-between">
        <Skeleton className="h-9 w-32" />
        <Skeleton className="h-9 w-28" />
      </div>
    </div>
  );
}

// Language options with icons
const languageOptions = [
  { id: "curl", label: "cURL", icon: Terminal },
  { id: "python", label: "Python", icon: FileCode },
  { id: "typescript", label: "TypeScript", icon: FileJson },
];

// Code examples in different languages
const codeExamples = {
  curl: (workflowId: string) => `curl -X POST \\
  https://api.ruh.ai/v1/workflows/${workflowId}/run \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "input": "Your workflow input here"
  }'`,

  python: (workflowId: string) => `import requests

url = "https://api.ruh.ai/v1/workflows/${workflowId}/run"
headers = {
    "Authorization": "Bearer YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "input": "Your workflow input here"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())`,

  typescript: (workflowId: string) => `const executeWorkflow = async () => {
  const response = await fetch('https://api.ruh.ai/v1/workflows/${workflowId}/run', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      input: "Your workflow input here"
    }),
  });

  const data = await response.json();
  console.log(data);
};

executeWorkflow();`,
};

export default function WorkflowsPage() {
  const router = useRouter();
  const [apiDialog, setApiDialog] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [codeLanguage, setCodeLanguage] = useState<
    "curl" | "python" | "typescript"
  >("curl");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedVisibilities, setSelectedVisibilities] = useState<string[]>([]);

  // Create filter params object
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Debounce search query with 2 second delay
  const debouncedSearchQuery = useDebounce(searchQuery, 2000);

  // Create filter params for API
  const filterParams = {
    page: currentPage,
    page_size: pageSize,
    search: debouncedSearchQuery || undefined,
    status: selectedStatuses.length > 0 ? selectedStatuses : undefined,
    visibility: selectedVisibilities.length > 0 ? selectedVisibilities : undefined,
  };

  // Fetch workflows from API with filters
  const { data: workflowsData, isLoading, isError, error } = useUserWorkflows(filterParams);

  const handleCreateWorkflow = () => {
    // Navigate to the workflow creation page
    const url = getWorkflowBuilderUrl();
    router.push(`${url}/workflows`);
  };

  const handleExploreMarketplace = () => {
    const marketplaceUrl = getMarketplaceUrl() || "https://marketplace.ruh.ai";
    window.open(marketplaceUrl, "_blank");
    toast.info("Opening workflow marketplace in a new tab");
  };

  const handleViewApiDocs = (workflowId: string) => {
    setSelectedWorkflow(workflowId);
    setApiDialog(true);
  };

  const handleStatusChange = (status: string) => {
    // If the status is already selected or empty (All), clear the selection
    setSelectedStatuses(status === "" ? [] : [status]);
    // Reset to page 1 when filters change
    setCurrentPage(1);
  };

  const handleVisibilityChange = (visibility: string) => {
    // If the visibility is already selected or empty (All), clear the selection
    setSelectedVisibilities(visibility === "" ? [] : [visibility]);
    // Reset to page 1 when filters change
    setCurrentPage(1);
  };

  const handleResetFilters = () => {
    setSelectedStatuses([]);
    setSelectedVisibilities([]);
    setSearchQuery("");
    setCurrentPage(1);
  };

  // Handle search with debounce
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setCurrentPage(1);
  };

  // Show error toast when API call fails - only once
  useEffect(() => {
    if (isError && error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch workflows';
      toast.error(errorMessage);
    }
  }, [isError, error]);

  // Map API workflows to UI format with static tools and agents data
  const workflows = workflowsData?.data?.map(workflow => {
    // Add static tools and agents data for now
    const staticTools = ["GPT-4", "API Integration", "Database"];
    const staticAgents = ["Processing Agent"];

    return {
      id: workflow.id,
      name: workflow.name,
      description: workflow.description || "No description provided",
      tools: staticTools,
      agents: staticAgents,
      status: workflow.status,
      visibility: workflow.visibility,
      createdAt: format(new Date(workflow.created_at), 'yyyy-MM-dd'),
      lastRun: workflow.updated_at ? format(new Date(workflow.updated_at), 'yyyy-MM-dd') : undefined
    };
  }) || [];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 w-full">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">AI Workflows</h1>
          <p className="text-muted-foreground">
            Create and manage AI workflows to automate your tasks
          </p>
        </div>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto justify-end">
          <Button
            variant="outline"
            onClick={handleExploreMarketplace}
            className="w-full sm:w-auto"
          >
            Explore Marketplace
          </Button>
          <Button onClick={handleCreateWorkflow} className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            Create New Workflow
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4 mb-6">
        {/* Search Bar */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search workflows..."
              className="pl-8 w-full"
              value={searchQuery}
              onChange={handleSearch}
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetFilters}
            className="w-auto self-start"
          >
            Reset Filters
          </Button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 bg-card rounded-lg border">
          <div>
            <div className="flex items-center gap-2 text-sm font-medium mb-3">
              <Filter className="h-4 w-4" />
              Status
            </div>
            <RadioGroup
              value={selectedStatuses.length > 0 ? selectedStatuses[0] : ""}
              onValueChange={handleStatusChange}
              className="flex flex-wrap gap-3"
            >
              <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                <RadioGroupItem
                  id="status-active"
                  value="active"
                />
                <label htmlFor="status-active" className="text-sm cursor-pointer">
                  Active
                </label>
              </div>
              <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                <RadioGroupItem
                  id="status-inactive"
                  value="inactive"
                />
                <label htmlFor="status-inactive" className="text-sm cursor-pointer">
                  Inactive
                </label>
              </div>
              <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                <RadioGroupItem
                  id="status-all"
                  value=""
                />
                <label htmlFor="status-all" className="text-sm cursor-pointer">
                  All
                </label>
              </div>
            </RadioGroup>
          </div>

          <div>
            <div className="flex items-center gap-2 text-sm font-medium mb-3">
              <Filter className="h-4 w-4" />
              Visibility
            </div>
            <RadioGroup
              value={selectedVisibilities.length > 0 ? selectedVisibilities[0] : ""}
              onValueChange={handleVisibilityChange}
              className="flex flex-wrap gap-3"
            >
              <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                <RadioGroupItem
                  id="visibility-public"
                  value="public"
                />
                <label htmlFor="visibility-public" className="text-sm cursor-pointer">
                  Public
                </label>
              </div>
              <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                <RadioGroupItem
                  id="visibility-private"
                  value="private"
                />
                <label htmlFor="visibility-private" className="text-sm cursor-pointer">
                  Private
                </label>
              </div>
              <div className="flex items-center gap-2 bg-muted/40 hover:bg-muted px-3 py-1.5 rounded-md cursor-pointer transition-colors">
                <RadioGroupItem
                  id="visibility-all"
                  value=""
                />
                <label htmlFor="visibility-all" className="text-sm cursor-pointer">
                  All
                </label>
              </div>
            </RadioGroup>
          </div>
        </div>
      </div>

      {/* Loading State - Skeleton UI */}
      {isLoading && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <WorkflowCardSkeleton key={index} />
          ))}
        </div>
      )}

      {/* Error State */}
      {isError && !isLoading && (
        <div className="flex flex-col items-center justify-center py-16 text-center border rounded-lg bg-card/50">
          <div className="bg-destructive/10 p-4 rounded-full mb-4">
            <AlertCircle className="h-12 w-12 text-destructive" />
          </div>
          <h3 className="text-xl font-medium mb-2">Failed to load workflows</h3>
          <p className="text-muted-foreground mb-8 max-w-md">
            There was an error loading your workflows. Please try again later.
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            size="lg"
          >
            Retry
          </Button>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !isError && workflows.length === 0 && (
        <div className="flex flex-col items-center justify-center py-16 text-center border rounded-lg bg-card/50">
          <div className="bg-primary/10 p-4 rounded-full mb-4">
            <FolderPlus className="h-12 w-12 text-primary" />
          </div>
          <h3 className="text-xl font-medium mb-2">No workflows found</h3>
          <p className="text-muted-foreground mb-8 max-w-md">
            You don&apos;t have any workflows yet. Create your first workflow to get started.
          </p>
          <Button onClick={handleCreateWorkflow} size="lg">
            <Plus className="mr-2 h-5 w-5" />
            Create New Workflow
          </Button>
        </div>
      )}

      {/* Workflow Cards */}
      {!isLoading && !isError && workflows.length > 0 && (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {workflows.map((workflow) => (
              <WorkflowCard
                key={workflow.id}
                workflow={workflow}
                onViewApiDocs={handleViewApiDocs}
              />
            ))}
          </div>

          {/* Pagination */}
          {workflowsData?.metadata && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                Showing {workflows.length} of {workflowsData.metadata.total} workflows
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                >
                  Previous
                </Button>
                <div className="text-sm font-medium">
                  Page {currentPage} of {Math.ceil(workflowsData.metadata.total / pageSize)}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage >= Math.ceil(workflowsData.metadata.total / pageSize)}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </>
      )}

      {/* API Integration Modal */}
      <Dialog open={apiDialog} onOpenChange={setApiDialog}>
        <DialogContent className="sm:max-w-md w-[calc(100%-2rem)]">
          <DialogHeader>
            <DialogTitle>API Integration</DialogTitle>
            <DialogDescription>
              Integrate this workflow with your application using our API
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">API Endpoint</h3>
              <div className="flex flex-wrap items-center gap-2">
                <div className="relative flex-1 min-w-0">
                  <Input
                    readOnly
                    value={`https://api.ruh.ai/v1/workflows/${selectedWorkflow}/run`}
                    className="font-mono text-xs pr-16"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        `https://api.ruh.ai/v1/workflows/${selectedWorkflow}/run`
                      );
                      toast.success("API endpoint copied to clipboard");
                    }}
                    className="absolute right-0 top-0 h-full px-3"
                  >
                    Copy
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Authentication</h3>
              <p className="text-sm text-muted-foreground">
                Include your API key in the request headers:
              </p>
              <div className="bg-muted p-3 rounded-md font-mono text-xs">
                Authorization: Bearer YOUR_API_KEY
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Example Request</h3>

              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-2">
                <div className="text-sm">Select language:</div>
                <div className="flex bg-background rounded-md p-1 flex-wrap">
                  {languageOptions.map((lang) => (
                    <button
                      key={lang.id}
                      onClick={() =>
                        setCodeLanguage(
                          lang.id as "curl" | "python" | "typescript"
                        )
                      }
                      className={`flex items-center gap-1.5 px-3 py-1 rounded text-xs ${
                        codeLanguage === lang.id
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-muted-foreground/10"
                      } mb-1 sm:mb-0`}
                    >
                      <lang.icon className="h-4 w-4" />
                      <span>{lang.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="bg-muted rounded-md">
                <div className="max-h-[240px] overflow-y-auto">
                  <pre className="p-4 text-xs whitespace-pre-wrap overflow-x-auto">
                    {selectedWorkflow
                      ? codeExamples[codeLanguage](selectedWorkflow)
                      : ""}
                  </pre>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="sm:justify-start flex-wrap gap-2">
            <Button
              variant="secondary"
              onClick={() => {
                window.open("/dashboard/documentation", "_blank");
                toast.info("Opening documentation in a new tab");
              }}
              className="w-full sm:w-auto"
            >
              View Full Documentation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
