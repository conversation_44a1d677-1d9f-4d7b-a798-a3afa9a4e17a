"use client";

import { useParams } from "next/navigation";
import { useWorkflow } from "@/hooks/use-workflows";
import { WorkflowDetails } from "@/features/workflows/components";

export default function WorkflowDetailsPage() {
  const params = useParams();
  const workflowId = params.id as string;
  const { data, isLoading, refetch } = useWorkflow(workflowId);

  return (
    <WorkflowDetails
      workflowId={workflowId}
      workflow={data?.workflow}
      isLoading={isLoading}
      refetchWorkflow={refetch}
    />
  );
}
