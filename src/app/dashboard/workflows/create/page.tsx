"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { ArrowLeft, Save  } from "lucide-react"
import { toast } from "sonner"

export default function CreateWorkflowPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [workflowData, setWorkflowData] = useState({
    name: "",
    description: "",
    category: "general",
    visibility: "private"
  })

  const handleChange = (field: string, value: string) => {
    setWorkflowData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!workflowData.name.trim()) {
      toast.error("Please enter a workflow name")
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // Simulate API call with a delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Show success message
      toast.success("Workflow created successfully")
      
      // Redirect to workflows list
      router.push("/dashboard/workflows")
    } catch (error) {
      console.error("Error creating workflow:", error)
      toast.error("Failed to create workflow")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Create New Workflow</h1>
            <p className="text-muted-foreground">
              Create a new workflow to automate tasks
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Workflow Details</CardTitle>
            <CardDescription>
              Enter the basic information for your new workflow
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="text-sm font-medium">Name</div>
              <Input
                placeholder="Enter workflow name"
                value={workflowData.name}
                onChange={(e) => handleChange("name", e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                A descriptive name for your workflow
              </p>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Description</div>
              <Textarea
                placeholder="Enter workflow description"
                value={workflowData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                rows={4}
              />
              <p className="text-sm text-muted-foreground">
                Describe what this workflow does and how it should be used
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <div className="text-sm font-medium">Category</div>
                <Select
                  value={workflowData.category}
                  onValueChange={(value) => handleChange("category", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="customer-support">Customer Support</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="data-processing">Data Processing</SelectItem>
                    <SelectItem value="content-creation">Content Creation</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Categorize your workflow for easier discovery
                </p>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium">Visibility</div>
                <Select
                  value={workflowData.visibility}
                  onValueChange={(value) => handleChange("visibility", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select visibility" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="private">Private</SelectItem>
                    <SelectItem value="team">Team</SelectItem>
                    <SelectItem value="public">Public</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Control who can see and use this workflow
                </p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              variant="outline" 
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="gap-2"
            >
              {isSubmitting ? (
                <>Creating...</>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Create Workflow
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
