import axiosClient from "@/lib/axios-client";
import {
  PreSignedUrlRequest,
  PreSignedUrlResponse
} from "@/shared/interfaces";

/**
 * Uploads a file to Google Cloud Storage using a presigned URL
 * @param presignedUrl The presigned URL from the backend
 * @param file The file to upload (can be <PERSON>, <PERSON><PERSON><PERSON>, or <PERSON>uffer)
 * @param options Optional upload options (contentType, metadata)
 * @returns Promise resolving to the public URL of the uploaded file
 */
export const uploadToGCS = async (
  presignedUrl: string,
  file: File
): Promise<string> => {
  try {
    // Using fetch API with the correct headers
    const response = await fetch(presignedUrl, {
      method: "PUT",
      headers: {
        "Content-Type": file.type,
      },
      body: file,
    });

    if (!response.ok) {
      // Attempt to read error response from GCS for more details
      let errorDetail = `Upload failed with status: ${response.status}`;
      try {
        const errorText = await response.text();
        errorDetail += ` - ${errorText}`;
      } catch (e) {
        // Ignore if reading text fails
        console.error("Failed to read error text from GCS response:", e);
      }
      throw new Error(errorDetail);
    }

    // --- Construct the public URL (Assumes standard GCS path & public access) ---
    const url = new URL(presignedUrl);
    const publicUrl = `${url.origin}${url.pathname}`;
    // --------------------------------------------------------------------------

    console.log(
      "File uploaded successfully, constructed public URL:",
      publicUrl
    );
    return publicUrl; // Return the constructed URL string
  } catch (error) {
    console.error("Error uploading file:", error);
    throw error;
  }
};

/**
 * GCS API functions for interacting with Google Cloud Storage
 */
export const gcsApi = {
  /**
   * Get a presigned URL for uploading a file to GCS using the new API endpoint
   * @param fileName The name of the file to upload
   * @param fileType The MIME type of the file
   * @param filePath The path where the file should be stored
   * @returns Promise resolving to the presigned URL response
   */
  getPresignedUrl: async (
    fileName: string,
    fileType: string,
    filePath: string
  ): Promise<PreSignedUrlResponse> => {
    try {
      const payload: PreSignedUrlRequest = {
        fileName,
        fileType,
        filePath,
      };

      const response = await axiosClient.post<PreSignedUrlResponse>(
        "/agents/media/presigned-url",
        payload
      );

      // Log the response for debugging
      console.log("Presigned URL response:", response.data);

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get presigned URL"
      );
    }
  },
};
