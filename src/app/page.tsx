"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { getAuthUrl } from "@/lib/helpers";
import { cookieUtils } from "@/lib/cookie";

export default function Home() {
  const router = useRouter();
  const authUrl = getAuthUrl();

  // Check for authentication on the client side
  useEffect(() => {
    const token = cookieUtils.getAccessToken();
    if (token) {
      router.push("/dashboard/overview");
    }
  }, [router]);

  // If not logged in, show landing page
  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-background to-background/80">
      <header className="w-full p-4 flex justify-end">
        <ThemeToggle />
      </header>

      <main className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="max-w-md w-full text-center space-y-10 p-8 rounded-lg border border-border/50 bg-card/50 backdrop-blur-sm shadow-sm">
          {/* Logo */}
          <div className="flex justify-center">
            <div className="flex items-center justify-center">
              <Image
                src="/logo.svg"
                alt="RUH AI Logo"
                width={100}
                height={100}
                className="h-36 w-36"
                priority
              />
            </div>
          </div>

          {/* Title and description */}
          <div className="space-y-3">
            <h1 className="text-3xl font-bold tracking-tight">RUH AI Developer Platform</h1>
            <p className="text-muted-foreground text-lg">
              Access your developer tools, workflows, and resources
            </p>
          </div>

          {/* Login button */}
          <div className="pt-2">
            <Button
              size="lg"
              className="w-full text-base py-6"
              onClick={() => {
                window.location.href = `${authUrl}/?redirect_url=${window.location.origin}/dashboard/overview`;
              }}
            >
              Login to access the Dashboard
            </Button>
          </div>
        </div>
      </main>

      <footer className="py-6 text-center text-sm text-muted-foreground">
        <p>© {new Date().getFullYear()} RUH AI. All rights reserved.</p>
      </footer>
    </div>
  );
}
