import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { 
  promptService, 
  OptimizePromptRequest, 
  OptimizePromptResponse 
} from '@/services/prompt-service';
import { ApiError } from './use-api';

// Hook for optimizing prompts
export function useOptimizePrompt(
  options?: Omit<
    UseMutationOptions<OptimizePromptResponse, AxiosError<ApiError>, OptimizePromptRequest>,
    'mutationFn'
  >
) {
  return useMutation<OptimizePromptResponse, AxiosError<ApiError>, OptimizePromptRequest>({
    mutationFn: (data: OptimizePromptRequest) => promptService.optimizePrompt(data),
    ...options,
  });
}
