import { useQuery } from "@tanstack/react-query";
import { fetchAgentAvatars, AvatarsResponse } from "@/services/agent-avatars";

export const useAgentAvatars = (page = 1, pageSize = 10) => {
  return useQuery<AvatarsResponse, Error>({
    queryKey: ["agent-avatars", page, pageSize],
    queryFn: () => fetchAgentAvatars(page, pageSize),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
