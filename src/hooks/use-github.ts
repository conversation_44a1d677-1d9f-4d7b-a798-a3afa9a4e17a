import { useQuery, useInfiniteQuery } from '@tanstack/react-query'
import { githubService, type GitHubRepositoriesResponse, type GitHubBranchesResponse, type GitHubRepoParams, type GitHubUser } from '@/services/github-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'

// Query keys for GitHub
export const githubKeys = {
  repositories: (params?: GitHubRepoParams) => ['github', 'repositories', params] as const,
  repositoriesInfinite: (params?: Omit<GitHubRepoParams, 'page'>) => ['github', 'repositories', 'infinite', params] as const,
  branches: (repoName: string) => ['github', 'branches', repoName] as const,
  user: () => ['github', 'user'] as const,
}

// Hook for fetching user repositories
export function useGitHubRepositories(params: GitHubRepoParams = {}, enabled: boolean = true) {
  return useQuery<GitHubRepositoriesResponse, AxiosError<ApiError>>({
    queryKey: githubKeys.repositories(params),
    queryFn: () => githubService.getUserRepositories(params),
    enabled,
    retry: 1,
    refetchOnWindowFocus: false,
  })
}

// Hook for fetching user repositories with infinite scroll
export function useGitHubRepositoriesInfinite(params: Omit<GitHubRepoParams, 'page'> = {}, enabled: boolean = true) {
  return useInfiniteQuery<GitHubRepositoriesResponse, AxiosError<ApiError>>({
    queryKey: githubKeys.repositoriesInfinite(params),
    queryFn: ({ pageParam = 1 }) => githubService.getUserRepositories({ ...params, page: pageParam as number }),
    enabled,
    retry: 1,
    refetchOnWindowFocus: false,
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.has_next_page) {
        return lastPage.metadata.current_page + 1;
      }
      return undefined;
    },
  })
}

// Hook for fetching repository branches
export function useGitHubBranches(repoName: string, enabled: boolean = true) {
  return useQuery<GitHubBranchesResponse, AxiosError<ApiError>>({
    queryKey: githubKeys.branches(repoName),
    queryFn: () => githubService.getRepositoryBranches(repoName),
    enabled: enabled && !!repoName,
    retry: 1,
    refetchOnWindowFocus: false,
  })
}

// Hook for fetching GitHub user details
export function useGitHubUser(enabled: boolean = true) {
  return useQuery<GitHubUser, AxiosError<ApiError>>({
    queryKey: githubKeys.user(),
    queryFn: () => githubService.getGitHubUser(),
    enabled,
    retry: 1,
    refetchOnWindowFocus: false,
  })
}
