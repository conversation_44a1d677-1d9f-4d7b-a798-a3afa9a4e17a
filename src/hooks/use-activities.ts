import { useQuery } from '@tanstack/react-query';
import activityService from '@/services/activity-service';
import { ActivitiesResponse, ActivityLogsResponse, ActivityEventsResponse, Activity as ApiActivity, ActivityLog, ActivityEvent } from '@/shared/interfaces';

interface UseActivitiesParams {
  page?: number;
  page_size?: number;
  // Add other filter parameters here if needed, matching the service
  // e.g., activity_type?: string;
  // e.g., resource_id?: string;
  enabled?: boolean; // To conditionally enable/disable the query
}

interface UseLogsParams {
  page?: number;
  page_size?: number;
  enabled?: boolean;
}

interface UseEventsParams {
  page?: number;
  page_size?: number;
  enabled?: boolean;
}

export const ACTIVITIES_QUERY_KEY = 'activities';
export const LOGS_QUERY_KEY = 'activity-logs';
export const EVENTS_QUERY_KEY = 'activity-events';
export const ACTIVITY_BY_ID_QUERY_KEY = 'activity-by-id';

export const useActivities = (params?: UseActivitiesParams) => {
  const { page = 1, page_size = 10, enabled = true, ...restParams } = params || {};

  return useQuery<ActivitiesResponse, Error>({
    queryKey: [ACTIVITIES_QUERY_KEY, { page, page_size, ...restParams }],
    queryFn: () => activityService.getActivities({ page, page_size, ...restParams }),
    placeholderData: (previousData) => previousData, // Useful for pagination
    enabled: enabled, // Control when the query should run
    // Add other react-query options if needed, e.g., staleTime, cacheTime
  });
};

export const useLogs = (params?: UseLogsParams) => {
  const { page = 1, page_size = 10, enabled = true } = params || {};

  return useQuery<ActivityLogsResponse, Error>({
    queryKey: [LOGS_QUERY_KEY, { page, page_size }],
    queryFn: () => activityService.getLogs({ page, page_size }),
    placeholderData: (previousData) => previousData,
    enabled: enabled,
  });
};

export const useEvents = (params?: UseEventsParams) => {
  const { page = 1, page_size = 10, enabled = true } = params || {};

  return useQuery<ActivityEventsResponse, Error>({
    queryKey: [EVENTS_QUERY_KEY, { page, page_size }],
    queryFn: () => activityService.getEvents({ page, page_size }),
    placeholderData: (previousData) => previousData,
    enabled: enabled,
  });
};

export const useActivityById = (activityId: string) => {
  return useQuery<ApiActivity, Error>({
    queryKey: [ACTIVITY_BY_ID_QUERY_KEY, activityId],
    queryFn: () => activityService.getActivityById(activityId),
    enabled: !!activityId, // Only run the query if activityId is available
  });
};