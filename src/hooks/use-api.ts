import { 
  useQuery, 
  useMutation, 
  UseQueryOptions, 
  UseMutationOptions,
  QueryClient
} from '@tanstack/react-query'
import axiosClient from '@/lib/axios-client'
import { AxiosError, AxiosRequestConfig } from 'axios'

// Type for API error responses
export type ApiError = {
  message: string
  errors?: Record<string, string[]>
  status?: number
}

// Generic GET request hook
export function useApiGet<TData = unknown>(
  endpoint: string,
  queryKey: string[],
  config?: AxiosRequestConfig,
  options?: Omit<UseQueryOptions<TData, AxiosError<ApiError>>, 'queryKey' | 'queryFn'>
) {
  return useQuery<TData, AxiosError<ApiError>>({
    queryKey,
    queryFn: async () => {
      const response = await axiosClient.get<TData>(endpoint, config)
      return response.data
    },
    ...options,
  })
}

// Generic POST request hook
export function useApiPost<TData = unknown, TVariables = unknown>(
  endpoint: string,
  options?: Omit<UseMutationOptions<TData, AxiosError<ApiError>, TVariables>, 'mutationFn'>
) {
  return useMutation<TData, AxiosError<ApiError>, TVariables>({
    mutationFn: async (variables) => {
      const response = await axiosClient.post<TData>(endpoint, variables)
      return response.data
    },
    ...options,
  })
}

// Generic PUT request hook
export function useApiPut<TData = unknown, TVariables = unknown>(
  endpoint: string,
  options?: Omit<UseMutationOptions<TData, AxiosError<ApiError>, TVariables>, 'mutationFn'>
) {
  return useMutation<TData, AxiosError<ApiError>, TVariables>({
    mutationFn: async (variables) => {
      const response = await axiosClient.put<TData>(endpoint, variables)
      return response.data
    },
    ...options,
  })
}

// Generic DELETE request hook
export function useApiDelete<TData = unknown>(
  endpoint: string,
  options?: Omit<UseMutationOptions<TData, AxiosError<ApiError>, void>, 'mutationFn'>
) {
  return useMutation<TData, AxiosError<ApiError>, void>({
    mutationFn: async () => {
      const response = await axiosClient.delete<TData>(endpoint)
      return response.data
    },
    ...options,
  })
}

// Utility function to invalidate queries (useful after mutations)
export const invalidateQueries = (queryClient: QueryClient, queryKey: string[]) => {
  return queryClient.invalidateQueries({ queryKey })
}
