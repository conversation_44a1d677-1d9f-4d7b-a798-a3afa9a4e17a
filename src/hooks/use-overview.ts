import { useQuery } from '@tanstack/react-query';
import { overviewService, OverviewData } from "@/services/overview-service";

export const OVERVIEW_QUERY_KEY = 'overview';

interface OverviewCardData {
  active_agents: number;
  credit_usage: number;
  agent_requests: number;
  workflow_requests: number;
  custom_mcps: number;
}

interface UseOverviewParams {
  userId: string;
  enabled?: boolean;
}

export const useOverview = (params: UseOverviewParams) => {
  const { userId, enabled = true } = params;

  return useQuery<OverviewData, Error, OverviewCardData>({
    queryKey: [OVERVIEW_QUERY_KEY, { userId }],
    queryFn: async () => {
      const response = await overviewService.getOverview(userId);
      return response.overview;
    },
    enabled: enabled && !!userId,
    select: (data) => ({
      active_agents: data.active_agents,
      credit_usage: data.credit_usage,
      agent_requests: data.agent_requests,
      workflow_requests: data.workflow_requests,
      custom_mcps: data.custom_mcps,
    }),
  });
}; 