import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  authService,
  type LoginRequest,
  type SignupRequest,
  type ForgotPasswordRequest,
  type ResetPasswordRequest,
  type AuthResponse,
  type UserDetails
} from '@/services/auth-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'
import { useRouter } from 'next/navigation'
import { cookieUtils } from '@/lib/cookie'
import { toast } from 'sonner'


// Query keys for auth
export const authKeys = {
  user: ['auth', 'user'] as const,
  userDetails: ['auth', 'userDetails'] as const,
}

// Hook for getting the current user
export function useCurrentUser() {
  return useQuery({
    queryKey: authKeys.user,
    queryFn: () => authService.getCurrentUser(),
    retry: false,
    // Don't refetch on window focus for auth state
    refetchOnWindowFocus: false,
  })
}

// Hook for getting user details
export function useUserDetails() {
  return useQuery<UserDetails, AxiosError<ApiError>>({
    queryKey: authKeys.userDetails,
    queryFn: () => authService.getUserDetails(),
    retry: false,
    refetchOnWindowFocus: false,
  })
}

// Hook for login
export function useLogin() {
  const queryClient = useQueryClient()
  const router = useRouter()

  return useMutation<AuthResponse, AxiosError<ApiError>, LoginRequest>({
    mutationFn: (data) => authService.login(data),
    onSuccess: async (data) => {
      // Store tokens in cookies
      cookieUtils.setAuthTokens(
        data.access_token,
        data.refresh_token,
        data.token_type
      );

      // Update user data in React Query cache if available
      if (data.user) {
        queryClient.setQueryData(authKeys.user, data.user)
      }

      // Fetch user details and update cache
      try {
        const userDetails = await authService.getUserDetails()
        queryClient.setQueryData(authKeys.userDetails, userDetails)
        console.log('User details fetched successfully:', userDetails)
      } catch (error) {
        console.error('Failed to fetch user details:', error)
        // Show error toast
        toast.error("Failed to fetch user details", {
          description: error instanceof Error
            ? error.message
            : "An unexpected error occurred"
        })
      }

      // Check if there's a redirect parameter in the URL
      const searchParams = new URLSearchParams(window.location.search)
      const redirectPath = searchParams.get('redirect')

      // Redirect to the original path or dashboard
      if (redirectPath) {
        router.push(redirectPath)
      } else {
        router.push('/dashboard/overview')
      }
    },
  })
}

// Hook for signup
export function useSignup() {
  const router = useRouter()

  return useMutation<AuthResponse, AxiosError<ApiError>, SignupRequest>({
    mutationFn: (data) => authService.signup(data),
    onSuccess: () => {
      // Redirect to onboarding page after successful signup
      router.push('/onboarding')
    },
  })
}

// Hook for forgot password
export function useForgotPassword() {
  return useMutation<{ message: string }, AxiosError<ApiError>, ForgotPasswordRequest>({
    mutationFn: (data) => authService.forgotPassword(data),
  })
}

// Hook for reset password
export function useResetPassword() {
  const router = useRouter()

  return useMutation<{ message: string }, AxiosError<ApiError>, ResetPasswordRequest>({
    mutationFn: (data) => authService.resetPassword(data),
    onSuccess: () => {
      // Redirect to login page after successful password reset
      router.push('/auth/login')
    },
  })
}

// Hook for logout
export function useLogout() {
  const queryClient = useQueryClient()
  const router = useRouter()

  return useMutation({
    mutationFn: () => authService.logout(),
    onSuccess: () => {
      // Clear React Query cache
      queryClient.clear()

      // Redirect to home route
      router.push('/');
    },
    onError: (error) => {
      console.error("Logout error:", error);
      // Error is handled in the component that calls this hook
    }
  })
}

// Hook for GitHub OAuth
export function useConnectGitHub() {
  return useMutation<{ auth_url: string }, AxiosError<ApiError>, number>({
    mutationFn: (userId: number) => authService.connectGitHub(userId),
    onSuccess: (data) => {
      // Redirect to GitHub OAuth URL
      window.location.href = data.auth_url;
    },
  })
}
