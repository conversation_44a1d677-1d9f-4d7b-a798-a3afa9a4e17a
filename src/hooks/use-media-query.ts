"use client"

import { useState, useEffect } from "react"

export function useMediaQuery(query: string): boolean {
  // Initialize with a default value (false) for SSR
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== "undefined") {
      const media = window.matchMedia(query)

      // Update the state initially
      setMatches(media.matches)

      // Define a callback function to handle changes
      const listener = (event: MediaQueryListEvent) => {
        setMatches(event.matches)
      }

      // Add the listener
      media.addEventListener("change", listener)

      // Clean up
      return () => {
        media.removeEventListener("change", listener)
      }
    }

    // Return false for SSR
    return () => {}
  }, [query])

  return matches
}
