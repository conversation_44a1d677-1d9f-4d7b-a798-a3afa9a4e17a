import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  workflowService,
  type Workflow,
  type PaginatedResponse,
  type WorkflowResponse,
  type WorkflowVersionsResponse
} from '@/services/workflow-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'

// Query keys for workflows
export const workflowKeys = {
  all: ['workflows'] as const,
  details: (id: string) => ['workflows', id] as const,
  versions: (workflowId: string) => ['workflows', workflowId, 'versions'] as const,
}

// Define filter parameters type
export type WorkflowFilterParams = {
  page?: number;
  page_size?: number;
  category?: string;
  status?: string | string[];
  visibility?: string | string[];
  search?: string;
  tags?: string;
};

// Hook for fetching all workflows for the current user with optional filters
export function useUserWorkflows(filters?: WorkflowFilterParams) {
  return useQuery<PaginatedResponse<Workflow>, AxiosError<ApiError>>({
    queryKey: [...workflowKeys.all, filters],
    queryFn: () => workflowService.getUserWorkflows(filters),
    retry: 1,
  })
}

// Hook for fetching a single workflow
export function useWorkflow(id: string) {
  return useQuery<WorkflowResponse, AxiosError<ApiError>>({
    queryKey: workflowKeys.details(id),
    queryFn: () => workflowService.getWorkflowById(id),
    enabled: !!id, // Only run if id is provided
  })
}

// Hook for creating a new workflow
export function useCreateWorkflow() {
  const queryClient = useQueryClient()

  return useMutation<Workflow, AxiosError<ApiError>, Partial<Workflow>>({
    mutationFn: (data) => workflowService.createWorkflow(data),
    onSuccess: () => {
      // Invalidate the workflows list query to refetch after successful creation
      queryClient.invalidateQueries({ queryKey: workflowKeys.all })
    },
  })
}

// Hook for updating a workflow
export function useUpdateWorkflow() {
  const queryClient = useQueryClient()

  return useMutation<
    Workflow,
    AxiosError<ApiError>,
    { id: string; data: Partial<Workflow> }
  >({
    mutationFn: ({ id, data }) => workflowService.updateWorkflow(id, data),
    onSuccess: (data, variables) => {
      // Update both the list and the individual workflow in the cache
      queryClient.invalidateQueries({ queryKey: workflowKeys.all })
      queryClient.invalidateQueries({ queryKey: workflowKeys.details(variables.id) })
    },
  })
}

// Hook for deleting a workflow
export function useDeleteWorkflow() {
  const queryClient = useQueryClient()

  return useMutation<void, AxiosError<ApiError>, string>({
    mutationFn: (id) => workflowService.deleteWorkflow(id),
    onSuccess: () => {
      // Invalidate the workflows list query to refetch after successful deletion
      queryClient.invalidateQueries({ queryKey: workflowKeys.all })
    },
  })
}

// Hook for toggling workflow visibility
export function useToggleWorkflowVisibility() {
  const queryClient = useQueryClient()

  return useMutation<
    { success: boolean; message: string },
    AxiosError<ApiError>,
    string
  >({
    mutationFn: (id) => workflowService.toggleVisibility(id),
    onSuccess: (_, workflowId) => {
      // Invalidate both the list and the individual workflow in the cache
      queryClient.invalidateQueries({ queryKey: workflowKeys.all })
      queryClient.invalidateQueries({ queryKey: workflowKeys.details(workflowId) })
    },
  })
}

// Hook for fetching workflow versions
export function useWorkflowVersions(
  workflowId: string,
  params?: { page?: number; page_size?: number }
) {
  return useQuery<WorkflowVersionsResponse, AxiosError<ApiError>>({
    queryKey: [...workflowKeys.versions(workflowId), params],
    queryFn: () => workflowService.getWorkflowVersions(workflowId, params),
    enabled: !!workflowId, // Only run if workflowId is provided
  })
}
