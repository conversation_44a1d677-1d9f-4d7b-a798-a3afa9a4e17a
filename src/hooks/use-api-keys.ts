import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  apiKeysService,
  type ApiKey,
  type ApiKeysResponse,
  type CreateApiKeyRequest,
  type CreateApiKeyResponse
} from '@/services/api-keys-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'

// Query keys for API keys
export const apiKeysKeys = {
  all: ['api-keys'] as const,
  details: (id: string) => [...apiKeysKeys.all, id] as const,
}

// Hook for fetching all API keys
export function useApiKeys() {
  return useQuery<ApiKeysResponse, AxiosError<ApiError>>({
    queryKey: apiKeysKeys.all,
    queryFn: () => apiKeysService.getAll(),
    retry: 1,
  })
}

// Hook for fetching a single API key
export function useApiKey(id: string) {
  return useQuery<ApiKey, AxiosError<ApiError>>({
    queryKey: apiKeysKeys.details(id),
    queryFn: () => apiKeysService.getById(id),
    enabled: !!id, // Only run if id is provided
  })
}

// Hook for creating a new API key
export function useCreateApiKey() {
  const queryClient = useQueryClient()

  return useMutation<CreateApiKeyResponse, AxiosError<ApiError>, CreateApiKeyRequest>({
    mutationFn: (data) => apiKeysService.create(data),
    onSuccess: () => {
      // Invalidate the API keys list query to refetch after successful creation
      queryClient.invalidateQueries({ queryKey: apiKeysKeys.all })
    },
  })
}

// Hook for deleting an API key
export function useDeleteApiKey() {
  const queryClient = useQueryClient()

  return useMutation<{ success: boolean, message: string }, AxiosError<ApiError>, string>({
    mutationFn: (id) => apiKeysService.delete(id),
    onSuccess: () => {
      // Invalidate the API keys list query to refetch after successful deletion
      queryClient.invalidateQueries({ queryKey: apiKeysKeys.all })
    },
  })
}
