import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  agentService,
  type Agent,
  type PaginatedAgentResponse,
  type AgentResponse,
  type CreateAgentRequest,
  type AgentFilterParams,
  type AgentVersionsResponse
} from '@/services/agent-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'

// Query keys for agents
export const agentKeys = {
  all: ['agents'] as const,
  filtered: (filters?: AgentFilterParams) => [...agentKeys.all, 'filtered', filters] as const,
  details: (id: string) => [...agentKeys.all, id] as const,
  versions: (agentId: string) => [...agentKeys.all, agentId, 'versions'] as const,
}

// Hook for fetching all agents for the current user with optional filters
export function useUserAgents(filters?: AgentFilterParams) {
  return useQuery<PaginatedAgentResponse, AxiosError<ApiError>>({
    queryKey: agentKeys.filtered(filters),
    queryFn: () => agentService.getUserAgents(filters),
    retry: 1,
  })
}

// Hook for fetching a single agent
export function useAgent(id: string) {
  return useQuery<AgentResponse, AxiosError<ApiError>>({
    queryKey: agentKeys.details(id),
    queryFn: () => agentService.getAgentById(id),
    enabled: !!id, // Only run if id is provided
  })
}

// Hook for creating a new agent
export function useCreateAgent() {
  const queryClient = useQueryClient()

  return useMutation<Agent, AxiosError<ApiError>, CreateAgentRequest>({
    mutationFn: (data) => agentService.createAgent(data),
    onSuccess: () => {
      // Invalidate the agents list query to refetch after successful creation
      queryClient.invalidateQueries({ queryKey: agentKeys.all })
    },
  })
}

// Hook for deleting an agent
export function useDeleteAgent() {
  const queryClient = useQueryClient()

  return useMutation<void, AxiosError<ApiError>, string>({
    mutationFn: (id) => agentService.deleteAgent(id),
    onSuccess: () => {
      // Invalidate the agents list query to refetch after successful deletion
      queryClient.invalidateQueries({ queryKey: agentKeys.all })
    },
  })
}

// Hook for fetching agent versions
export function useAgentVersions(
  agentId: string,
  params?: { page?: number; page_size?: number }
) {
  return useQuery<AgentVersionsResponse, AxiosError<ApiError>>({
    queryKey: [...agentKeys.versions(agentId), params],
    queryFn: () => agentService.getAgentVersions(agentId, params),
    enabled: !!agentId, // Only run if agentId is provided
  })
}

// Hook for publishing agent changes (create version and publish to marketplace)
export function usePublishAgentChanges() {
  const queryClient = useQueryClient()

  return useMutation<
    { success: boolean; message: string },
    AxiosError<ApiError>,
    { agentId: string; publishToMarketplace: boolean }
  >({
    mutationFn: ({ agentId, publishToMarketplace }) =>
      agentService.createVersionAndPublish(agentId, publishToMarketplace),
    onSuccess: (data, variables) => {
      // Invalidate agent details and versions to refetch after publishing
      queryClient.invalidateQueries({ queryKey: agentKeys.details(variables.agentId) })
      queryClient.invalidateQueries({ queryKey: agentKeys.versions(variables.agentId) })
      // Optionally, invalidate the main list if marketplace status affects it
      queryClient.invalidateQueries({ queryKey: agentKeys.all })
    },
  })
}
