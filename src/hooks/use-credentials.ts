import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  credentialsService,
  type Credential,
  type CredentialsResponse,
  type CreateCredentialRequest,
  type CreateCredentialResponse
} from '@/services/api-keys-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'

// Query keys for credentials
export const credentialsKeys = {
  all: ['credentials'] as const,
  details: (id: string) => [...credentialsKeys.all, id] as const,
}

// Hook for fetching all credentials
export function useCredentials() {
  return useQuery<CredentialsResponse, AxiosError<ApiError>>({
    queryKey: credentialsKeys.all,
    queryFn: () => credentialsService.getAll(),
    retry: 1,
  })
}

// Hook for creating a new credential
export function useCreateCredential() {
  const queryClient = useQueryClient()

  return useMutation<CreateCredentialResponse, AxiosError<ApiError>, CreateCredentialRequest>({
    mutationFn: (data) => credentialsService.create(data),
    onSuccess: () => {
      // Invalidate the credentials list query to refetch after successful creation
      queryClient.invalidateQueries({ queryKey: credentialsKeys.all })
    },
  })
}

// Hook for deleting a credential
export function useDeleteCredential() {
  const queryClient = useQueryClient()

  return useMutation<{ success: boolean, message: string }, AxiosError<ApiError>, string>({
    mutationFn: (id) => credentialsService.delete(id),
    onSuccess: () => {
      // Invalidate the credentials list query to refetch after successful deletion
      queryClient.invalidateQueries({ queryKey: credentialsKeys.all })
    },
  })
}
