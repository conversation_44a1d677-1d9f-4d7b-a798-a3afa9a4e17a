import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  applicationService,
  type Application,
  type PaginatedApplicationsResponse,
  type ApplicationResponse,
  type CreateApplicationRequest,
  type UpdateApplicationRequest,
  type ApplicationFilterParams
} from '@/services/application-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'

// Query keys for applications
export const applicationKeys = {
  all: ['applications'] as const,
  filtered: (filters?: ApplicationFilterParams) => [...applicationKeys.all, 'filtered', filters] as const,
  details: (id: string) => [...applicationKeys.all, id] as const,
}

// Hook for fetching all applications for the current user with optional filters
export function useUserApplications(filters?: ApplicationFilterParams) {
  return useQuery<PaginatedApplicationsResponse, AxiosError<ApiError>>({
    queryKey: applicationKeys.filtered(filters),
    queryFn: () => applicationService.getUserApplications(filters),
    retry: 1,
  })
}

// Hook for fetching a single application
export function useApplication(id: string) {
  return useQuery<ApplicationResponse, AxiosError<ApiError>>({
    queryKey: applicationKeys.details(id),
    queryFn: () => applicationService.getApplicationById(id),
    enabled: !!id, // Only run if id is provided
  })
}

// Hook for creating a new application
export function useCreateApplication() {
  const queryClient = useQueryClient()

  return useMutation<Application, AxiosError<ApiError>, CreateApplicationRequest>({
    mutationFn: (data) => applicationService.createApplication(data),
    onSuccess: () => {
      // Invalidate the applications list query to refetch after successful creation
      queryClient.invalidateQueries({ queryKey: applicationKeys.all })
    },
  })
}

// Hook for updating an application
export function useUpdateApplication() {
    const queryClient = useQueryClient()

    return useMutation<Application, AxiosError<ApiError>, { id: string, data: Partial<UpdateApplicationRequest> }>({
        mutationFn: ({ id, data }) => applicationService.updateApplication(id, data),
        onSuccess: (updatedApplication) => {
            // Invalidate the specific application query and potentially the list query
            queryClient.invalidateQueries({ queryKey: applicationKeys.details(updatedApplication.id) });
            queryClient.invalidateQueries({ queryKey: applicationKeys.all });
        },
    });
}

// Hook for deleting an application
export function useDeleteApplication() {
  const queryClient = useQueryClient()

  return useMutation<void, AxiosError<ApiError>, string>({
    mutationFn: (id) => applicationService.deleteApplication(id),
    onSuccess: () => {
      // Invalidate the applications list query to refetch after successful deletion
      queryClient.invalidateQueries({ queryKey: applicationKeys.all })
    },
  })
} 