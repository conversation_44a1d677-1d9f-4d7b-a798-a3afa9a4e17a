import { useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { workflowJsonService, WorkflowJson } from '@/services/workflow-json-service';
import { ApiError } from './use-api';

// Query keys for workflow JSON
export const workflowJsonKeys = {
  all: ['workflow-json'] as const,
  details: (url: string) => [...workflowJsonKeys.all, url] as const,
};

// Hook for fetching workflow JSON from URL
export function useWorkflowJson(url: string | undefined) {
  return useQuery<WorkflowJson, AxiosError<ApiError>>({
    queryKey: workflowJsonKeys.details(url || ''),
    queryFn: () => workflowJsonService.getWorkflowJsonFromUrl(url || ''),
    enabled: !!url, // Only run if URL is provided
    retry: 1,
  });
}
