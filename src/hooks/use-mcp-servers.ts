import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  mcpService,
  type MCPServer,
  type PaginatedResponse,
  type MCPServerResponse,
  type CreateMCPServerRequest,
  type CreateMCPServerResponse,
  type MCPServerFilterParams,
  type UpdateMCPServerToolOutputSchemaRequest, // Added
  type UpdateMCPServerToolOutputSchemaResponse, // Added
  type ToggleMCPServerVisibilityResponse // Added
} from '@/services/mcp-service'
import { AxiosError } from 'axios'
import { ApiError } from './use-api'

// Query keys for MCP servers
export const mcpServerKeys = {
  all: ['mcp-servers'] as const,
  filtered: (filters?: MCPServerFilterParams) => [...mcpServerKeys.all, 'filtered', filters] as const,
  details: (id: string) => [...mcpServerKeys.all, id] as const,
}

// Hook for fetching all MCP servers with optional filters
export function useMCPServers(filters?: MCPServerFilterParams) {
  return useQuery<PaginatedResponse<MCPServer>, AxiosError<ApiError>>({
    queryKey: mcpServerKeys.filtered(filters),
    queryFn: () => mcpService.getMCPServers(filters),
    retry: 1,
  })
}

// Hook for fetching a single MCP server
export function useMCPServer(id: string) {
  return useQuery<MCPServerResponse, AxiosError<ApiError>>({
    queryKey: mcpServerKeys.details(id),
    queryFn: () => mcpService.getMCPServerById(id),
    enabled: !!id, // Only run if id is provided
  })
}

// Hook for creating a new MCP server
export function useCreateMCPServer() {
  const queryClient = useQueryClient()

  return useMutation<CreateMCPServerResponse, AxiosError<ApiError>, CreateMCPServerRequest>({
    mutationFn: (data) => mcpService.createMCPServer(data),
    onSuccess: () => {
      // Invalidate the MCP servers list query to refetch after successful creation
      queryClient.invalidateQueries({ queryKey: mcpServerKeys.all })
    },
  })
}

// Hook for updating an MCP server
export function useUpdateMCPServer() {
  const queryClient = useQueryClient()

  return useMutation<
    MCPServer,
    AxiosError<ApiError>,
    { id: string; data: CreateMCPServerRequest }
  >({
    mutationFn: ({ id, data }) => mcpService.updateMCPServer(id, data),
    onSuccess: (data, variables) => {
      // Update both the list and the individual MCP server in the cache
      queryClient.invalidateQueries({ queryKey: mcpServerKeys.all })
      queryClient.invalidateQueries({ queryKey: mcpServerKeys.details(variables.id) })
    },
  })
}

// Hook for updating an MCP server tool's output schema
export function useUpdateMCPServerToolOutputSchema() {
  const queryClient = useQueryClient()

  return useMutation<
    UpdateMCPServerToolOutputSchemaResponse,
    AxiosError<ApiError>,
    UpdateMCPServerToolOutputSchemaRequest
  >({
    mutationFn: (data) => mcpService.updateMCPServerToolOutputSchema(data),
    onSuccess: (data, variables) => {
      // Invalidate the specific MCP server details to refetch after successful update
      queryClient.invalidateQueries({ queryKey: mcpServerKeys.details(variables.mcp_id) })
    },
  })
}

// Hook for deleting an MCP server
export function useDeleteMCPServer() {
  const queryClient = useQueryClient()

  return useMutation<void, AxiosError<ApiError>, string>({
    mutationFn: (id) => mcpService.deleteMCPServer(id),
    onSuccess: () => {
      // Invalidate the MCP servers list query to refetch after successful deletion
      queryClient.invalidateQueries({ queryKey: mcpServerKeys.all })
    },
  })
}

// Hook for toggling MCP server visibility
export function useToggleMCPServerVisibility() {
  const queryClient = useQueryClient();

  return useMutation<ToggleMCPServerVisibilityResponse, AxiosError<ApiError>, string>({
    mutationFn: (mcp_id) => mcpService.toggleMCPServerVisibility(mcp_id),
    onSuccess: (data, mcp_id) => {
      queryClient.invalidateQueries({ queryKey: mcpServerKeys.details(mcp_id) });
      queryClient.invalidateQueries({ queryKey: mcpServerKeys.all }); // Invalidate all to update visibility in lists
    },
  });
}
