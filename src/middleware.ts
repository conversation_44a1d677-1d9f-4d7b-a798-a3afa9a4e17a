import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { COOKIE_NAMES } from './lib/cookie';

// Define public paths that don't require authentication
const publicPaths = [
  '/auth/login',
  '/auth/signup',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/onboarding',
  // Add any other public paths here
];

// Function to check if the path is public
const isPublicPath = (path: string) => {
  return publicPaths.some(publicPath => path.startsWith(publicPath));
};

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  console.log('Pathname:', pathname);

  // Get the full URL (including query parameters)
  const fullUrl = request.url;
  console.log('Full URL:', fullUrl);

  // Check if the path is a dashboard path
  const isDashboardPath = pathname.startsWith('/dashboard');

  // Check if the path is the onboarding path
  const isOnboardingPath = pathname.startsWith('/onboarding');

  // Get the access token from cookies
  const token = request.cookies.get(COOKIE_NAMES.ACCESS_TOKEN)?.value;

  // If it's a dashboard path and there's no token, redirect to home route
  if (isDashboardPath && !token) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Allow access to onboarding regardless of auth status
  if (isOnboardingPath) {
    return NextResponse.next();
  }

  // If it's a public path (like login) and there is a token, redirect to dashboard
  if (isPublicPath(pathname) && token && !isOnboardingPath) {
    return NextResponse.redirect(new URL('/dashboard/overview', request.url));
  }

  // For the root path, redirect to dashboard if authenticated
  // The home page itself will handle the login button click
  if (pathname === '/' && token) {
    return NextResponse.redirect(new URL('/dashboard/overview', request.url));
  }

  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  // Match all request paths except for the ones starting with:
  // - _next/static (static files)
  // - _next/image (image optimization files)
  // - favicon.ico (favicon file)
  // - public folder
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|logo.svg|.*\\.png$|.*\\.jpg$).*)',
  ],
};
