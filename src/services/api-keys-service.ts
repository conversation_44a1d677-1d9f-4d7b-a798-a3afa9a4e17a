import axiosClient from '@/lib/axios-client'

// Define types for API key data (legacy)
export type ApiKey = {
  id: string
  name: string
  public_key: string
  private_key: string
  project: string
  created_at: string
}

export type ApiKeysResponse = {
  success: boolean
  message: string
  api_keys: ApiKey[]
}

export type CreateApiKeyRequest = {
  name: string
  project: string
}

export type CreateApiKeyResponse = {
  success: boolean
  message: string
  public_key: string
  private_key: string
}

// Define types for credentials data (new API structure)
export type Credential = {
  id: string
  key_name: string
  description: string | null
  value: string
  created_at: string
  updated_at: string
  last_used_at: string
}

export type CredentialsResponse = {
  success: boolean
  message: string
  credentials: Credential[]
}

export type CreateCredentialRequest = {
  key_name: string
  value: string
}

export type CreateCredentialResponse = {
  success: boolean
  message: string
  credential: Credential
}

// API Keys service with methods for different operations (legacy)
export const apiKeysService = {
  // Get all API keys
  getAll: async (): Promise<ApiKeysResponse> => {
    const response = await axiosClient.get<ApiKeysResponse>('/api-keys/api-keys')
    console.log('API Keys response:', response.data)
    return response.data
  },

  // Get a single API key by ID
  getById: async (id: string): Promise<ApiKey> => {
    const response = await axiosClient.get<{ data: ApiKey }>(`/api-keys/${id}`)
    return response.data.data
  },

  // Create a new API key
  create: async (data: CreateApiKeyRequest): Promise<CreateApiKeyResponse> => {
    const response = await axiosClient.post<CreateApiKeyResponse>('/api-keys/add-key', data)
    console.log('Create API key response:', response.data)
    return response.data
  },

  // Delete an API key
  delete: async (id: string): Promise<{ success: boolean, message: string }> => {
    const response = await axiosClient.delete<{ success: boolean, message: string }>(`/api-keys/${id}`)
    console.log('Delete API key response:', response.data)
    return response.data
  }
}

// Credentials service with methods for different operations (new API structure)
export const credentialsService = {
  // Get all credentials
  getAll: async (): Promise<CredentialsResponse> => {
    const response = await axiosClient.get<CredentialsResponse>('/credentials')
    console.log('Credentials response:', response.data)
    return response.data
  },

  // Create a new credential
  create: async (data: CreateCredentialRequest): Promise<CreateCredentialResponse> => {
    const response = await axiosClient.post<CreateCredentialResponse>('/credentials', data)
    console.log('Create credential response:', response.data)
    return response.data
  },

  // Delete a credential
  delete: async (id: string): Promise<{ success: boolean, message: string }> => {
    const response = await axiosClient.delete<{ success: boolean, message: string }>(`/credentials/${id}`)
    console.log('Delete credential response:', response.data)
    return response.data
  }
}
