import axiosClient from "@/lib/axios-client";

// Define types for workflow data based on the API response
export type Agent = {
  id: string;
  name: string;
  type: string;
  description?: string;
};

export type Tool = {
  id: string;
  name: string;
  type: string;
  description?: string;
};

export type Node = {
  id: string;
  name: string;
  type: string;
  description?: string;
};

export type Trigger = {
  id: string;
  name: string;
  type: string;
  value?: string;
  description?: string;
};

export type Workflow = {
  id: string;
  name: string;
  description: string | null;
  workflow_url: string;
  builder_url: string;
  start_nodes: string[];
  owner_id: string;
  user_ids: string[];
  owner_type: string;
  workflow_template_id: string | null;
  template_owner_id: string | null;
  url: string | null;
  is_imported: boolean;
  version: string;
  visibility: string;
  category: string | null;
  tags: Record<string, unknown> | null;
  status: string;
  is_changes_marketplace: boolean;
  created_at: string;
  updated_at: string;
  is_customizable?: boolean;
  is_updated: boolean;
  // Optional properties for UI
  tools?: Tool[];
  agents?: Agent[];
  triggers?: Trigger[];
  available_nodes: Node[];
};

// Define the pagination metadata type
export type PaginationMetadata = {
  total: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

// Define the paginated response type
export type PaginatedResponse<T> = {
  data: T[];
  metadata: PaginationMetadata;
};

// Define the single workflow response type
export type WorkflowResponse = {
  success: boolean;
  message: string;
  workflow: Workflow;
};

// Define types for workflow version data
export type WorkflowVersion = {
  id: string;
  workflow_id: string;
  version_number: string;
  name: string;
  description: string;
  workflow_url: string;
  builder_url: string;
  start_nodes: Array<Record<string, unknown>>; // Assuming start_nodes is an array of objects
  category: string;
  tags: string[];
  changelog: string;
  status: string;
  is_customizable: boolean;
  created_at: string;
  is_current: boolean;
};

export type WorkflowVersionsResponse = {
  success: boolean;
  message: string;
  versions: WorkflowVersion[];
  total: number;
  page: number;
  total_pages: number;
  current_version_id: string;
};

// No sample workflow data - using real API data

// Workflows service with methods for different operations
export const workflowService = {
  // Get all workflows for the current user with optional filters
  getUserWorkflows: async (params?: {
    page?: number;
    page_size?: number;
    category?: string;
    status?: string | string[];
    visibility?: string | string[];
    search?: string;
    tags?: string;
  }): Promise<PaginatedResponse<Workflow>> => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add pagination parameters
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.page_size)
        queryParams.append("page_size", params.page_size.toString());

      // Add filter parameters
      if (params?.category) queryParams.append("category", params.category);

      // Handle multiple status values
      if (params?.status) {
        if (Array.isArray(params.status)) {
          params.status.forEach((status) =>
            queryParams.append("status", status)
          );
        } else {
          queryParams.append("status", params.status);
        }
      }

      // Handle multiple visibility values
      if (params?.visibility) {
        if (Array.isArray(params.visibility)) {
          params.visibility.forEach((visibility) =>
            queryParams.append("visibility", visibility)
          );
        } else {
          queryParams.append("visibility", params.visibility);
        }
      }

      // Add search parameter
      if (params?.search) queryParams.append("search", params.search);

      // Add tags parameter
      if (params?.tags) queryParams.append("tags", params.tags);

      // Build the URL with query parameters
      const url = `/workflows${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await axiosClient.get<PaginatedResponse<Workflow>>(url);
      console.log("Workflow API response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching workflows:", error);
      throw error;
    }
  },

  // Get a single workflow by ID
  getWorkflowById: async (id: string): Promise<WorkflowResponse> => {
    try {
      const response = await axiosClient.get<WorkflowResponse>(
        `/workflows/${id}`
      );
      console.log("Workflow details response:", response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching workflow with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new workflow
  createWorkflow: async (data: Partial<Workflow>): Promise<Workflow> => {
    try {
      const response = await axiosClient.post<Workflow>("/workflows", data);
      console.log("Create workflow response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error creating workflow:", error);
      throw error;
    }
  },

  // Update a workflow
  updateWorkflow: async (
    id: string,
    data: Partial<Workflow>
  ): Promise<Workflow> => {
    try {
      const response = await axiosClient.put<Workflow>(
        `/workflows/${id}`,
        data
      );
      console.log("Update workflow response:", response.data);
      return response.data;
    } catch (error) {
      console.error(`Error updating workflow with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete a workflow
  deleteWorkflow: async (id: string): Promise<void> => {
    try {
      await axiosClient.delete(`/workflows/${id}`);
      console.log(`Deleted workflow with ID: ${id}`);
    } catch (error) {
      console.error(`Error deleting workflow with ID ${id}:`, error);
      throw error;
    }
  },

  // Run a workflow
  runWorkflow: async (
    id: string,
    input: Record<string, unknown>
  ): Promise<Record<string, unknown>> => {
    try {
      const response = await axiosClient.post(`/workflows/${id}/run`, input);
      console.log(`Running workflow with ID: ${id}, response:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error running workflow with ID ${id}:`, error);
      throw error;
    }
  },

  // Toggle workflow visibility
  toggleVisibility: async (
    id: string
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axiosClient.post<{
        success: boolean;
        message: string;
      }>(`/workflows/${id}/toggle-visibility`, {});
      console.log("Toggle workflow visibility response:", response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error toggling visibility for workflow ${id}:`, error);
      if (error && typeof error === "object" && "response" in error) {
        const axiosError = error as {
          response: { data: unknown; status: number };
        };
        console.error("Error response data:", axiosError.response.data);
        console.error("Error response status:", axiosError.response.status);
      }
      throw error;
    }
  },

  // Create a new version for a workflow and optionally publish to marketplace
  createVersionAndPublish: async (
    workflowId: string,
    publishToMarketplace: boolean = false
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const queryParams = new URLSearchParams();
      if (publishToMarketplace) {
        queryParams.append("publish_to_marketplace", "true");
      }

      const url = `/workflows/${workflowId}/create-version-and-publish${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await axiosClient.post<{
        success: boolean;
        message: string;
      }>(url, {});

      console.log("Create version and publish response:", response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(
        `Error creating version and publishing workflow ${workflowId}:`,
        error
      );
      if (error && typeof error === "object" && "response" in error) {
        const axiosError = error as {
          response: { data: unknown; status: number };
        };
        console.error("Error response data:", axiosError.response.data);
        console.error("Error response status:", axiosError.response.status);
      }
      throw error;
    }
  },

  // Get all versions for a workflow
  getWorkflowVersions: async (
    workflowId: string,
    params?: {
      page?: number;
      page_size?: number;
    }
  ): Promise<WorkflowVersionsResponse> => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.page_size)
        queryParams.append("page_size", params.page_size.toString());

      const url = `/workflows/${workflowId}/versions${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await axiosClient.get<WorkflowVersionsResponse>(url);
      console.log(
        `Workflow versions for ID ${workflowId} response:`,
        response.data
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching versions for workflow with ID ${workflowId}:`,
        error
      );
      throw error;
    }
  },
};
