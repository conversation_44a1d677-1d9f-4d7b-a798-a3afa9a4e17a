import apiClient from '@/lib/axios-client';
import { ActivitiesResponse, ActivityLogsResponse, ActivityEventsResponse, Activity as ApiActivity, ActivityLog, ActivityEvent } from '@/shared/interfaces';

interface GetActivitiesParams {
  page?: number;
  page_size?: number;
  // Add other filter parameters here if needed in the future
  // e.g., activity_type?: string;
  // e.g., resource_id?: string;
}

interface GetLogsParams {
  page?: number;
  page_size?: number;
}

interface GetEventsParams {
  page?: number;
  page_size?: number;
}

const activityService = {
  getActivities: async (params?: GetActivitiesParams): Promise<ActivitiesResponse> => {
    try {
      const response = await apiClient.get<ActivitiesResponse>('/activities', { params });
      console.log('response', response.data);
      return response.data;
    } catch (error) {
      // console.error('Error fetching activities:', error);
      // It's often better to let react-query handle error states,
      // but re-throwing or returning a specific error structure can be done here.
      throw error;
    }
  },

  getLogs: async (params?: GetLogsParams): Promise<ActivityLogsResponse> => {
    try {
      const response = await apiClient.get<ActivityLogsResponse>('/activities/logs/list', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getEvents: async (params?: GetEventsParams): Promise<ActivityEventsResponse> => {
    try {
      const response = await apiClient.get<ActivityEventsResponse>('/activities/events/list', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getActivityById: async (id: string): Promise<ApiActivity> => {
    try {
      const response = await apiClient.get<{ activity: ApiActivity; logs: ActivityLog[]; events: ActivityEvent[] }>(`/activities/${id}`);
      return response.data.activity;
    } catch (error) {
      throw error;
    }
  },
};

export default activityService;