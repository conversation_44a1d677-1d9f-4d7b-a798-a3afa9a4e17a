import axiosClient from "@/lib/axios-client";

// Define types for prompt optimization API
export type OptimizePromptRequest = {
  original_prompt: string;
  agent_context: {
    title: string;
    description: string;
  };
};

export type OptimizePromptResponse = {
  original_prompt: string;
  improved_prompt: string;
};

// Utility function to convert markdown prompt to plain text
export const convertMarkdownToText = (markdownText: string): string => {
  if (!markdownText) return '';

  let text = markdownText;

  // Remove markdown code blocks
  text = text.replace(/```markdown\s*\n?/g, '');
  text = text.replace(/```\s*$/g, '');

  // Convert headers to plain text with proper spacing
  text = text.replace(/^#{1,6}\s+(.+)$/gm, '$1\n');

  // Convert bold/italic to plain text
  text = text.replace(/\*\*(.+?)\*\*/g, '$1');
  text = text.replace(/\*(.+?)\*/g, '$1');
  text = text.replace(/__(.+?)__/g, '$1');
  text = text.replace(/_(.+?)_/g, '$1');

  // Convert lists to plain text with proper indentation
  text = text.replace(/^\s*[-*+]\s+/gm, '• ');
  text = text.replace(/^\s*\d+\.\s+/gm, (match, offset, string) => {
    const lineStart = string.lastIndexOf('\n', offset) + 1;
    const beforeMatch = string.substring(lineStart, offset);
    const indent = beforeMatch.length;
    return `${indent > 0 ? '  ' : ''}${match.replace(/^\s*\d+\.\s+/, '• ')}`;
  });

  // Clean up extra whitespace and normalize line breaks
  text = text.replace(/\n{3,}/g, '\n\n');
  text = text.trim();

  return text;
};

// Prompt service with methods for different operations
export const promptService = {
  // Optimize a prompt using AI
  optimizePrompt: async (data: OptimizePromptRequest): Promise<OptimizePromptResponse> => {
    try {
      console.log('Making API call to /prompts/improve with data:', data);
      const response = await axiosClient.post<OptimizePromptResponse>(
        '/prompts/improve',
        data
      );
      console.log('Optimize prompt response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error optimizing prompt:', error);
      throw error;
    }
  },
};
