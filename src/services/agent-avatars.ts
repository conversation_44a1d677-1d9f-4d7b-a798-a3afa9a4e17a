import axiosClient from "@/lib/axios-client";

export interface AgentAvatar {
  url: string;
  id: string;
  created_at: string;
  updated_at: string;
}

export interface AvatarsResponse {
  avatars: AgentAvatar[];
  metadata: {
    total: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const fetchAgentAvatars = async (page = 1, pageSize = 10): Promise<AvatarsResponse> => {
  try {
    const response = await axiosClient.get(`/agent-avatars?page=${page}&pageSize=${pageSize}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching agent avatars:", error);
    throw error;
  }
};
