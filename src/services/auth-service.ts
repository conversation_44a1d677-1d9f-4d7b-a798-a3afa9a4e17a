import axiosClient from "@/lib/axios-client";
import { cookieUtils } from "@/lib/cookie";
import { toast } from "sonner";

// Define types for authentication
export type LoginRequest = {
  email: string;
  password: string;
};

export type SignupRequest = {
  name: string;
  email: string;
  password: string;
};

export type ForgotPasswordRequest = {
  email: string;
};

export type ResetPasswordRequest = {
  token: string;
  password: string;
  password_confirmation: string;
};

export type AuthResponse = {
  access_token: string;
  refresh_token: string;
  token_type: string;
  message: string;
  success: boolean;
  user?: {
    id: number;
    name: string;
    email: string;
  };
};

export type UserDetails = {
  id: number;
  email: string;
  fullName: string;
  company: string;
  department: string;
  jobRole: string;
  profileImage?: string;
  github_access_token?: string | null;
};

export type AccessTokenResponse = {
  success: boolean;
  access_token: string;
  token_type: string;
  tokenExpireAt: string;
};

// Auth service with methods for different operations
export const authService = {
  // Login user
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    const response = await axiosClient.post<AuthResponse>("/auth/login", {
      ...data,
      fcm_token: "12345",
    });
    console.log(response.data);
    return response.data;
  },

  // Register new user
  signup: async (data: SignupRequest): Promise<AuthResponse> => {
    const response = await axiosClient.post<AuthResponse>(
      "/auth/register",
      data
    );
    return response.data;
  },

  // Request password reset
  forgotPassword: async (
    data: ForgotPasswordRequest
  ): Promise<{ message: string }> => {
    const response = await axiosClient.post<{ message: string }>(
      "/api/auth/forgot-password",
      data
    );
    return response.data;
  },

  // Reset password with token
  resetPassword: async (
    data: ResetPasswordRequest
  ): Promise<{ message: string }> => {
    const response = await axiosClient.post<{ message: string }>(
      "/api/auth/reset-password",
      data
    );
    return response.data;
  },

  // Logout user - currently just clearing cookies without API call
  logout: async (): Promise<void> => {
    // Skip API call for now
    // await axiosClient.post('/api/auth/logout')

    // Clear all cookies on logout
    cookieUtils.clearAllOnLogout();
    console.log("Logged out: Cleared all cookies");
  },

  // Get current user
  getCurrentUser: async () => {
    const response = await axiosClient.get("/api/auth/user");
    return response.data;
  },

  // Get user details
  getUserDetails: async (): Promise<UserDetails> => {
    try {
      const response = await axiosClient.get<UserDetails>("/users/me");
      console.log("user dertails: ", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching user details:", error);

      // Show error in toast notification
      toast.error("Failed to fetch user details", {
        description: error instanceof Error
          ? error.message
          : "An unexpected error occurred"
      });

      // Re-throw the error so it can be handled by the calling component
      throw error;
    }
  },

  // GitHub OAuth - Initiate GitHub authentication
  connectGitHub: async (userId: number): Promise<{ auth_url: string }> => {
    try {
      const response = await axiosClient.get<{ auth_url: string }>("/auth/github/", {
        params: { user_id: userId }
      });
      return response.data;
    } catch (error) {
      console.error("Error initiating GitHub OAuth:", error);
      toast.error("Failed to connect to GitHub", {
        description: error instanceof Error
          ? error.message
          : "An unexpected error occurred"
      });
      throw error;
    }
  },

  // Generate new access token using refresh token
  generateAccessToken: async (refreshToken: string): Promise<AccessTokenResponse> => {
    try {
      const response = await axiosClient.get<AccessTokenResponse>("/auth/access-token", {
        params: {
          refresh_token: refreshToken
        }
      });
      return response.data;
    } catch (error) {
      console.error("Error generating access token:", error);
      throw error;
    }
  },
};
