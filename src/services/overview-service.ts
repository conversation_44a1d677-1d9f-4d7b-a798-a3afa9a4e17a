import axiosClient from "@/lib/axios-client";

export type CreditBreakdown = {
  additionalProp1: number;
  additionalProp2: number;
  additionalProp3: number;
};

export type AppCreditUsage = {
  timestamp: string;
  value: number;
};

export type AgentPerformance = {
  timestamp: string;
  requests: number;
  completion_rate: number;
};

export type RecentEvent = {
  type: string;
  endpoint: string;
  status: string;
  timestamp: string;
  duration: string;
  user: string;
};

export type OverviewData = {
  user_id: string;
  active_agents: number;
  credit_usage: number;
  agent_requests: number;
  workflow_requests: number;
  custom_mcps: number;
  credit_breakdown: any;
  app_credit_usage: any;
  agent_performance: any;
  recent_events: any;
};

export type OverviewResponse = {
  success: boolean;
  message: string;
  overview: OverviewData;
};

export const overviewService = {
  getOverview: async (userId: string): Promise<OverviewResponse> => {
    const response = await axiosClient.get<OverviewResponse>("/analytics/dashboard/overview", {
      params: { user_id: userId },
    });
    return response.data;
  },
}; 