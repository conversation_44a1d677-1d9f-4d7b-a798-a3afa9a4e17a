import axios from "axios";

// Define types for workflow JSON data
export interface WorkflowTool {
  tool_id: number;
  tool_name: string;
  input_schema?: Record<string, any>;
  output_schema?: Record<string, any>;
}

export interface WorkflowNode {
  id: string;
  server_script_path: string;
  server_tools: WorkflowTool[];
}

export interface WorkflowTransition {
  id: string;
  source_id: string;
  target_id: string;
  condition?: string;
}

export interface WorkflowJson {
  nodes: WorkflowNode[];
  transitions: WorkflowTransition[];
}

// Service to fetch workflow JSON from URL
export const workflowJsonService = {
  // Get workflow JSON from URL
  getWorkflowJsonFromUrl: async (url: string): Promise<WorkflowJson> => {
    try {
      // Use axios directly (not axiosClient) as specified
      const response = await axios.get<WorkflowJson>(url);
      console.log('Workflow JSON response:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching workflow JSON from URL ${url}:`, error);
      throw error;
    }
  },
};
