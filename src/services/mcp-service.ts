import axiosClient from "@/lib/axios-client";

// Define types for MCP tool schema
export type MCPToolInputSchema = {
  type: string;
  properties: Record<string, {
    type: string;
    description: string;
  }>;
  required: string[];
};

export type MCPTool = {
  name: string;
  description: string;
  input_schema: MCPToolInputSchema;
  annotations: Record<string, unknown> | null;
};

export type MCPToolsConfig = {
  meta: Record<string, unknown> | null;
  nextCursor: string | number | null;
  tools: MCPTool[];
};

// Define types for MCP server URLs
export type MCPServerConfig = {
  url: string;
  type: "sse" | "streamable-http" | "stdio";
};

// Define types for MCP server data
export type MCPServer = {
  id: string;
  name: string;
  logo: string | null;
  description: string | null;
  owner_id: string;
  user_ids: string[] | null;
  owner_type: string;
  config: MCPServerConfig[] | null;
  git_url: string | null;
  git_branch: string | null;
  deployment_status: string;
  visibility: string;
  tags: string[] | null;
  status: "active" | "inactive";
  mcp_type?: "sse" | "streamable-http" | "stdio" | null;
  created_at: string;
  updated_at: string;
  category: string;
  mcp_tools_config: MCPToolsConfig;
  is_added:boolean;
  env_keys?: Array<{
    key: string;
    description?: string;
  }>;
  component_category?: string | null;
};

// Define the pagination metadata type
export type PaginationMetadata = {
  total: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

// Define the paginated response type
export type PaginatedResponse<T> = {
  data: T[];
  metadata: PaginationMetadata;
};

// Define the single MCP server response type
export type MCPServerResponse = {
  success: boolean;
  message: string;
  mcp: MCPServer;
};

// Define types for MCP server creation
export type CreateMCPServerRequest = {
  logo?: string;
  name: string;
  description?: string;
  git_url?: string;
  git_branch?: string;
  config?: MCPServerConfig[];
  github_access_token?: string;
  category: "general" | "sales" | "marketing" | "engineering" | "finance" | "hr";
  visibility: "private" | "public";
  tags?: string[];
  status: "active" | "inactive";
  user_ids?: string[];
  mcp_type?: "sse" | "streamable-http" | "stdio";
  env_keys?: Array<{
    key: string;
    description?: string;
  }>;
};

export type CreateMCPServerResponse = {
  success: boolean;
  message: string;
  mcp_id: string;
};

// Define types for updating MCP tool output schema
export type UpdateMCPServerToolOutputSchemaRequest = {
  mcp_id: string;
  tool_name: string;
  output_schema_json: object; // Assuming the schema is a generic JSON object
};

export type UpdateMCPServerToolOutputSchemaResponse = {
  success: boolean;
  message: string;
  // Potentially return the updated tool or MCP server details if the API does so
};


// Define filter parameters type for MCP servers
export type MCPServerFilterParams = {
  page?: number;
  page_size?: number;
  department?: string;
  visibility?: string;
  status?: string;
  url_type?: string;
  search?: string;
  deployment_status?: string;
};

// Define types for toggling MCP server visibility
export type ToggleMCPServerVisibilityResponse = {
  success: boolean;
  message: string;
  visibility: "public" | "private"; // The new visibility status
};

// MCP servers service with methods for different operations
export const mcpService = {
  // Get all MCP servers with optional filters
  getMCPServers: async (params?: MCPServerFilterParams): Promise<PaginatedResponse<MCPServer>> => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add pagination parameters
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

      // Add filter parameters
      if (params?.department) queryParams.append('department', params.department);
      if (params?.visibility) queryParams.append('visibility', params.visibility);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.url_type) queryParams.append('url_type', params.url_type);
      if (params?.search) queryParams.append('search', params.search);
      if (params?.deployment_status) queryParams.append('deployment_status', params.deployment_status);

      // Build the URL with query parameters
      const url = `/mcps${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      console.log('Fetching MCP servers with URL:', url);

      const response = await axiosClient.get<PaginatedResponse<MCPServer>>(url);
      console.log('MCP Servers API response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error('Error fetching MCP servers:', error);
      throw error;
    }
  },

  // Get a single MCP server by ID
  getMCPServerById: async (id: string): Promise<MCPServerResponse> => {
    try {
      const response = await axiosClient.get<MCPServerResponse>(`/mcps/${id}`);
      console.log('MCP Server details response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error fetching MCP server with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new MCP server
  createMCPServer: async (data: CreateMCPServerRequest): Promise<CreateMCPServerResponse> => {
    try {
      const response = await axiosClient.post<CreateMCPServerResponse>('/mcps', data);
      console.log('Create MCP server response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error('Error creating MCP server:', error);
      throw error;
    }
  },

  // Update an MCP server
  updateMCPServer: async (id: string, data: CreateMCPServerRequest): Promise<MCPServer> => {
    try {
      const response = await axiosClient.patch<MCPServer>(`/mcps/${id}`, data);
      console.log('Update MCP server response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error(`Error updating MCP server with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete an MCP server
  deleteMCPServer: async (id: string): Promise<void> => {
    try {
      await axiosClient.delete(`/mcps/${id}`);
      console.log(`Deleted MCP server with ID: ${id}`);
    } catch (error: unknown) {
      console.error(`Error deleting MCP server with ID ${id}:`, error);
      throw error;
    }
  },

  // Update an MCP server tool's output schema
  updateMCPServerToolOutputSchema: async (
    data: UpdateMCPServerToolOutputSchemaRequest
  ): Promise<UpdateMCPServerToolOutputSchemaResponse> => {
    try {
      const response = await axiosClient.put<UpdateMCPServerToolOutputSchemaResponse>(
        '/mcps/tool/output-schema',
        data
      );
      console.log('Update MCP server tool output schema response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error('Error updating MCP server tool output schema:', error);
      throw error;
    }
  },

  // Toggle MCP server visibility
  toggleMCPServerVisibility: async (
    mcp_id: string
  ): Promise<ToggleMCPServerVisibilityResponse> => {
    try {
      const response = await axiosClient.post<ToggleMCPServerVisibilityResponse>(
        `/mcps/${mcp_id}/toggle-visibility`
      );
      console.log('Toggle MCP server visibility response:', response.data);
      return response.data;
    } catch (error: unknown) {
      console.error('Error toggling MCP server visibility:', error);
      throw error;
    }
  },
};
