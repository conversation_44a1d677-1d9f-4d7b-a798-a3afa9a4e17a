import axiosClient from "@/lib/axios-client";

// Define types for Application data
export type Application = {
  id: string;
  user_id: string;
  name: string;
  description: string;
  workflow_ids: string[];
  agent_ids: string[];
  status: string;
  created_at: string;
  updated_at: string;
  api_keys: string[];
  is_deleted: boolean;
};

// Define types for Application creation request
export type CreateApplicationRequest = {
  user_id: string;
  name: string;
  description: string;
};

// Define types for Application update request
export type UpdateApplicationRequest = {
  name?: string;
  description?: string;
  workflow_ids?: string[];
  agent_ids?: string[];
  status?: string;
};

// Define the pagination metadata type
export type PaginationMetadata = {
  total: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

// Define the paginated response type for listing applications
export type PaginatedApplicationsResponse = {
  data: Application[];
  metadata: PaginationMetadata;
};

// Define the single application response type for getting details
export type ApplicationResponse = {
  success: boolean;
  message: string;
  application: Application;
  metrics?: {
    application_id: string;
    total_requests: number;
    successful_requests: number;
    failed_requests: number;
    credits_used: number;
    last_request_at: string;
    usage_trend: Array<{
      date: string;
      count: number;
    }>;
  };
};

// Define the response type for create and update operations (can be reused if structure is same)
export type ApplicationMutationResponse = {
    success: boolean;
    message: string;
    application: Application;
}

// Define filter parameters type for listing applications
export type ApplicationFilterParams = {
  page?: number;
  page_size?: number;
  search?: string;
};

// Applications service with methods for different operations
export const applicationService = {
  // Get all applications with optional filters
  getUserApplications: async (params?: ApplicationFilterParams): Promise<PaginatedApplicationsResponse> => {
    try {
      const queryParams = new URLSearchParams();

      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.page_size) queryParams.append('page_size', params.page_size.toString());
      if (params?.search) queryParams.append('search', params.search);

      const url = `/applications${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      const response = await axiosClient.get<PaginatedApplicationsResponse>(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching applications:', error);
      throw error;
    }
  },

  // Get a single application by ID
  getApplicationById: async (id: string): Promise<ApplicationResponse> => {
    try {
      const response = await axiosClient.get<ApplicationResponse>(`/applications/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching application with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new application
  createApplication: async (data: CreateApplicationRequest): Promise<Application> => {
    try {
      const response = await axiosClient.post<ApplicationMutationResponse>('/applications', data);
      return response.data.application;
    } catch (error) {
      console.error('Error creating application:', error);
      throw error;
    }
  },

  // Update an application
  updateApplication: async (
    id: string,
    data: Partial<UpdateApplicationRequest>
  ): Promise<Application> => {
    try {
      const response = await axiosClient.put<ApplicationMutationResponse>(`/applications/${id}`, data);
      return response.data.application;
    } catch (error) {
      console.error(`Error updating application with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete an application
  deleteApplication: async (id: string): Promise<void> => {
    try {
      await axiosClient.delete<{success: boolean, message: string}>(`/applications/${id}`);
    } catch (error) {
      console.error(`Error deleting application with ID ${id}:`, error);
      throw error;
    }
  },
}; 