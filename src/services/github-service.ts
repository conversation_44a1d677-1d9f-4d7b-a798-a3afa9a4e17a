import axiosClient from "@/lib/axios-client";
import { toast } from "sonner";

// GitHub repository types
export type GitHubRepository = {
  id: number;
  name: string;
  full_name: string;
  private: boolean;
  html_url: string;
  description: string | null;
  default_branch: string;
  owner: {
    login: string;
    id: number;
    avatar_url: string;
    html_url: string;
  };
  language: string | null;
  stargazers_count: number;
  forks_count: number;
  updated_at: string;
};

export type GitHubRepositoriesResponse = {
  success: boolean;
  message: string;
  repositories: GitHubRepository[];
  metadata: {
    total_items: number;
    total_pages: number;
    current_page: number;
    page_size: number;
    has_next_page: boolean;
    has_previous_page: boolean;
  };
};

// GitHub branch types
export type GitHubBranch = {
  name: string;
};

export type GitHubBranchesResponse = {
  success: boolean;
  message: string;
  repository_full_name: string;
  owner_login: string;
  branches: GitHubBranch[];
};

// GitHub user types
export type GitHubUser = {
  login: string;
  id: number;
  node_id: string;
  avatar_url: string;
  gravatar_id: string;
  url: string;
  html_url: string;
  followers_url: string;
  following_url: string;
  gists_url: string;
  starred_url: string;
  subscriptions_url: string;
  organizations_url: string;
  repos_url: string;
  events_url: string;
  received_events_url: string;
  type: string;
  user_view_type: string;
  site_admin: boolean;
  name: string | null;
  company: string | null;
  blog: string;
  location: string | null;
  email: string | null;
  hireable: boolean | null;
  bio: string | null;
  twitter_username: string | null;
  notification_email: string | null;
  public_repos: number;
  public_gists: number;
  followers: number;
  following: number;
  created_at: string;
  updated_at: string;
  private_gists: number;
  total_private_repos: number;
  owned_private_repos: number;
  disk_usage: number;
  collaborators: number;
  two_factor_authentication: boolean;
  plan: {
    name: string;
    space: number;
    collaborators: number;
    private_repos: number;
  };
};

// GitHub repository query parameters
export type GitHubRepoParams = {
  type?: "all" | "owner" | "member";
  sort?: "created" | "updated" | "pushed" | "full_name";
  direction?: "asc" | "desc";
  search?: string;
  page?: number;
  page_size?: number;
};

// GitHub service
export const githubService = {
  // Get user repositories
  getUserRepositories: async (params: GitHubRepoParams = {}): Promise<GitHubRepositoriesResponse> => {
    try {
      const queryParams = {
        type: params.type || "all",
        sort: params.sort || "updated",
        direction: params.direction || "desc",
        search: params.search,
        page: params.page || 1,
        page_size: params.page_size || 50,
      };

      // Remove undefined values
      const cleanParams = Object.fromEntries(
        Object.entries(queryParams).filter(([_, value]) => value !== undefined)
      );

      const response = await axiosClient.get<GitHubRepositoriesResponse>("/auth/github/user/repos", {
        params: cleanParams
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching GitHub repositories:", error);
      toast.error("Failed to fetch GitHub repositories", {
        description: error instanceof Error
          ? error.message
          : "An unexpected error occurred"
      });
      throw error;
    }
  },

  // Get repository branches
  getRepositoryBranches: async (repoName: string): Promise<GitHubBranchesResponse> => {
    try {
      const response = await axiosClient.get<GitHubBranchesResponse>(`/auth/github/repos/${repoName}/branches`);
      return response.data;
    } catch (error) {
      console.error("Error fetching repository branches:", error);
      toast.error("Failed to fetch repository branches", {
        description: error instanceof Error
          ? error.message
          : "An unexpected error occurred"
      });
      throw error;
    }
  },

  // Get GitHub user details
  getGitHubUser: async (): Promise<GitHubUser> => {
    try {
      const response = await axiosClient.get<GitHubUser>("/auth/github/user/me");
      return response.data;
    } catch (error) {
      console.error("Error fetching GitHub user details:", error);
      toast.error("Failed to fetch GitHub user details", {
        description: error instanceof Error
          ? error.message
          : "An unexpected error occurred"
      });
      throw error;
    }
  },
};
