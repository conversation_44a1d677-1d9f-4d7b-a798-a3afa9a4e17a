# RUH Developer Frontend Code Structure

This document outlines the code structure for the RUH Developer Frontend project.

## Directory Structure

```
/src
  /app                   # Next.js App Router pages
  /components            # Reusable UI components
    /common              # Common components used across the app
      /layout            # Layout components (header, sidebar, etc.)
      /data-display      # Components for displaying data (tables, charts, etc.)
      /feedback          # Feedback components (alerts, toasts, etc.)
    /ui                  # UI components (from shadcn/ui)
    /providers           # Context providers
  /features              # Feature-specific code
    /agents              # Agent-related code
      /components        # Agent-specific components
      /hooks             # Agent-specific hooks
      /utils             # Agent-specific utilities
      /types             # Agent-specific types
    /workflows           # Workflow-related code
      /components        # Workflow-specific components
      /hooks             # Workflow-specific hooks
      /utils             # Workflow-specific utilities
      /types             # Workflow-specific types
  /lib                   # Shared utilities and helpers
    /api                 # API client functions
    /hooks               # Custom hooks
    /utils               # Utility functions
    /types               # TypeScript types and interfaces
    /constants           # Constants and configuration
  /services              # Service layer for API communication
  /store                 # Global state management
```

## Import Conventions

- Use absolute imports with the `@/` prefix
- Import from index files when available
- Group imports by:
  1. React and Next.js imports
  2. Third-party libraries
  3. Internal components and utilities
  4. Styles

Example:
```tsx
// React and Next.js imports
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

// Third-party libraries
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';

// Internal components and utilities
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/hooks/use-auth';
import { api } from '@/lib/api';

// Styles (if any)
import styles from './Component.module.css';
```

## Component Structure

Components should follow these conventions:

1. Use TypeScript for all components
2. Use named exports
3. Use the function component syntax
4. Use the "use client" directive for components that use client-side features
5. Group related components in the same directory
6. Create index files for exporting components

## Feature Organization

Features are organized by domain (agents, workflows, etc.) and contain:

1. Components specific to that feature
2. Hooks specific to that feature
3. Utilities specific to that feature
4. Types specific to that feature

This organization makes it easier to find and maintain code related to a specific feature.
