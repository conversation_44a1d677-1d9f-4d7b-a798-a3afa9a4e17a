# API and State Management in Developer Platform

This document provides an overview of how API calls and state management are implemented in the Developer Platform project.

## Table of Contents

1. [API Client (Axios)](#api-client-axios)
2. [Data Fetching (React Query)](#data-fetching-react-query)
3. [State Management (Zustand)](#state-management-zustand)
4. [Usage Examples](#usage-examples)

## API Client (Axios)

We use Axios for making HTTP requests to our backend API. The base configuration is set up in `src/lib/axios-client.ts`.

### Key Features

- Base URL configuration
- Default headers
- Request/response interceptors
- Authentication token handling
- Error handling

### Basic Usage

```typescript
import axiosClient from '@/lib/axios-client';

// GET request
const getData = async () => {
  const response = await axiosClient.get('/endpoint');
  return response.data;
};

// POST request
const createData = async (data) => {
  const response = await axiosClient.post('/endpoint', data);
  return response.data;
};
```

## Data Fetching (React Query)

React Query is used for data fetching, caching, and state management of asynchronous data. It's configured in `src/lib/providers/query-provider.tsx`.

### Key Features

- Automatic caching
- Background refetching
- Loading and error states
- Pagination and infinite scrolling
- Optimistic updates

### Basic Usage

We've created custom hooks in `src/hooks/use-api.ts` to simplify working with React Query:

```typescript
import { useApiGet, useApiPost } from '@/hooks/use-api';

// In a component
function MyComponent() {
  // Fetch data
  const { data, isLoading, error } = useApiGet('/endpoint', ['endpoint']);
  
  // Mutation
  const { mutate, isPending } = useApiPost('/endpoint');
  
  const handleSubmit = (formData) => {
    mutate(formData, {
      onSuccess: (data) => {
        console.log('Success:', data);
      },
      onError: (error) => {
        console.error('Error:', error);
      }
    });
  };
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    // Component JSX
  );
}
```

## State Management (Zustand)

Zustand is used for global UI state management. The main store is defined in `src/store/use-ui-store.ts`.

### Key Features

- Simple API with minimal boilerplate
- Immutable updates
- Middleware support (persist, devtools)
- TypeScript support

### Basic Usage

```typescript
import { useUIStore } from '@/store/use-ui-store';

function MyComponent() {
  // Access state
  const { sidebarOpen, theme } = useUIStore();
  
  // Update state
  const { setSidebarOpen, toggleSidebar, setTheme } = useUIStore();
  
  return (
    <div>
      <button onClick={toggleSidebar}>
        Toggle Sidebar
      </button>
      <button onClick={() => setTheme('dark')}>
        Dark Mode
      </button>
    </div>
  );
}
```

## Usage Examples

### Example 1: API Keys Page

The API Keys page (`src/app/dashboard/api-keys/page.tsx`) demonstrates how to use React Query with Axios for data fetching and mutations:

```typescript
// Custom hooks for API keys
const { data: apiKeys, isLoading, error } = useApiKeys();
const createApiKey = useCreateApiKey();
const deleteApiKey = useDeleteApiKey();

// Create a new API key
const handleCreateApiKey = () => {
  createApiKey.mutate(
    { name: "New API Key", project: "Project Name" },
    {
      onSuccess: () => {
        // Handle success
      },
      onError: (error) => {
        // Handle error
      },
    }
  );
};
```

### Example 2: Sidebar Component

The Sidebar component (`src/components/sidebar.tsx`) demonstrates how to use Zustand for UI state:

```typescript
// Access state from Zustand store
const { sidebarOpen, toggleSidebar } = useUIStore();

// Use the state in the component
return (
  <div className={sidebarOpen ? "sidebar-open" : "sidebar-closed"}>
    <button onClick={toggleSidebar}>Toggle</button>
    {/* Sidebar content */}
  </div>
);
```

### Example 3: Theme Toggle

The Theme Toggle component (`src/components/theme-toggle.tsx`) shows how to sync Zustand state with other libraries:

```typescript
// Access state from Zustand store
const { theme, setTheme } = useUIStore();
// Access next-themes API
const { setTheme: setNextTheme } = useTheme();

// Toggle theme and sync with next-themes
const toggleTheme = () => {
  const newTheme = theme === "light" ? "dark" : "light";
  setTheme(newTheme);
  setNextTheme(newTheme);
};
```
