-Core Design Principles:
Simplicity Over Complexity: Choose the simplest solution that meets requirements
Maintainability First: Prioritize code that's easy to maintain and extend
Scalability By Design: Architecture should scale without major rewrites
Developer Experience: Optimize for developer productivity and satisfaction
Cost Efficiency: Balance technical excellence with reasonable operational costs

-Technology Selection Guidelines:
Prefer established technologies with strong community support
Choose tools with comprehensive documentation and learning resources
Select technologies that align with the team's existing skills when possible
Avoid bleeding-edge technologies for critical system components
Consider long-term support and ecosystem health

-Architecture Requirements:
All components must have clear boundaries and well-defined interfaces
Data flows must be explicitly documented with direction and payload types
Every design decision must include an explanation of alternatives considered
Security must be addressed at each architectural layer
Performance considerations must be documented for each component

-Diagram Requirements:

1.Mandatory Diagram Types:
System Context Diagram showing the system and its external dependencies
Container Diagram depicting high-level technical components
Component Diagrams for each significant container
Data Flow Diagrams showing how information moves through the system
Sequence Diagrams for all critical user journeys and system operations
State Transition Diagrams for components with complex state management
Deployment Diagrams showing runtime infrastructure and deployment patterns

2.Diagram Quality Standards:
All diagrams must follow consistent notation (preferably C4 model where applicable)
Each diagram must include a clear title, legend, and version information
Diagrams must be maintained in a format that supports version control
Every architectural element in diagrams must have a corresponding explanation
Relationships between different diagrams must be clearly documented

3.Flow Visualization Requirements
User flows must be visualized with appropriate journey/process diagrams
API interactions must be documented with sequence or collaboration diagrams
Event flows must be visualized showing producers, consumers, and payloads
Data transformations must be illustrated across system boundaries
Error and exception flows must be documented separately from happy paths

-Documentation Standards:
All diagrams must have accompanying explanations
Use consistent naming conventions across all documentation
Document assumptions explicitly and prominently
Include rationale for key architectural decisions
Provide context for how components interact with each other

-Operational Considerations:
Design for observability from the start (logging, metrics, tracing)
Include error handling strategies for each component
Document backup and disaster recovery approaches
Consider deployment complexity and CI/CD pipeline requirements
Address infrastructure-as-code principles where applicable

-Mandatory Approval Gates:
Clarification questions must be approved before proceeding with architecture design
High-level architecture must be approved before detailed component design
Technology stack recommendations require approval before specific implementation details
All required diagrams must be completed and reviewed before implementation begins

-Deliverable Quality Checklist:
[ ] All diagrams are clear, consistent, and properly labeled
[ ] Technical decisions include justifications and trade-offs
[ ] Security considerations are addressed at each layer
[ ] Scalability approaches are documented
[ ] Cost implications of architectural choices are explained
[ ] Maintenance considerations are documented
[ ] Complete set of required diagrams is provided
[ ] All system and data flows are visually documented
