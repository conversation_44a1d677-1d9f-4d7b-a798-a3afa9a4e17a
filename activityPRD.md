# Activity Page Product Requirements Document (PRD)

## 1. Overview

This document outlines the features and functionality of the Platform Activity page, as implemented in [`src/app/dashboard/activity/page.tsx`](src/app/dashboard/activity/page.tsx:1). The Activity page serves as a centralized location for users to monitor various activities occurring across the platform, including API requests (logs) and system events.

## 2. Key Components and UI Elements

The Activity page is built using Next.js, React, and TypeScript, leveraging several UI components for tables, selects, buttons, inputs, badges, dialogs, and tabs.

- **Main View:** Displays a filterable and searchable table of activity items.
- **Tabs:** Allows users to switch between different views of activity:
    - All Activity
    - Logs
    - Events
- **Filtering:** Provides options to filter data based on type, status, and search queries.
- **Details Modal:** Shows detailed information for a selected log or event item.

## 3. Tab Functionality

### 3.1. All Activity Tab

- **Purpose:** Displays a consolidated view of all logs and events, sorted by timestamp (most recent first).
- **Data Displayed:** Combines data from both logs and events.
- **Filters:**
    - **Search:** By ID (log or event) or resource ID (event). The search also implicitly covers log endpoint.
    - **Event Type Dropdown:** Filters by specific event types (e.g., "Workflow Completed", "API Request Success"). This filter applies to event items when "All Activity" is selected.
    - **Status Dropdown:** Filters by status. For logs, this means success (2xx) or error (4xx/5xx). For events, this means "delivered" or "failed". When "All Activity" is selected, this filter applies to both logs and events based on their respective status definitions.

### 3.2. Logs Tab

- **Purpose:** Displays only API request logs.
- **Data Displayed:** Shows details such as timestamp, HTTP method, endpoint, status code, duration, and request/response ID.
- **Filters:**
    - **Search:** By ID (request ID) or endpoint.
    - **Status Dropdown:**
        - All Status
        - Success (2xx)
        - Error (4xx/5xx)
- **Details View:** Clicking a log entry opens a modal displaying:
    - Timestamp
    - Duration
    - Method
    - Status Code
    - Endpoint
    - Request (JSON)
    - Response (JSON)

### 3.3. Events Tab

- **Purpose:** Displays system-generated events.
- **Data Displayed:** Shows details such as timestamp, event type, status, resource ID, and event ID.
- **Filters:**
    - **Search:** By ID (event ID) or resource ID.
    - **Event Type Dropdown:**
        - All Events
        - Workflow Completed
        - Workflow Started
        - API Request Success
        - API Request Error
    - **Status Dropdown:**
        - All Status
        - Delivered
        - Failed
- **Details View:** Clicking an event entry opens a modal displaying:
    - Timestamp
    - Event Type
    - Status
    - Resource ID
    - Event Payload (JSON)

### 3.4. Agents Tab (Note)

- **Note:** The provided file [`src/app/dashboard/activity/page.tsx`](src/app/dashboard/activity/page.tsx:1) does not currently implement an "Agents" tab within the activity monitoring section. The existing tabs are "All Activity", "Logs", and "Events". If an "Agents" tab is a requirement, it would need to be added.

## 4. Data Structures and Interfaces

The frontend defines the following interfaces for activity items:

### 4.1. `LogItem` Interface (from [`src/app/dashboard/activity/page.tsx`](src/app/dashboard/activity/page.tsx:34))
```typescript
interface LogItem {
  id: string; // Request ID
  timestamp: string;
  method: string; // e.g., GET, POST
  endpoint: string; // API endpoint
  statusCode: number; // e.g., 200, 401, 500
  duration: string;
  type: "log";
  request: Record<string, unknown>; // JSON request body
  response: Record<string, unknown>; // JSON response body
}
```

### 4.2. `EventItem` Interface (from [`src/app/dashboard/activity/page.tsx`](src/app/dashboard/activity/page.tsx:46))
```typescript
interface EventItem {
  id: string; // Event ID
  timestamp: string;
  type: "event";
  eventType: string; // e.g., workflow.completed, api.request.error
  status: string; // e.g., delivered, failed
  resourceId: string; // ID of the related resource (e.g., workflow run ID)
  payload: Record<string, unknown>; // JSON event payload
}
```

### 4.3. `ActivityItem` Type (from [`src/app/dashboard/activity/page.tsx`](src/app/dashboard/activity/page.tsx:57))
A union type combining `LogItem` and `EventItem`.
```typescript
type ActivityItem = LogItem | EventItem;
```

## 5. Backend Table Schema (Postgres - As per user specification)

The following schema is specified for the backend Postgres database. Note that the frontend interfaces (`LogItem`, `EventItem`) are structured to consume data that would align with or be derived from such a backend schema.

**Base Table (Conceptual - for common fields):**
- `id`: SERIAL PRIMARY KEY (or appropriate unique ID type)
- `created_at`: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- `event_source_type`: ENUM ('workflow', 'agent', 'api_request', etc.) - *User specified 'event: enum (workflow, agent)', this is an expansion to cover logs too.*
- `resource_id`: VARCHAR(255) (or appropriate type for various resource IDs)

**Logs Table (Derived from user's "Additional fields: Logs"):**
- `log_id`: SERIAL PRIMARY KEY (links to a base activity ID or is the primary ID)
- `activity_id` (FK to a common activity table if one exists)
- `timestamp`: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP (corresponds to `created_at` or is separate)
- `http_method`: VARCHAR(10) (e.g., 'GET', 'POST' - corresponds to `LogItem.method` and user's `Events.Type` for API events)
- `endpoint`: VARCHAR(255) (corresponds to `LogItem.endpoint`)
- `status_code`: INTEGER (corresponds to `LogItem.statusCode` and user's `Events.Status` for API events)
- `duration_ms`: INTEGER (corresponds to `LogItem.duration` after parsing)
- `request_payload`: JSONB (corresponds to `LogItem.request`)
- `response_payload`: JSONB (corresponds to `LogItem.response`)
- `status`: ENUM('success', 'failed') (derived from `status_code`, corresponds to user's `Logs.Status`)
- `log_type`: ENUM('workflow_started', 'workflow_completed', 'api_success', 'api_error') (corresponds to user's `Logs.Type`) - *This seems to overlap with `EventItem.eventType` and might be better suited for an events table or a unified activity type field.*

**Events Table (Derived from user's "Additional fields: Events" and `EventItem`):**
- `event_id`: SERIAL PRIMARY KEY (links to a base activity ID or is the primary ID)
- `activity_id` (FK to a common activity table if one exists)
- `timestamp`: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP (corresponds to `EventItem.timestamp`)
- `event_type_name`: VARCHAR(255) (e.g., 'workflow.completed', 'api.request.success' - corresponds to `EventItem.eventType` and user's `Logs.Type`)
- `delivery_status`: ENUM('delivered', 'failed', 'pending') (corresponds to `EventItem.status` and user's `Logs.Status`)
- `related_resource_id`: VARCHAR(255) (corresponds to `EventItem.resourceId`)
- `payload_data`: JSONB (corresponds to `EventItem.payload` and user's `Events.Payload`)
- `api_event_http_method`: ENUM('GET', 'POST', 'PUT', 'DELETE') (corresponds to user's `Events.Type` for API events)
- `api_event_status_code`: INTEGER (corresponds to user's `Events.Status` for API events, e.g., 200, 401, 500)


**Consolidated/Unified Activity Table (Recommended Approach):**

A more robust backend might use a single `Activities` table with a type discriminator and JSONB fields for specific data, or related tables for logs and events.

Example Unified `Activities` Table:
- `id`: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- `timestamp`: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- `activity_type`: ENUM('API_LOG', 'WORKFLOW_EVENT', 'AGENT_EVENT', etc.)
- `source_identifier`: VARCHAR(255) (e.g., request ID for logs, event ID for events)
- `resource_id`: VARCHAR(255) (Optional, for related entities like workflow run ID)
- `status`: VARCHAR(50) (e.g., 'SUCCESS', 'FAILED', 'DELIVERED', '200', '401')
- `details`: JSONB (Stores specific data like request/response for logs, or payload for events)
    - For Logs: `{ "method": "POST", "endpoint": "/api/xyz", "statusCode": 200, "durationMs": 120, "request": {...}, "response": {...} }`
    - For Events: `{ "eventType": "workflow.completed", "payload": {...} }`

This unified approach simplifies querying for "All Activity" and allows flexibility.

## 6. Filtering Logic (as per [`src/app/dashboard/activity/page.tsx`](src/app/dashboard/activity/page.tsx:264))

- **Tab-based filtering:**
    - If `activeTab` is "logs", only items with `type: "log"` are shown.
    - If `activeTab` is "events", only items with `type: "event"` are shown.
- **Log Filtering:**
    - **Status:** `statusFilter` ("all", "success" for `statusCode < 400`, "error" for `statusCode >= 400`).
    - **Search:** `searchQuery` matches `endpoint` (case-insensitive) or `id` (case-insensitive).
- **Event Filtering:**
    - **Type:** `typeFilter` ("all" or matches `eventType`).
    - **Status:** `statusFilter` ("all" or matches `status`).
    - **Search:** `searchQuery` matches `id` (case-insensitive) or `resourceId` (case-insensitive).
- **"All Activity" Tab Filtering:**
    - When the "All Activity" tab is active, the filters for event type and status are available.
    - The `typeFilter` (for event types) will apply to items of type "event".
    - The `statusFilter` will apply to both "log" and "event" items, respecting their individual status definitions (HTTP status codes for logs, "delivered"/"failed" for events).
    - The search query applies as defined for logs and events respectively.

## 7. Future Considerations / Potential Enhancements

- Add an "Agents" tab if required, with relevant data and filters.
- More granular filtering options (e.g., date range).
- Real-time updates to the activity feed.
- Export functionality for logs/events.